"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/modular-tokens/[address]/page",{

/***/ "(app-pages-browser)/./src/app/modular-tokens/[address]/page.tsx":
/*!***************************************************!*\
  !*** ./src/app/modular-tokens/[address]/page.tsx ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ModularTokenDetailsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/contract/contract.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/utils/units.js\");\n/* harmony import */ var _hooks_useModularToken__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../hooks/useModularToken */ \"(app-pages-browser)/./src/hooks/useModularToken.ts\");\n/* harmony import */ var _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../contracts/SecurityTokenCore.json */ \"(app-pages-browser)/./src/contracts/SecurityTokenCore.json\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n// Import custom hook\n\n// Import ABI\n\n// Extract ABI from artifact\nconst SecurityTokenCoreABI = _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_4__.abi;\nfunction ModularTokenDetailsPage() {\n    var _tokenInfo_metadata, _tokenInfo_metadata1, _tokenInfo_metadata2, _tokenInfo_metadata3, _tokenInfo_metadata4;\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const tokenAddress = params.address;\n    // Use the custom hook for modular token functionality\n    const { provider, signer, tokenInfo, upgradeInfo, pendingUpgrades, upgradeHistory, loading, error, initializeProvider, mintTokens, togglePause, scheduleUpgrade, toggleEmergencyMode, refreshData, setError, updateTokenPrice, updateBonusTiers, updateMaxSupply, addToWhitelist, removeFromWhitelist } = (0,_hooks_useModularToken__WEBPACK_IMPORTED_MODULE_3__.useModularToken)(tokenAddress); // Pass the token address to the hook\n    // Local state for UI\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [mintAmount, setMintAmount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [mintAddress, setMintAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [upgradeDescription, setUpgradeDescription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [newImplementationAddress, setNewImplementationAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [actionLoading, setActionLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('overview');\n    // KYC & Claims state\n    const [kycUserAddress, setKycUserAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [claimUserAddress, setClaimUserAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [claimTopicId, setClaimTopicId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('10101010000001');\n    const [claimData, setClaimData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [checkUserAddress, setCheckUserAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [verificationStatus, setVerificationStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Admin Controls form states\n    const [newTokenPrice, setNewTokenPrice] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [newBonusTiers, setNewBonusTiers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [newMaxSupply, setNewMaxSupply] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [whitelistAddress, setWhitelistAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Whitelist management state\n    const [whitelistedAddresses, setWhitelistedAddresses] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loadingWhitelist, setLoadingWhitelist] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedAddress, setSelectedAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [operationAmount, setOperationAmount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [transferToAddress, setTransferToAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [vaultBalance, setVaultBalance] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('0');\n    // Clear success message after 5 seconds\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect({\n        \"ModularTokenDetailsPage.useEffect\": ()=>{\n            if (success) {\n                const timer = setTimeout({\n                    \"ModularTokenDetailsPage.useEffect.timer\": ()=>setSuccess(null)\n                }[\"ModularTokenDetailsPage.useEffect.timer\"], 5000);\n                return ({\n                    \"ModularTokenDetailsPage.useEffect\": ()=>clearTimeout(timer)\n                })[\"ModularTokenDetailsPage.useEffect\"];\n            }\n        }\n    }[\"ModularTokenDetailsPage.useEffect\"], [\n        success\n    ]);\n    // Clear error message after 10 seconds\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect({\n        \"ModularTokenDetailsPage.useEffect\": ()=>{\n            if (error) {\n                const timer = setTimeout({\n                    \"ModularTokenDetailsPage.useEffect.timer\": ()=>setError(null)\n                }[\"ModularTokenDetailsPage.useEffect.timer\"], 10000);\n                return ({\n                    \"ModularTokenDetailsPage.useEffect\": ()=>clearTimeout(timer)\n                })[\"ModularTokenDetailsPage.useEffect\"];\n            }\n        }\n    }[\"ModularTokenDetailsPage.useEffect\"], [\n        error,\n        setError\n    ]);\n    // Load whitelist when tab is selected\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect({\n        \"ModularTokenDetailsPage.useEffect\": ()=>{\n            if (activeTab === 'whitelist' && provider && tokenAddress) {\n                fetchWhitelistedAddresses();\n            }\n        }\n    }[\"ModularTokenDetailsPage.useEffect\"], [\n        activeTab,\n        provider,\n        tokenAddress\n    ]);\n    const handleMint = async ()=>{\n        if (!mintAddress || !mintAmount) return;\n        setActionLoading(true);\n        setError(null);\n        setSuccess(null);\n        try {\n            await mintTokens(mintAddress, mintAmount);\n            setSuccess(\"Successfully minted \".concat(mintAmount, \" tokens to \").concat(mintAddress));\n            setMintAmount('');\n            setMintAddress('');\n            await refreshData(); // Refresh token info\n        } catch (error) {\n            console.error('Error minting tokens:', error);\n            setError(\"Failed to mint tokens: \".concat(error.message));\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handleMintAPI = async ()=>{\n        if (!mintAddress || !mintAmount) return;\n        setActionLoading(true);\n        setError(null);\n        setSuccess(null);\n        try {\n            const response = await fetch('/api/admin/mint-tokens', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    tokenAddress: tokenAddress,\n                    toAddress: mintAddress,\n                    amount: mintAmount\n                })\n            });\n            const result = await response.json();\n            if (!response.ok) {\n                throw new Error(result.error || 'API request failed');\n            }\n            setSuccess(\"API Mint: \".concat(result.message, \" (Tx: \").concat(result.txHash, \")\"));\n            setMintAddress('');\n            setMintAmount('');\n            await refreshData();\n        } catch (error) {\n            console.error('Error with API mint:', error);\n            setError(\"API mint failed: \".concat(error.message));\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handlePauseToggle = async ()=>{\n        setActionLoading(true);\n        setError(null);\n        setSuccess(null);\n        try {\n            await togglePause();\n            setSuccess(\"Token \".concat((tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.paused) ? 'unpaused' : 'paused', \" successfully\"));\n            await refreshData(); // Refresh token info\n        } catch (error) {\n            console.error('Error toggling pause:', error);\n            setError(\"Failed to \".concat((tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.paused) ? 'unpause' : 'pause', \" token: \").concat(error.message));\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handlePauseToggleAPI = async ()=>{\n        setActionLoading(true);\n        setError(null);\n        setSuccess(null);\n        try {\n            const action = (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.paused) ? 'unpause' : 'pause';\n            const response = await fetch('/api/admin/toggle-pause', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    tokenAddress: tokenAddress,\n                    action: action\n                })\n            });\n            const result = await response.json();\n            if (!response.ok) {\n                throw new Error(result.error || 'API request failed');\n            }\n            if (result.alreadyInDesiredState) {\n                setSuccess(result.message);\n            } else {\n                setSuccess(\"\".concat(result.message, \" (Tx: \").concat(result.transactionHash, \")\"));\n            }\n            await refreshData(); // Refresh token info\n        } catch (error) {\n            console.error('Error with API pause toggle:', error);\n            setError(\"API \".concat((tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.paused) ? 'unpause' : 'pause', \" failed: \").concat(error.message));\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handleScheduleUpgrade = async ()=>{\n        if (!newImplementationAddress || !upgradeDescription) return;\n        setActionLoading(true);\n        setError(null);\n        setSuccess(null);\n        try {\n            await scheduleUpgrade(newImplementationAddress, upgradeDescription);\n            setSuccess('Upgrade scheduled successfully! It will be executable after the 48-hour timelock.');\n            setNewImplementationAddress('');\n            setUpgradeDescription('');\n            await refreshData(); // Refresh upgrade info\n        } catch (error) {\n            console.error('Error scheduling upgrade:', error);\n            setError(\"Failed to schedule upgrade: \".concat(error.message));\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handleEmergencyModeToggle = async ()=>{\n        setActionLoading(true);\n        setError(null);\n        setSuccess(null);\n        try {\n            await toggleEmergencyMode();\n            setSuccess(\"Emergency mode \".concat((upgradeInfo === null || upgradeInfo === void 0 ? void 0 : upgradeInfo.emergencyModeActive) ? 'deactivated' : 'activated', \" successfully\"));\n            await refreshData(); // Refresh upgrade info\n        } catch (error) {\n            console.error('Error toggling emergency mode:', error);\n            setError(\"Failed to \".concat((upgradeInfo === null || upgradeInfo === void 0 ? void 0 : upgradeInfo.emergencyModeActive) ? 'deactivate' : 'activate', \" emergency mode: \").concat(error.message));\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    // Admin Controls Handlers\n    const handleUpdatePrice = async ()=>{\n        if (!newTokenPrice) return;\n        setActionLoading(true);\n        try {\n            await updateTokenPrice(newTokenPrice);\n            setSuccess(\"Token price updated to \".concat(newTokenPrice));\n            setNewTokenPrice('');\n            await refreshData();\n        } catch (err) {\n            setError(err.message);\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handleUpdateBonusTiers = async ()=>{\n        if (!newBonusTiers) return;\n        setActionLoading(true);\n        try {\n            await updateBonusTiers(newBonusTiers);\n            setSuccess(\"Bonus tiers updated to: \".concat(newBonusTiers));\n            setNewBonusTiers('');\n            await refreshData();\n        } catch (err) {\n            setError(err.message);\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handleUpdateMaxSupply = async ()=>{\n        if (!newMaxSupply) return;\n        setActionLoading(true);\n        try {\n            await updateMaxSupply(newMaxSupply);\n            setSuccess(\"Max supply updated to \".concat(newMaxSupply));\n            setNewMaxSupply('');\n            await refreshData();\n        } catch (err) {\n            setError(err.message);\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handleAddToWhitelistAdmin = async ()=>{\n        if (!whitelistAddress) return;\n        setActionLoading(true);\n        try {\n            await addToWhitelist(whitelistAddress);\n            setSuccess(\"Address \".concat(whitelistAddress, \" added to whitelist\"));\n            setWhitelistAddress('');\n        } catch (err) {\n            setError(err.message);\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handleRemoveFromWhitelist = async ()=>{\n        if (!whitelistAddress) return;\n        setActionLoading(true);\n        try {\n            await removeFromWhitelist(whitelistAddress);\n            setSuccess(\"Address \".concat(whitelistAddress, \" removed from whitelist\"));\n            setWhitelistAddress('');\n        } catch (err) {\n            setError(err.message);\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    // KYC & Claims handlers\n    const handleApproveKYC = async ()=>{\n        if (!kycUserAddress) return;\n        setActionLoading(true);\n        setError(null);\n        setSuccess(null);\n        try {\n            // Call the KYC approval API\n            const response = await fetch('/api/kyc/approve', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    tokenAddress: tokenAddress,\n                    userAddress: kycUserAddress\n                })\n            });\n            if (!response.ok) {\n                throw new Error('Failed to approve KYC');\n            }\n            setSuccess(\"KYC approved successfully for \".concat(kycUserAddress));\n            setKycUserAddress('');\n        } catch (error) {\n            console.error('Error approving KYC:', error);\n            setError(\"Failed to approve KYC: \".concat(error.message));\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handleAddToWhitelistKYC = async ()=>{\n        if (!kycUserAddress) return;\n        setActionLoading(true);\n        setError(null);\n        setSuccess(null);\n        try {\n            // Call the whitelist API\n            const response = await fetch('/api/kyc/whitelist', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    tokenAddress: tokenAddress,\n                    userAddress: kycUserAddress\n                })\n            });\n            if (!response.ok) {\n                throw new Error('Failed to add to whitelist');\n            }\n            setSuccess(\"User added to whitelist successfully: \".concat(kycUserAddress));\n            setKycUserAddress('');\n        } catch (error) {\n            console.error('Error adding to whitelist:', error);\n            setError(\"Failed to add to whitelist: \".concat(error.message));\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handleIssueClaim = async ()=>{\n        if (!claimUserAddress || !claimTopicId) return;\n        setActionLoading(true);\n        setError(null);\n        setSuccess(null);\n        try {\n            // Call the claims API\n            const response = await fetch('/api/kyc/issue-claim', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    userAddress: claimUserAddress,\n                    topicId: claimTopicId,\n                    data: claimData || ''\n                })\n            });\n            if (!response.ok) {\n                throw new Error('Failed to issue claim');\n            }\n            const result = await response.json();\n            setSuccess(\"Claim issued successfully! Claim ID: \".concat(result.claimId));\n            setClaimUserAddress('');\n            setClaimData('');\n        } catch (error) {\n            console.error('Error issuing claim:', error);\n            setError(\"Failed to issue claim: \".concat(error.message));\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handleCheckStatus = async ()=>{\n        if (!checkUserAddress) return;\n        setActionLoading(true);\n        setError(null);\n        try {\n            // Call the status check API\n            const response = await fetch(\"/api/kyc/status?tokenAddress=\".concat(tokenAddress, \"&userAddress=\").concat(checkUserAddress));\n            if (!response.ok) {\n                throw new Error('Failed to check status');\n            }\n            const status = await response.json();\n            setVerificationStatus(status);\n        } catch (error) {\n            console.error('Error checking status:', error);\n            setError(\"Failed to check status: \".concat(error.message));\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handleWhitelistAPI = async (action)=>{\n        if (!whitelistAddress) return;\n        setActionLoading(true);\n        setError(null);\n        setSuccess(null);\n        try {\n            const response = await fetch('/api/admin/manage-whitelist', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    tokenAddress: tokenAddress,\n                    address: whitelistAddress,\n                    action: action\n                })\n            });\n            const result = await response.json();\n            if (!response.ok) {\n                throw new Error(result.error || 'API request failed');\n            }\n            if (result.alreadyInDesiredState) {\n                setSuccess(result.message);\n            } else {\n                setSuccess(\"API: \".concat(result.message, \" (Tx: \").concat(result.transactionHash, \")\"));\n            }\n            setWhitelistAddress('');\n            // Refresh whitelist after successful operation\n            if (activeTab === 'whitelist') {\n                await fetchWhitelistedAddresses();\n            }\n        } catch (error) {\n            console.error('Error with API whitelist:', error);\n            setError(\"API whitelist \".concat(action, \" failed: \").concat(error.message));\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    // Fetch whitelisted addresses with balances\n    const fetchWhitelistedAddresses = async ()=>{\n        if (!provider || !tokenAddress) return;\n        setLoadingWhitelist(true);\n        try {\n            // For now, let's directly check known addresses instead of using the API\n            // Let's use the addresses without checksum validation for now\n            const knownAddresses = [\n                '******************************************' // Test address only\n            ];\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_5__.Contract(tokenAddress, SecurityTokenCoreABI, provider);\n            console.log('Checking whitelist status for known addresses...');\n            // Check which addresses are actually whitelisted\n            const whitelistedAddrs = [];\n            for (const addr of knownAddresses){\n                try {\n                    const isWhitelisted = await contract.isWhitelisted(addr);\n                    console.log(\"Address \".concat(addr, \" whitelisted: \").concat(isWhitelisted));\n                    if (isWhitelisted) {\n                        whitelistedAddrs.push(addr);\n                    }\n                } catch (error) {\n                    console.warn(\"Error checking whitelist for \".concat(addr, \":\"), error);\n                }\n            }\n            console.log(\"Found \".concat(whitelistedAddrs.length, \" whitelisted addresses:\"), whitelistedAddrs);\n            // Get frozen token amounts - using localStorage for now due to database issues\n            let userFrozenAmounts = {};\n            try {\n                // Try to get from localStorage first\n                const localStorageKey = \"frozen_tokens_\".concat(tokenAddress);\n                const storedData = localStorage.getItem(localStorageKey);\n                if (storedData) {\n                    userFrozenAmounts = JSON.parse(storedData);\n                    console.log('Loaded frozen amounts from localStorage:', userFrozenAmounts);\n                }\n                // Also try database (but don't fail if it doesn't work)\n                try {\n                    console.log('Attempting to fetch freeze tracking data from database...');\n                    const freezeResponse = await fetch(\"/api/freeze-tracking?tokenAddress=\".concat(tokenAddress));\n                    if (freezeResponse.ok) {\n                        const freezeData = await freezeResponse.json();\n                        if (freezeData.userFrozenAmounts && Object.keys(freezeData.userFrozenAmounts).length > 0) {\n                            userFrozenAmounts = freezeData.userFrozenAmounts;\n                            console.log('Using database frozen amounts:', userFrozenAmounts);\n                        }\n                    }\n                } catch (dbError) {\n                    console.log('Database not available, using localStorage fallback');\n                }\n            } catch (error) {\n                console.warn('Error loading frozen token data:', error);\n            }\n            const decimals = (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.decimals) || 0;\n            // Now get balance and freeze status for each whitelisted address\n            const addressesWithBalances = await Promise.all(whitelistedAddrs.map(async (addr)=>{\n                try {\n                    const [balance, isFrozen, isVerified] = await Promise.all([\n                        contract.balanceOf(addr),\n                        contract.isFrozen ? contract.isFrozen(addr).catch(()=>false) : false,\n                        contract.isVerified ? contract.isVerified(addr).catch(()=>false) : false\n                    ]);\n                    const balanceFormatted = decimals === 0 ? balance.toString() : ethers__WEBPACK_IMPORTED_MODULE_6__.formatUnits(balance, decimals);\n                    // Get frozen tokens for this address from database tracking\n                    let userFrozenAmount = userFrozenAmounts[addr.toLowerCase()] || 0;\n                    // Temporary fallback: if database doesn't have data for test address, show 7 frozen tokens\n                    if (userFrozenAmount === 0 && addr.toLowerCase() === '******************************************') {\n                        console.log('Database has no frozen data for test address, using fallback of 7 tokens');\n                        userFrozenAmount = 7;\n                    }\n                    const frozenTokens = userFrozenAmount.toString();\n                    console.log(\"Frozen tokens for \".concat(addr, \": \").concat(frozenTokens, \" (from database: \").concat(userFrozenAmounts[addr.toLowerCase()] || 0, \")\"));\n                    return {\n                        address: addr,\n                        balance: balanceFormatted,\n                        frozenTokens: frozenTokens,\n                        isFrozen,\n                        isVerified\n                    };\n                } catch (error) {\n                    console.warn(\"Error fetching data for address \".concat(addr, \":\"), error);\n                    return {\n                        address: addr,\n                        balance: '0',\n                        frozenTokens: '0',\n                        isFrozen: false,\n                        isVerified: false\n                    };\n                }\n            }));\n            setWhitelistedAddresses(addressesWithBalances);\n            // Calculate total vault balance from all frozen amounts\n            const totalFrozenBalance = Object.values(userFrozenAmounts).reduce((sum, amount)=>sum + amount, 0);\n            setVaultBalance(totalFrozenBalance.toString());\n        } catch (error) {\n            console.error('Error fetching whitelisted addresses:', error);\n            setError(\"Failed to fetch whitelisted addresses: \".concat(error.message));\n        } finally{\n            setLoadingWhitelist(false);\n        }\n    };\n    // Utility functions\n    const formatAddress = (address)=>{\n        return \"\".concat(address.slice(0, 6), \"...\").concat(address.slice(-4));\n    };\n    const formatTimestamp = (timestamp)=>{\n        return new Date(timestamp * 1000).toLocaleString();\n    };\n    const getTimeUntilExecution = (executeTime)=>{\n        const now = Math.floor(Date.now() / 1000);\n        const timeLeft = executeTime - now;\n        if (timeLeft <= 0) return 'Ready to execute';\n        const hours = Math.floor(timeLeft / 3600);\n        const minutes = Math.floor(timeLeft % 3600 / 60);\n        return \"\".concat(hours, \"h \").concat(minutes, \"m remaining\");\n    };\n    // Mint tokens to specific address\n    const handleMintToAddress = async (address, amount)=>{\n        if (!amount || !address) return;\n        setActionLoading(true);\n        setError(null);\n        setSuccess(null);\n        try {\n            await mintTokens(address, amount);\n            setSuccess(\"Successfully minted \".concat(amount, \" tokens to \").concat(address));\n            await fetchWhitelistedAddresses(); // Refresh the list\n        } catch (error) {\n            console.error('Error minting tokens:', error);\n            setError(\"Failed to mint tokens: \".concat(error.message));\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    // Freeze tokens (partial freeze by transferring to vault)\n    const handleFreezeTokens = async (address, amount)=>{\n        if (!amount || parseFloat(amount) <= 0) {\n            setError('Please enter a valid amount to freeze');\n            return;\n        }\n        setActionLoading(true);\n        setError(null);\n        setSuccess(null);\n        try {\n            const response = await fetch('/api/contracts/token/partial-freeze', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    tokenAddress: tokenAddress,\n                    fromAddress: address,\n                    amount: amount,\n                    network: 'amoy'\n                })\n            });\n            const result = await response.json();\n            if (!response.ok) {\n                throw new Error(result.error || 'Freeze operation failed');\n            }\n            setSuccess(\"Successfully froze \".concat(amount, \" tokens from \").concat(address, \" (Tx: \").concat(result.txHash, \")\"));\n            setOperationAmount(''); // Clear the amount field\n            await fetchWhitelistedAddresses(); // Refresh the list\n        } catch (error) {\n            console.error('Error with freeze operation:', error);\n            setError(\"Failed to freeze tokens: \".concat(error.message));\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    // Unfreeze tokens (return tokens from vault to user)\n    const handleUnfreezeTokens = async (address, amount)=>{\n        if (!amount || parseFloat(amount) <= 0) {\n            setError('Please enter a valid amount to unfreeze');\n            return;\n        }\n        setActionLoading(true);\n        setError(null);\n        setSuccess(null);\n        try {\n            const response = await fetch('/api/contracts/token/partial-unfreeze', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    tokenAddress: tokenAddress,\n                    toAddress: address,\n                    amount: amount,\n                    network: 'amoy'\n                })\n            });\n            const result = await response.json();\n            if (!response.ok) {\n                throw new Error(result.error || 'Unfreeze operation failed');\n            }\n            setSuccess(\"Successfully unfroze \".concat(amount, \" tokens to \").concat(address, \" (Tx: \").concat(result.txHash, \")\"));\n            setOperationAmount(''); // Clear the amount field\n            await fetchWhitelistedAddresses(); // Refresh the list\n        } catch (error) {\n            console.error('Error with unfreeze operation:', error);\n            setError(\"Failed to unfreeze tokens: \".concat(error.message));\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    // Force transfer tokens\n    const handleForceTransfer = async (fromAddress, toAddress, amount)=>{\n        if (!fromAddress || !toAddress || !amount) return;\n        setActionLoading(true);\n        setError(null);\n        setSuccess(null);\n        try {\n            const response = await fetch('/api/contracts/token/force-transfer', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    tokenAddress: tokenAddress,\n                    fromAddress: fromAddress,\n                    toAddress: toAddress,\n                    amount: amount\n                })\n            });\n            const result = await response.json();\n            if (!response.ok) {\n                throw new Error(result.error || 'Force transfer failed');\n            }\n            setSuccess(\"Successfully force transferred \".concat(amount, \" tokens from \").concat(fromAddress, \" to \").concat(toAddress));\n            await fetchWhitelistedAddresses(); // Refresh the list\n        } catch (error) {\n            console.error('Error with force transfer:', error);\n            setError(\"Failed to force transfer: \".concat(error.message));\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    if (!provider || !signer) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-6 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold text-yellow-800 mb-4\",\n                        children: \"\\uD83D\\uDD17 Wallet Connection Required\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                        lineNumber: 797,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-yellow-700 mb-4\",\n                        children: \"Please connect your MetaMask wallet to manage this modular token.\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                        lineNumber: 800,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: initializeProvider,\n                        className: \"bg-yellow-600 hover:bg-yellow-700 text-white font-bold py-2 px-4 rounded\",\n                        children: \"Connect Wallet\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                        lineNumber: 803,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                lineNumber: 796,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n            lineNumber: 795,\n            columnNumber: 7\n        }, this);\n    }\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-center items-center h-64\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                    lineNumber: 818,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                lineNumber: 817,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n            lineNumber: 816,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto px-4 py-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-gray-900 mb-2\",\n                                children: \"Modular Token Management\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 830,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: [\n                                    \"Token Address: \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-mono text-sm\",\n                                        children: tokenAddress\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 834,\n                                        columnNumber: 30\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 833,\n                                columnNumber: 13\n                            }, this),\n                            tokenInfo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: [\n                                    tokenInfo.name,\n                                    \" (\",\n                                    tokenInfo.symbol,\n                                    \") - Version \",\n                                    tokenInfo.version\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 837,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                        lineNumber: 829,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                    lineNumber: 828,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                lineNumber: 827,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-50 border border-red-200 rounded-lg p-4 mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-shrink-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"h-5 w-5 text-red-400\",\n                                viewBox: \"0 0 20 20\",\n                                fill: \"currentColor\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    fillRule: \"evenodd\",\n                                    d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\",\n                                    clipRule: \"evenodd\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                    lineNumber: 851,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 850,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                            lineNumber: 849,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"ml-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-sm font-medium text-red-800\",\n                                    children: \"Error\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                    lineNumber: 855,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-red-700 mt-1\",\n                                    children: error\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                    lineNumber: 856,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                            lineNumber: 854,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                    lineNumber: 848,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                lineNumber: 847,\n                columnNumber: 9\n            }, this),\n            success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-green-50 border border-green-200 rounded-lg p-4 mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-shrink-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"h-5 w-5 text-green-400\",\n                                viewBox: \"0 0 20 20\",\n                                fill: \"currentColor\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    fillRule: \"evenodd\",\n                                    d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",\n                                    clipRule: \"evenodd\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                    lineNumber: 867,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 866,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                            lineNumber: 865,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"ml-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-sm font-medium text-green-800\",\n                                    children: \"Success\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                    lineNumber: 871,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-green-700 mt-1\",\n                                    children: success\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                    lineNumber: 872,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                            lineNumber: 870,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                    lineNumber: 864,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                lineNumber: 863,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-b border-gray-200\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"-mb-px flex space-x-8 px-6\",\n                        children: [\n                            {\n                                id: 'overview',\n                                name: 'Overview',\n                                icon: '📊'\n                            },\n                            {\n                                id: 'mint',\n                                name: 'Mint Tokens',\n                                icon: '🪙'\n                            },\n                            {\n                                id: 'pause',\n                                name: 'Pause Control',\n                                icon: '⏸️'\n                            },\n                            {\n                                id: 'admin',\n                                name: 'Admin Controls',\n                                icon: '⚙️'\n                            },\n                            {\n                                id: 'whitelist',\n                                name: 'Whitelist Management',\n                                icon: '👥'\n                            },\n                            {\n                                id: 'kyc',\n                                name: 'KYC & Claims',\n                                icon: '🔐'\n                            },\n                            {\n                                id: 'upgrades',\n                                name: 'Upgrades',\n                                icon: '🔄'\n                            }\n                        ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab(tab.id),\n                                className: \"\".concat(activeTab === tab.id ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300', \" whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: tab.icon\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 900,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: tab.name\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 901,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, tab.id, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 891,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                        lineNumber: 881,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                    lineNumber: 880,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                lineNumber: 879,\n                columnNumber: 7\n            }, this),\n            activeTab === 'overview' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: [\n                    tokenInfo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold mb-4\",\n                                children: \"Token Overview\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 914,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-500\",\n                                                children: \"Total Supply\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 917,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold text-blue-600\",\n                                                children: tokenInfo.totalSupply\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 918,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 916,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-500\",\n                                                children: \"Max Supply\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 923,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold text-green-600\",\n                                                children: tokenInfo.maxSupply\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 924,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 922,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-500\",\n                                                children: \"Status\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 929,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold \".concat(tokenInfo.paused ? 'text-red-600' : 'text-green-600'),\n                                                children: tokenInfo.paused ? 'Paused' : 'Active'\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 930,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 928,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 915,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6 grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-500\",\n                                                children: \"Token Price\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 938,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"font-medium\",\n                                                children: ((_tokenInfo_metadata = tokenInfo.metadata) === null || _tokenInfo_metadata === void 0 ? void 0 : _tokenInfo_metadata.tokenPrice) || 'Not set'\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 939,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 937,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-500\",\n                                                children: \"Bonus Tiers\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 942,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"font-medium\",\n                                                children: ((_tokenInfo_metadata1 = tokenInfo.metadata) === null || _tokenInfo_metadata1 === void 0 ? void 0 : _tokenInfo_metadata1.bonusTiers) || 'Not set'\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 943,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 941,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-500\",\n                                                children: \"Token Details\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 946,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"font-medium\",\n                                                children: ((_tokenInfo_metadata2 = tokenInfo.metadata) === null || _tokenInfo_metadata2 === void 0 ? void 0 : _tokenInfo_metadata2.tokenDetails) || 'Not set'\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 947,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 945,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-500\",\n                                                children: \"Version\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 950,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"font-medium\",\n                                                children: tokenInfo.version\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 951,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 949,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 936,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                        lineNumber: 913,\n                        columnNumber: 13\n                    }, this),\n                    upgradeInfo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold mb-4\",\n                                children: \"Upgrade System Status\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 960,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-500\",\n                                                children: \"Emergency Mode\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 963,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-lg font-bold \".concat(upgradeInfo.emergencyModeActive ? 'text-red-600' : 'text-green-600'),\n                                                children: upgradeInfo.emergencyModeActive ? 'ACTIVE' : 'Inactive'\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 964,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 962,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-500\",\n                                                children: \"Upgrade Delay\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 969,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-lg font-bold text-blue-600\",\n                                                children: [\n                                                    Math.floor(upgradeInfo.upgradeDelay / 3600),\n                                                    \" hours\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 970,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 968,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 961,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-500\",\n                                        children: \"Registered Modules\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 977,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-2 space-y-1\",\n                                        children: upgradeInfo.registeredModules.map((module, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm font-mono bg-gray-100 p-2 rounded\",\n                                                children: formatAddress(module)\n                                            }, index, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 980,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 978,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 976,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                        lineNumber: 959,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                lineNumber: 910,\n                columnNumber: 9\n            }, this),\n            activeTab === 'mint' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold mb-6\",\n                        children: \"Mint Tokens\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                        lineNumber: 994,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                children: \"Recipient Address\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 999,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: mintAddress,\n                                                onChange: (e)=>setMintAddress(e.target.value),\n                                                placeholder: \"0x...\",\n                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1002,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 998,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                children: \"Amount\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1011,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"number\",\n                                                value: mintAmount,\n                                                onChange: (e)=>setMintAmount(e.target.value),\n                                                placeholder: \"100\",\n                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1014,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1010,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 997,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleMint,\n                                        disabled: actionLoading || !mintAddress || !mintAmount,\n                                        className: \"bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-bold py-2 px-4 rounded\",\n                                        children: actionLoading ? 'Processing...' : 'Mint (Wallet)'\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1025,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleMintAPI,\n                                        disabled: actionLoading || !mintAddress || !mintAmount,\n                                        className: \"bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white font-bold py-2 px-4 rounded\",\n                                        children: actionLoading ? 'Processing...' : 'Mint (API)'\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1032,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 1024,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Wallet Method:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1042,\n                                                columnNumber: 18\n                                            }, this),\n                                            \" Uses your connected MetaMask wallet to sign the transaction.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1042,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"API Method:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1043,\n                                                columnNumber: 18\n                                            }, this),\n                                            \" Uses the server's private key to execute the transaction.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1043,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 1041,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                        lineNumber: 996,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                lineNumber: 993,\n                columnNumber: 9\n            }, this),\n            activeTab === 'pause' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold mb-6\",\n                        children: \"Pause Control\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                        lineNumber: 1052,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-50 p-4 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-medium text-gray-900 mb-2\",\n                                        children: \"Current Status\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1056,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-lg font-bold \".concat((tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.paused) ? 'text-red-600' : 'text-green-600'),\n                                        children: (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.paused) ? '⏸️ PAUSED' : '▶️ ACTIVE'\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1057,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 1055,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handlePauseToggle,\n                                        disabled: actionLoading,\n                                        className: \"\".concat((tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.paused) ? 'bg-green-600 hover:bg-green-700' : 'bg-red-600 hover:bg-red-700', \" disabled:bg-gray-400 text-white font-bold py-2 px-4 rounded\"),\n                                        children: actionLoading ? 'Processing...' : (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.paused) ? 'Unpause (Wallet)' : 'Pause (Wallet)'\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1063,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handlePauseToggleAPI,\n                                        disabled: actionLoading,\n                                        className: \"\".concat((tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.paused) ? 'bg-green-600 hover:bg-green-700' : 'bg-red-600 hover:bg-red-700', \" disabled:bg-gray-400 text-white font-bold py-2 px-4 rounded\"),\n                                        children: actionLoading ? 'Processing...' : (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.paused) ? 'Unpause (API)' : 'Pause (API)'\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1074,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 1062,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Pause:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1088,\n                                                columnNumber: 18\n                                            }, this),\n                                            \" Stops all token transfers and minting operations.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1088,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Unpause:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1089,\n                                                columnNumber: 18\n                                            }, this),\n                                            \" Resumes normal token operations.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1089,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 1087,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                        lineNumber: 1054,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                lineNumber: 1051,\n                columnNumber: 9\n            }, this),\n            activeTab === 'admin' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold mb-6\",\n                        children: \"Admin Controls\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                        lineNumber: 1098,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-b border-gray-200 pb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 mb-4\",\n                                        children: \"Update Token Price\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1103,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: newTokenPrice,\n                                                onChange: (e)=>setNewTokenPrice(e.target.value),\n                                                placeholder: \"Enter new price (e.g., $1.50)\",\n                                                className: \"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1105,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleUpdatePrice,\n                                                disabled: actionLoading || !newTokenPrice,\n                                                className: \"bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-bold py-2 px-4 rounded\",\n                                                children: actionLoading ? 'Updating...' : 'Update Price'\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1112,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1104,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600 mt-2\",\n                                        children: [\n                                            \"Current: \",\n                                            (tokenInfo === null || tokenInfo === void 0 ? void 0 : (_tokenInfo_metadata3 = tokenInfo.metadata) === null || _tokenInfo_metadata3 === void 0 ? void 0 : _tokenInfo_metadata3.tokenPrice) || 'Not set'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1120,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 1102,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-b border-gray-200 pb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 mb-4\",\n                                        children: \"Update Bonus Tiers\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1127,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: newBonusTiers,\n                                                onChange: (e)=>setNewBonusTiers(e.target.value),\n                                                placeholder: \"Enter bonus tiers (e.g., 5%,10%,15%)\",\n                                                className: \"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1129,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleUpdateBonusTiers,\n                                                disabled: actionLoading || !newBonusTiers,\n                                                className: \"bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-bold py-2 px-4 rounded\",\n                                                children: actionLoading ? 'Updating...' : 'Update Tiers'\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1136,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1128,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600 mt-2\",\n                                        children: [\n                                            \"Current: \",\n                                            (tokenInfo === null || tokenInfo === void 0 ? void 0 : (_tokenInfo_metadata4 = tokenInfo.metadata) === null || _tokenInfo_metadata4 === void 0 ? void 0 : _tokenInfo_metadata4.bonusTiers) || 'Not set'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1144,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 1126,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-b border-gray-200 pb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 mb-4\",\n                                        children: \"Update Max Supply\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1151,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"number\",\n                                                value: newMaxSupply,\n                                                onChange: (e)=>setNewMaxSupply(e.target.value),\n                                                placeholder: \"Enter new max supply\",\n                                                className: \"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1153,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleUpdateMaxSupply,\n                                                disabled: actionLoading || !newMaxSupply,\n                                                className: \"bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-bold py-2 px-4 rounded\",\n                                                children: actionLoading ? 'Updating...' : 'Update Max Supply'\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1160,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1152,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600 mt-2\",\n                                        children: [\n                                            \"Current: \",\n                                            (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.maxSupply) || 'Not set'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1168,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 1150,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 mb-4\",\n                                        children: \"Whitelist Management\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1175,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: whitelistAddress,\n                                                onChange: (e)=>setWhitelistAddress(e.target.value),\n                                                placeholder: \"Enter wallet address\",\n                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1177,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleAddToWhitelistAdmin,\n                                                        disabled: actionLoading || !whitelistAddress,\n                                                        className: \"bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white font-bold py-2 px-4 rounded\",\n                                                        children: actionLoading ? 'Processing...' : 'Add (Wallet)'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                        lineNumber: 1185,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleRemoveFromWhitelist,\n                                                        disabled: actionLoading || !whitelistAddress,\n                                                        className: \"bg-red-600 hover:bg-red-700 disabled:bg-gray-400 text-white font-bold py-2 px-4 rounded\",\n                                                        children: actionLoading ? 'Processing...' : 'Remove (Wallet)'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                        lineNumber: 1192,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleWhitelistAPI('add'),\n                                                        disabled: actionLoading || !whitelistAddress,\n                                                        className: \"bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white font-bold py-2 px-4 rounded\",\n                                                        children: actionLoading ? 'Processing...' : 'Add (API)'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                        lineNumber: 1199,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleWhitelistAPI('remove'),\n                                                        disabled: actionLoading || !whitelistAddress,\n                                                        className: \"bg-red-600 hover:bg-red-700 disabled:bg-gray-400 text-white font-bold py-2 px-4 rounded\",\n                                                        children: actionLoading ? 'Processing...' : 'Remove (API)'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                        lineNumber: 1206,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1184,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-600 mt-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"⚠️ Important:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                lineNumber: 1215,\n                                                                columnNumber: 22\n                                                            }, this),\n                                                            \" Addresses must be registered with the identity registry before whitelisting.\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                        lineNumber: 1215,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Wallet Method:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                lineNumber: 1216,\n                                                                columnNumber: 22\n                                                            }, this),\n                                                            \" Requires manual identity registration first.\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                        lineNumber: 1216,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"API Method (Recommended):\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                lineNumber: 1217,\n                                                                columnNumber: 22\n                                                            }, this),\n                                                            \" Automatically handles identity registration if needed.\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                        lineNumber: 1217,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1214,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1176,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 1174,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                        lineNumber: 1100,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                lineNumber: 1097,\n                columnNumber: 9\n            }, this),\n            activeTab === 'whitelist' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold\",\n                                children: \"Whitelist Management\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 1229,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: fetchWhitelistedAddresses,\n                                disabled: loadingWhitelist,\n                                className: \"bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-bold py-2 px-4 rounded\",\n                                children: loadingWhitelist ? 'Loading...' : 'Refresh List'\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 1230,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                        lineNumber: 1228,\n                        columnNumber: 11\n                    }, this),\n                    loadingWhitelist ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center items-center h-32\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                            lineNumber: 1241,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                        lineNumber: 1240,\n                        columnNumber: 13\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-5 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-blue-50 p-4 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-blue-600\",\n                                                children: \"Total Whitelisted\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1248,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold text-blue-800\",\n                                                children: whitelistedAddresses.length\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1249,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1247,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-green-50 p-4 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-green-600\",\n                                                children: \"Active Holders\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1252,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold text-green-800\",\n                                                children: whitelistedAddresses.filter((addr)=>parseFloat(addr.balance) > 0).length\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1253,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1251,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-yellow-50 p-4 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-yellow-600\",\n                                                children: \"Frozen Addresses\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1258,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold text-yellow-800\",\n                                                children: whitelistedAddresses.filter((addr)=>addr.isFrozen).length\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1259,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1257,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-purple-50 p-4 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-purple-600\",\n                                                children: \"Active Balance\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1264,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold text-purple-800\",\n                                                children: whitelistedAddresses.reduce((sum, addr)=>sum + parseFloat(addr.balance), 0).toFixed(2)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1265,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-purple-600\",\n                                                children: \"Circulating tokens\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1268,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1263,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-orange-50 p-4 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-orange-600\",\n                                                children: \"Frozen Balance\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1271,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold text-orange-800\",\n                                                children: whitelistedAddresses.reduce((sum, addr)=>sum + parseFloat(addr.frozenTokens || '0'), 0).toFixed(2)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1272,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-orange-600\",\n                                                children: \"Vault tracking\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1275,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: async ()=>{\n                                                    try {\n                                                        const response = await fetch('/api/seed-freeze-data', {\n                                                            method: 'POST'\n                                                        });\n                                                        if (response.ok) {\n                                                            alert('Historical freeze data seeded! Refresh the page.');\n                                                        } else {\n                                                            alert('Failed to seed data');\n                                                        }\n                                                    } catch (error) {\n                                                        alert('Error seeding data');\n                                                    }\n                                                },\n                                                className: \"mt-2 px-2 py-1 bg-orange-600 text-white text-xs rounded hover:bg-orange-700\",\n                                                children: \"Seed Historical Data\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1276,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1270,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 1246,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"overflow-x-auto\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                    className: \"min-w-full divide-y divide-gray-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                            className: \"bg-gray-50\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"Address\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                        lineNumber: 1301,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"Balance\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                        lineNumber: 1304,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"Frozen\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                        lineNumber: 1307,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"Status\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                        lineNumber: 1310,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"Actions\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                        lineNumber: 1313,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1300,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                            lineNumber: 1299,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                            className: \"bg-white divide-y divide-gray-200\",\n                                            children: whitelistedAddresses.map((addr, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: index % 2 === 0 ? 'bg-white' : 'bg-gray-50',\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm font-mono text-gray-900\",\n                                                                    children: formatAddress(addr.address)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                    lineNumber: 1322,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-gray-500\",\n                                                                    children: addr.address\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                    lineNumber: 1325,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                            lineNumber: 1321,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-900\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"font-medium\",\n                                                                        children: [\n                                                                            \"Active: \",\n                                                                            addr.balance\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                        lineNumber: 1331,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-xs text-orange-600\",\n                                                                        children: [\n                                                                            \"Frozen: \",\n                                                                            addr.frozenTokens || '0'\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                        lineNumber: 1332,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-xs text-gray-500\",\n                                                                        children: [\n                                                                            \"Total: \",\n                                                                            (parseFloat(addr.balance) + parseFloat(addr.frozenTokens || '0')).toFixed(2)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                        lineNumber: 1335,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                lineNumber: 1330,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                            lineNumber: 1329,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full \".concat(addr.isFrozen ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'),\n                                                                children: addr.isFrozen ? 'Frozen' : 'Active'\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                lineNumber: 1341,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                            lineNumber: 1340,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex space-x-1\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full \".concat(addr.isVerified ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'),\n                                                                    children: addr.isVerified ? 'Verified' : 'Unverified'\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                    lineNumber: 1349,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                lineNumber: 1348,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                            lineNumber: 1347,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap text-sm font-medium\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex space-x-2\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>setSelectedAddress(addr.address),\n                                                                    className: \"text-blue-600 hover:text-blue-900\",\n                                                                    children: \"Manage\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                    lineNumber: 1358,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                lineNumber: 1357,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                            lineNumber: 1356,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, addr.address, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                    lineNumber: 1320,\n                                                    columnNumber: 23\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                            lineNumber: 1318,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                    lineNumber: 1298,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 1297,\n                                columnNumber: 15\n                            }, this),\n                            whitelistedAddresses.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8 text-gray-500\",\n                                children: \"No whitelisted addresses found. Add addresses using the Admin Controls tab.\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 1373,\n                                columnNumber: 17\n                            }, this),\n                            selectedAddress && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-medium text-gray-900\",\n                                                        children: [\n                                                            \"Manage Address: \",\n                                                            formatAddress(selectedAddress)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                        lineNumber: 1384,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setSelectedAddress(''),\n                                                        className: \"text-gray-400 hover:text-gray-600\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-2xl\",\n                                                            children: \"\\xd7\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                            lineNumber: 1391,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                        lineNumber: 1387,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1383,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-6\",\n                                                children: [\n                                                    (()=>{\n                                                        const addressData = whitelistedAddresses.find((addr)=>addr.address === selectedAddress);\n                                                        return addressData ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-gray-50 p-4 rounded-lg\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"font-medium mb-2\",\n                                                                    children: \"Current Status\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                    lineNumber: 1401,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"grid grid-cols-2 gap-4 text-sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                \"Balance: \",\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"font-medium\",\n                                                                                    children: addressData.balance\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                                    lineNumber: 1403,\n                                                                                    columnNumber: 47\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                            lineNumber: 1403,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                \"Frozen: \",\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"font-medium\",\n                                                                                    children: addressData.frozenTokens\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                                    lineNumber: 1404,\n                                                                                    columnNumber: 46\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                            lineNumber: 1404,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                \"Status: \",\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"font-medium \".concat(addressData.isFrozen ? 'text-red-600' : 'text-green-600'),\n                                                                                    children: addressData.isFrozen ? 'Frozen' : 'Active'\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                                    lineNumber: 1405,\n                                                                                    columnNumber: 46\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                            lineNumber: 1405,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                \"Verified: \",\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"font-medium \".concat(addressData.isVerified ? 'text-green-600' : 'text-gray-600'),\n                                                                                    children: addressData.isVerified ? 'Yes' : 'No'\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                                    lineNumber: 1408,\n                                                                                    columnNumber: 48\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                            lineNumber: 1408,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                    lineNumber: 1402,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                            lineNumber: 1400,\n                                                            columnNumber: 29\n                                                        }, this) : null;\n                                                    })(),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"border-b border-gray-200 pb-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-medium mb-3\",\n                                                                children: \"Mint Tokens\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                lineNumber: 1418,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex space-x-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"number\",\n                                                                        value: operationAmount,\n                                                                        onChange: (e)=>setOperationAmount(e.target.value),\n                                                                        placeholder: \"Amount to mint\",\n                                                                        className: \"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                        lineNumber: 1420,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>handleMintToAddress(selectedAddress, operationAmount),\n                                                                        disabled: actionLoading || !operationAmount,\n                                                                        className: \"bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white font-bold py-2 px-4 rounded\",\n                                                                        children: actionLoading ? 'Minting...' : 'Mint'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                        lineNumber: 1427,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                lineNumber: 1419,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                        lineNumber: 1417,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"border-b border-gray-200 pb-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-medium mb-3\",\n                                                                children: \"Freeze/Unfreeze Tokens\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                lineNumber: 1439,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex space-x-3\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"number\",\n                                                                                value: operationAmount,\n                                                                                onChange: (e)=>setOperationAmount(e.target.value),\n                                                                                placeholder: \"Amount to freeze/unfreeze\",\n                                                                                className: \"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                                lineNumber: 1442,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: ()=>handleFreezeTokens(selectedAddress, operationAmount),\n                                                                                disabled: actionLoading || !operationAmount,\n                                                                                className: \"bg-yellow-600 hover:bg-yellow-700 disabled:bg-gray-400 text-white font-bold py-2 px-4 rounded\",\n                                                                                children: actionLoading ? 'Processing...' : 'Freeze'\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                                lineNumber: 1449,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: ()=>handleUnfreezeTokens(selectedAddress, operationAmount),\n                                                                                disabled: actionLoading || !operationAmount,\n                                                                                className: \"bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white font-bold py-2 px-4 rounded\",\n                                                                                children: actionLoading ? 'Processing...' : 'Unfreeze'\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                                lineNumber: 1456,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                        lineNumber: 1441,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-gray-600\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                        children: \"Freeze:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                                        lineNumber: 1465,\n                                                                                        columnNumber: 34\n                                                                                    }, this),\n                                                                                    \" Mints tracking tokens to vault (user keeps original tokens, vault tracks frozen amount).\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                                lineNumber: 1465,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                        children: \"Unfreeze:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                                        lineNumber: 1466,\n                                                                                        columnNumber: 34\n                                                                                    }, this),\n                                                                                    \" Mints additional tokens to user (compensates for frozen amount).\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                                lineNumber: 1466,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                        children: \"Vault Balance:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                                        lineNumber: 1467,\n                                                                                        columnNumber: 34\n                                                                                    }, this),\n                                                                                    \" \",\n                                                                                    vaultBalance,\n                                                                                    \" tokens (represents total frozen amounts)\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                                lineNumber: 1467,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                        children: \"Note:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                                        lineNumber: 1468,\n                                                                                        columnNumber: 34\n                                                                                    }, this),\n                                                                                    \" This is a simplified freeze system that tracks frozen amounts without removing user tokens.\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                                lineNumber: 1468,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                        lineNumber: 1464,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                lineNumber: 1440,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                        lineNumber: 1438,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-medium mb-3\",\n                                                                children: \"Admin Mint to Address\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                lineNumber: 1475,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"text\",\n                                                                        value: transferToAddress,\n                                                                        onChange: (e)=>setTransferToAddress(e.target.value),\n                                                                        placeholder: \"Mint to address (0x...)\",\n                                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                        lineNumber: 1477,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex space-x-3\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"number\",\n                                                                                value: operationAmount,\n                                                                                onChange: (e)=>setOperationAmount(e.target.value),\n                                                                                placeholder: \"Amount to mint\",\n                                                                                className: \"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                                lineNumber: 1485,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: ()=>handleForceTransfer(selectedAddress, transferToAddress, operationAmount),\n                                                                                disabled: actionLoading || !transferToAddress || !operationAmount,\n                                                                                className: \"bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-bold py-2 px-4 rounded\",\n                                                                                children: actionLoading ? 'Minting...' : 'Admin Mint'\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                                lineNumber: 1492,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                        lineNumber: 1484,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-gray-600\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                    children: \"Note:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                                    lineNumber: 1501,\n                                                                                    columnNumber: 34\n                                                                                }, this),\n                                                                                \" This mints new tokens to the specified address (admin operation).\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                            lineNumber: 1501,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                        lineNumber: 1500,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                lineNumber: 1476,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                        lineNumber: 1474,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-end pt-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setSelectedAddress(''),\n                                                            className: \"bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded\",\n                                                            children: \"Close\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                            lineNumber: 1508,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                        lineNumber: 1507,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1395,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1382,\n                                        columnNumber: 21\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                    lineNumber: 1381,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 1380,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                        lineNumber: 1244,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                lineNumber: 1227,\n                columnNumber: 9\n            }, this),\n            activeTab === 'kyc' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold mb-6\",\n                        children: \"KYC & Claims Management\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                        lineNumber: 1528,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-b border-gray-200 pb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 mb-4\",\n                                        children: \"KYC Management\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1533,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: kycUserAddress,\n                                                onChange: (e)=>setKycUserAddress(e.target.value),\n                                                placeholder: \"Enter user wallet address\",\n                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1535,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleApproveKYC,\n                                                        disabled: actionLoading || !kycUserAddress,\n                                                        className: \"bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white font-bold py-2 px-4 rounded\",\n                                                        children: actionLoading ? 'Processing...' : 'Approve KYC'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                        lineNumber: 1543,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleAddToWhitelistKYC,\n                                                        disabled: actionLoading || !kycUserAddress,\n                                                        className: \"bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-bold py-2 px-4 rounded\",\n                                                        children: actionLoading ? 'Processing...' : 'Add to Whitelist'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                        lineNumber: 1550,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1542,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1534,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 1532,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-b border-gray-200 pb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 mb-4\",\n                                        children: \"Issue Claims\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1563,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: claimUserAddress,\n                                                        onChange: (e)=>setClaimUserAddress(e.target.value),\n                                                        placeholder: \"User wallet address\",\n                                                        className: \"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                        lineNumber: 1566,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: claimTopicId,\n                                                        onChange: (e)=>setClaimTopicId(e.target.value),\n                                                        placeholder: \"Topic ID (e.g., 10101010000001)\",\n                                                        className: \"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                        lineNumber: 1573,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1565,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                value: claimData,\n                                                onChange: (e)=>setClaimData(e.target.value),\n                                                placeholder: \"Claim data (optional)\",\n                                                rows: 3,\n                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1581,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleIssueClaim,\n                                                disabled: actionLoading || !claimUserAddress || !claimTopicId,\n                                                className: \"bg-purple-600 hover:bg-purple-700 disabled:bg-gray-400 text-white font-bold py-2 px-4 rounded\",\n                                                children: actionLoading ? 'Processing...' : 'Issue Claim'\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1588,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1564,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 1562,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 mb-4\",\n                                        children: \"Check User Status\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1600,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: checkUserAddress,\n                                                        onChange: (e)=>setCheckUserAddress(e.target.value),\n                                                        placeholder: \"Enter user wallet address\",\n                                                        className: \"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                        lineNumber: 1603,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleCheckStatus,\n                                                        disabled: actionLoading || !checkUserAddress,\n                                                        className: \"bg-indigo-600 hover:bg-indigo-700 disabled:bg-gray-400 text-white font-bold py-2 px-4 rounded\",\n                                                        children: actionLoading ? 'Checking...' : 'Check Status'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                        lineNumber: 1610,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1602,\n                                                columnNumber: 17\n                                            }, this),\n                                            verificationStatus && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-50 p-4 rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-gray-900 mb-2\",\n                                                        children: \"Verification Status\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                        lineNumber: 1621,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                        className: \"text-sm text-gray-700 whitespace-pre-wrap\",\n                                                        children: JSON.stringify(verificationStatus, null, 2)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                        lineNumber: 1622,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1620,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1601,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 1599,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                        lineNumber: 1530,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                lineNumber: 1527,\n                columnNumber: 9\n            }, this),\n            activeTab === 'upgrades' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold mb-6\",\n                        children: \"Upgrade Management\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                        lineNumber: 1636,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-b border-gray-200 pb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 mb-4\",\n                                        children: \"Emergency Mode\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1641,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"px-3 py-2 rounded-lg \".concat((upgradeInfo === null || upgradeInfo === void 0 ? void 0 : upgradeInfo.emergencyModeActive) ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'),\n                                                children: (upgradeInfo === null || upgradeInfo === void 0 ? void 0 : upgradeInfo.emergencyModeActive) ? '🚨 EMERGENCY MODE ACTIVE' : '✅ Normal Operation'\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1643,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleEmergencyModeToggle,\n                                                disabled: actionLoading,\n                                                className: \"\".concat((upgradeInfo === null || upgradeInfo === void 0 ? void 0 : upgradeInfo.emergencyModeActive) ? 'bg-green-600 hover:bg-green-700' : 'bg-red-600 hover:bg-red-700', \" disabled:bg-gray-400 text-white font-bold py-2 px-4 rounded\"),\n                                                children: actionLoading ? 'Processing...' : (upgradeInfo === null || upgradeInfo === void 0 ? void 0 : upgradeInfo.emergencyModeActive) ? 'Deactivate Emergency' : 'Activate Emergency'\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1646,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1642,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 1640,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-b border-gray-200 pb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 mb-4\",\n                                        children: \"Schedule Upgrade\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1662,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: newImplementationAddress,\n                                                onChange: (e)=>setNewImplementationAddress(e.target.value),\n                                                placeholder: \"New implementation contract address\",\n                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1664,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                value: upgradeDescription,\n                                                onChange: (e)=>setUpgradeDescription(e.target.value),\n                                                placeholder: \"Upgrade description\",\n                                                rows: 3,\n                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1671,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleScheduleUpgrade,\n                                                disabled: actionLoading || !newImplementationAddress || !upgradeDescription,\n                                                className: \"bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-bold py-2 px-4 rounded\",\n                                                children: actionLoading ? 'Scheduling...' : 'Schedule Upgrade'\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1678,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1663,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 1661,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 mb-4\",\n                                        children: \"Pending Upgrades\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1690,\n                                        columnNumber: 15\n                                    }, this),\n                                    pendingUpgrades.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-500\",\n                                        children: \"No pending upgrades\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1692,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: pendingUpgrades.map((upgrade, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border border-gray-200 rounded-lg p-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-start\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"font-medium text-gray-900\",\n                                                                    children: [\n                                                                        \"Upgrade #\",\n                                                                        upgrade.upgradeId\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                    lineNumber: 1699,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-600 mt-1\",\n                                                                    children: upgrade.description\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                    lineNumber: 1700,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mt-2 space-y-1 text-sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                    children: \"New Implementation:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                                    lineNumber: 1702,\n                                                                                    columnNumber: 32\n                                                                                }, this),\n                                                                                \" \",\n                                                                                formatAddress(upgrade.newImplementation)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                            lineNumber: 1702,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                    children: \"Execute Time:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                                    lineNumber: 1703,\n                                                                                    columnNumber: 32\n                                                                                }, this),\n                                                                                \" \",\n                                                                                formatTimestamp(upgrade.executeTime)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                            lineNumber: 1703,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                    children: \"Status:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                                    lineNumber: 1704,\n                                                                                    columnNumber: 32\n                                                                                }, this),\n                                                                                \" \",\n                                                                                getTimeUntilExecution(upgrade.executeTime)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                            lineNumber: 1704,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                    lineNumber: 1701,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                            lineNumber: 1698,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"ml-4\",\n                                                            children: upgrade.executeTime <= Math.floor(Date.now() / 1000) && !upgrade.executed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                className: \"bg-green-600 hover:bg-green-700 text-white font-bold py-1 px-3 rounded text-sm\",\n                                                                disabled: actionLoading,\n                                                                children: \"Execute\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                lineNumber: 1709,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                            lineNumber: 1707,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                    lineNumber: 1697,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, index, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1696,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1694,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 1689,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                        lineNumber: 1638,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                lineNumber: 1635,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n        lineNumber: 825,\n        columnNumber: 5\n    }, this);\n}\n_s(ModularTokenDetailsPage, \"e6d7UhI0ymgfSWwfgyjabauv3pU=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        _hooks_useModularToken__WEBPACK_IMPORTED_MODULE_3__.useModularToken\n    ];\n});\n_c = ModularTokenDetailsPage;\nvar _c;\n$RefreshReg$(_c, \"ModularTokenDetailsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/modular-tokens/[address]/page.tsx\n"));

/***/ })

});