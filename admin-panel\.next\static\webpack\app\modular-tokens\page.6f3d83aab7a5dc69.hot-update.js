"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/modular-tokens/page",{

/***/ "(app-pages-browser)/./src/hooks/useModularToken.ts":
/*!**************************************!*\
  !*** ./src/hooks/useModularToken.ts ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useModularToken: () => (/* binding */ useModularToken)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/providers/provider-browser.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/contract/contract.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/utils/units.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/crypto/keccak.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/utils/utf8.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/constants/addresses.js\");\n/* harmony import */ var _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/contracts/SecurityTokenCore.json */ \"(app-pages-browser)/./src/contracts/SecurityTokenCore.json\");\n/* harmony import */ var _contracts_UpgradeManager_json__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contracts/UpgradeManager.json */ \"(app-pages-browser)/./src/contracts/UpgradeManager.json\");\n/* __next_internal_client_entry_do_not_use__ useModularToken auto */ \n\n// Import ABIs - extract the abi property from the JSON artifacts\n\n\n// Extract ABIs from artifacts\nconst SecurityTokenCoreABI = _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_1__.abi;\nconst UpgradeManagerABI = _contracts_UpgradeManager_json__WEBPACK_IMPORTED_MODULE_2__.abi;\n// Contract addresses from environment\nconst SECURITY_TOKEN_CORE_ADDRESS = \"******************************************\";\nconst UPGRADE_MANAGER_ADDRESS = \"0xb7a53dC340C2E5e823002B174629e2a44B8ed815\";\nfunction useModularToken(tokenAddress) {\n    const [provider, setProvider] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [signer, setSigner] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [tokenInfo, setTokenInfo] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [contractAddresses, setContractAddresses] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [userRoles, setUserRoles] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [upgradeInfo, setUpgradeInfo] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [pendingUpgrades, setPendingUpgrades] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [upgradeHistory, setUpgradeHistory] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    // Initialize provider and signer\n    const initializeProvider = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[initializeProvider]\": async ()=>{\n            try {\n                if ( true && window.ethereum) {\n                    const provider = new ethers__WEBPACK_IMPORTED_MODULE_3__.BrowserProvider(window.ethereum);\n                    await provider.send('eth_requestAccounts', []);\n                    const signer = await provider.getSigner();\n                    setProvider(provider);\n                    setSigner(signer);\n                    return {\n                        provider,\n                        signer\n                    };\n                } else {\n                    throw new Error('MetaMask not found');\n                }\n            } catch (error) {\n                console.error('Error initializing provider:', error);\n                setError('Failed to connect to wallet');\n                return null;\n            }\n        }\n    }[\"useModularToken.useCallback[initializeProvider]\"], []);\n    // Load token information\n    const loadTokenInfo = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[loadTokenInfo]\": async ()=>{\n            const contractAddress = tokenAddress || SECURITY_TOKEN_CORE_ADDRESS;\n            if (!provider || !contractAddress) return;\n            try {\n                setLoading(true);\n                const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(contractAddress, SecurityTokenCoreABI, provider);\n                const [name, symbol, version, totalSupply, maxSupply, decimals, paused, metadata] = await Promise.all([\n                    contract.name(),\n                    contract.symbol(),\n                    contract.version(),\n                    contract.totalSupply(),\n                    contract.maxSupply(),\n                    contract.decimals(),\n                    contract.paused(),\n                    contract.getTokenMetadata()\n                ]);\n                console.log('Token contract data:');\n                console.log('- Name:', name);\n                console.log('- Symbol:', symbol);\n                console.log('- Decimals (raw):', decimals);\n                console.log('- Decimals (number):', Number(decimals));\n                console.log('- Total Supply (raw):', totalSupply.toString());\n                console.log('- Max Supply (raw):', maxSupply.toString());\n                console.log('- Metadata (raw):', metadata);\n                console.log('- Token Price:', metadata[0]);\n                console.log('- Bonus Tiers:', metadata[1]);\n                console.log('- Token Details:', metadata[2]);\n                console.log('- Token Image URL:', metadata[3]);\n                const decimalsNumber = Number(decimals);\n                // Format with proper blockchain decimal precision\n                let formattedTotalSupply;\n                let formattedMaxSupply;\n                if (decimalsNumber === 0) {\n                    // For 0 decimals, show as whole numbers\n                    formattedTotalSupply = totalSupply.toString();\n                    formattedMaxSupply = maxSupply.toString();\n                } else {\n                    // For tokens with decimals, use ethers.formatUnits and preserve decimal precision\n                    const totalSupplyFormatted = ethers__WEBPACK_IMPORTED_MODULE_5__.formatUnits(totalSupply, decimalsNumber);\n                    const maxSupplyFormatted = ethers__WEBPACK_IMPORTED_MODULE_5__.formatUnits(maxSupply, decimalsNumber);\n                    // For blockchain tokens, show with appropriate decimal places\n                    // Remove excessive trailing zeros but keep meaningful precision\n                    formattedTotalSupply = parseFloat(totalSupplyFormatted).toFixed(Math.min(decimalsNumber, 6));\n                    formattedMaxSupply = parseFloat(maxSupplyFormatted).toFixed(Math.min(decimalsNumber, 6));\n                    // Remove trailing zeros after decimal point\n                    formattedTotalSupply = formattedTotalSupply.replace(/\\.?0+$/, '');\n                    formattedMaxSupply = formattedMaxSupply.replace(/\\.?0+$/, '');\n                    // Ensure at least .0 for tokens with decimals\n                    if (!formattedTotalSupply.includes('.')) formattedTotalSupply += '.0';\n                    if (!formattedMaxSupply.includes('.')) formattedMaxSupply += '.0';\n                }\n                console.log('- Total Supply (formatted):', formattedTotalSupply);\n                console.log('- Max Supply (formatted):', formattedMaxSupply);\n                setTokenInfo({\n                    name,\n                    symbol,\n                    version,\n                    totalSupply: formattedTotalSupply,\n                    maxSupply: formattedMaxSupply,\n                    decimals: decimalsNumber,\n                    paused,\n                    metadata: {\n                        tokenPrice: metadata[0],\n                        bonusTiers: metadata[1],\n                        tokenDetails: metadata[2],\n                        tokenImageUrl: metadata[3]\n                    }\n                });\n            } catch (error) {\n                console.error('Error loading token info:', error);\n                setError('Failed to load token information');\n            } finally{\n                setLoading(false);\n            }\n        }\n    }[\"useModularToken.useCallback[loadTokenInfo]\"], [\n        provider,\n        tokenAddress\n    ]);\n    // Load contract addresses\n    const loadContractAddresses = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[loadContractAddresses]\": async ()=>{\n            const contractAddress = tokenAddress || SECURITY_TOKEN_CORE_ADDRESS;\n            if (!provider || !contractAddress) return;\n            try {\n                const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(contractAddress, SecurityTokenCoreABI, provider);\n                // Define module IDs (these are keccak256 hashes of the module names)\n                const moduleIds = {\n                    'Identity Manager': ethers__WEBPACK_IMPORTED_MODULE_6__.keccak256(ethers__WEBPACK_IMPORTED_MODULE_7__.toUtf8Bytes(\"IDENTITY_MANAGER\")),\n                    'Compliance Engine': ethers__WEBPACK_IMPORTED_MODULE_6__.keccak256(ethers__WEBPACK_IMPORTED_MODULE_7__.toUtf8Bytes(\"COMPLIANCE_ENGINE\")),\n                    'Transfer Controller': ethers__WEBPACK_IMPORTED_MODULE_6__.keccak256(ethers__WEBPACK_IMPORTED_MODULE_7__.toUtf8Bytes(\"TRANSFER_CONTROLLER\")),\n                    'Agent Manager': ethers__WEBPACK_IMPORTED_MODULE_6__.keccak256(ethers__WEBPACK_IMPORTED_MODULE_7__.toUtf8Bytes(\"AGENT_MANAGER\")),\n                    'Emergency Manager': ethers__WEBPACK_IMPORTED_MODULE_6__.keccak256(ethers__WEBPACK_IMPORTED_MODULE_7__.toUtf8Bytes(\"EMERGENCY_MANAGER\")),\n                    'KYC Claims Module': ethers__WEBPACK_IMPORTED_MODULE_6__.keccak256(ethers__WEBPACK_IMPORTED_MODULE_7__.toUtf8Bytes(\"KYC_CLAIMS_MODULE\"))\n                };\n                console.log('Fetching module addresses...');\n                // Fetch all module addresses\n                const addresses = {\n                    'Token Contract': contractAddress\n                };\n                for (const [moduleName, moduleId] of Object.entries(moduleIds)){\n                    try {\n                        const moduleAddress = await contract.getModule(moduleId);\n                        if (moduleAddress && moduleAddress !== ethers__WEBPACK_IMPORTED_MODULE_8__.ZeroAddress) {\n                            addresses[moduleName] = moduleAddress;\n                            console.log(\"\".concat(moduleName, \": \").concat(moduleAddress));\n                        } else {\n                            console.log(\"\".concat(moduleName, \": Not registered\"));\n                        }\n                    } catch (error) {\n                        console.warn(\"Error fetching \".concat(moduleName, \" address:\"), error);\n                    }\n                }\n                setContractAddresses(addresses);\n            } catch (error) {\n                console.error('Error loading contract addresses:', error);\n            }\n        }\n    }[\"useModularToken.useCallback[loadContractAddresses]\"], [\n        signer,\n        tokenAddress\n    ]);\n    // Force transfer tokens\n    const forceTransfer = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[forceTransfer]\": async (fromAddress, toAddress, amount)=>{\n            const contractAddress = tokenAddress || SECURITY_TOKEN_CORE_ADDRESS;\n            if (!signer || !contractAddress) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(contractAddress, SecurityTokenCoreABI, signer);\n            try {\n                // Check if user has required role\n                const TRANSFER_MANAGER_ROLE = ethers__WEBPACK_IMPORTED_MODULE_6__.keccak256(ethers__WEBPACK_IMPORTED_MODULE_7__.toUtf8Bytes(\"TRANSFER_MANAGER_ROLE\"));\n                const DEFAULT_ADMIN_ROLE = '0x0000000000000000000000000000000000000000000000000000000000000000';\n                const userAddress = await signer.getAddress();\n                const hasTransferManagerRole = await contract.hasRole(TRANSFER_MANAGER_ROLE, userAddress);\n                const hasAdminRole = await contract.hasRole(DEFAULT_ADMIN_ROLE, userAddress);\n                if (!hasTransferManagerRole && !hasAdminRole) {\n                    throw new Error(\"Wallet \".concat(userAddress, \" does not have TRANSFER_MANAGER_ROLE or DEFAULT_ADMIN_ROLE required for force transfers\"));\n                }\n                console.log('Force transferring tokens:', {\n                    fromAddress,\n                    toAddress,\n                    amount\n                });\n                console.log('User address:', userAddress);\n                console.log('Has TRANSFER_MANAGER_ROLE:', hasTransferManagerRole);\n                console.log('Has DEFAULT_ADMIN_ROLE:', hasAdminRole);\n                // Parse amount based on token decimals\n                const decimals = await contract.decimals();\n                const parsedAmount = ethers__WEBPACK_IMPORTED_MODULE_5__.parseUnits(amount, decimals);\n                console.log('Parsed amount:', parsedAmount.toString());\n                // Estimate gas first\n                let gasEstimate;\n                try {\n                    gasEstimate = await contract.forcedTransfer.estimateGas(fromAddress, toAddress, parsedAmount);\n                    console.log('Gas estimate:', gasEstimate.toString());\n                } catch (gasError) {\n                    console.error('Gas estimation failed:', gasError);\n                    throw new Error(\"Gas estimation failed: \".concat(gasError.message, \". This usually means the transaction would revert.\"));\n                }\n                // Execute the force transfer\n                const gasLimit = gasEstimate + gasEstimate / 10n; // Add 10% buffer\n                const tx = await contract.forcedTransfer(fromAddress, toAddress, parsedAmount, {\n                    gasLimit: gasLimit,\n                    gasPrice: ethers__WEBPACK_IMPORTED_MODULE_5__.parseUnits('30', 'gwei')\n                });\n                console.log('Force transfer transaction sent:', tx.hash);\n                return tx.wait();\n            } catch (error) {\n                console.error('Force transfer error:', error);\n                if (error.code === 'ACTION_REJECTED') {\n                    throw new Error('Transaction was rejected by user');\n                } else if (error.reason) {\n                    throw new Error(\"Contract error: \".concat(error.reason));\n                } else if (error.message.includes('Gas estimation failed')) {\n                    throw error; // Re-throw gas estimation errors as-is\n                } else {\n                    throw new Error(\"Failed to force transfer: \".concat(error.message));\n                }\n            }\n        }\n    }[\"useModularToken.useCallback[forceTransfer]\"], [\n        provider,\n        tokenAddress\n    ]);\n    // Load user roles\n    const loadUserRoles = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[loadUserRoles]\": async ()=>{\n            const contractAddress = tokenAddress || SECURITY_TOKEN_CORE_ADDRESS;\n            if (!provider || !signer || !contractAddress) return;\n            try {\n                const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(contractAddress, SecurityTokenCoreABI, provider);\n                const userAddress = await signer.getAddress();\n                // Define role hashes\n                const DEFAULT_ADMIN_ROLE = '0x0000000000000000000000000000000000000000000000000000000000000000';\n                const AGENT_ROLE = ethers__WEBPACK_IMPORTED_MODULE_6__.keccak256(ethers__WEBPACK_IMPORTED_MODULE_7__.toUtf8Bytes(\"AGENT_ROLE\"));\n                const TRANSFER_MANAGER_ROLE = ethers__WEBPACK_IMPORTED_MODULE_6__.keccak256(ethers__WEBPACK_IMPORTED_MODULE_7__.toUtf8Bytes(\"TRANSFER_MANAGER_ROLE\"));\n                const MODULE_MANAGER_ROLE = ethers__WEBPACK_IMPORTED_MODULE_6__.keccak256(ethers__WEBPACK_IMPORTED_MODULE_7__.toUtf8Bytes(\"MODULE_MANAGER_ROLE\"));\n                console.log('Checking roles for user:', userAddress);\n                // Check all roles\n                const roles = {};\n                try {\n                    roles['DEFAULT_ADMIN_ROLE'] = await contract.hasRole(DEFAULT_ADMIN_ROLE, userAddress);\n                    console.log('DEFAULT_ADMIN_ROLE:', roles['DEFAULT_ADMIN_ROLE']);\n                } catch (error) {\n                    console.warn('Error checking DEFAULT_ADMIN_ROLE:', error);\n                    roles['DEFAULT_ADMIN_ROLE'] = false;\n                }\n                try {\n                    roles['AGENT_ROLE'] = await contract.hasRole(AGENT_ROLE, userAddress);\n                    console.log('AGENT_ROLE:', roles['AGENT_ROLE']);\n                } catch (error) {\n                    console.warn('Error checking AGENT_ROLE:', error);\n                    roles['AGENT_ROLE'] = false;\n                }\n                try {\n                    roles['TRANSFER_MANAGER_ROLE'] = await contract.hasRole(TRANSFER_MANAGER_ROLE, userAddress);\n                    console.log('TRANSFER_MANAGER_ROLE:', roles['TRANSFER_MANAGER_ROLE']);\n                } catch (error) {\n                    console.warn('Error checking TRANSFER_MANAGER_ROLE:', error);\n                    roles['TRANSFER_MANAGER_ROLE'] = false;\n                }\n                try {\n                    roles['MODULE_MANAGER_ROLE'] = await contract.hasRole(MODULE_MANAGER_ROLE, userAddress);\n                    console.log('MODULE_MANAGER_ROLE:', roles['MODULE_MANAGER_ROLE']);\n                } catch (error) {\n                    console.warn('Error checking MODULE_MANAGER_ROLE:', error);\n                    roles['MODULE_MANAGER_ROLE'] = false;\n                }\n                setUserRoles(roles);\n            } catch (error) {\n                console.error('Error loading user roles:', error);\n            }\n        }\n    }[\"useModularToken.useCallback[loadUserRoles]\"], [\n        provider,\n        signer,\n        tokenAddress\n    ]);\n    // Load upgrade information\n    const loadUpgradeInfo = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[loadUpgradeInfo]\": async ()=>{\n            if (!provider || !UPGRADE_MANAGER_ADDRESS) return;\n            try {\n                setLoading(true);\n                const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(UPGRADE_MANAGER_ADDRESS, UpgradeManagerABI, provider);\n                const [emergencyModeActive, registeredModules, upgradeDelay, emergencyModeDuration, pendingUpgradeIds] = await Promise.all([\n                    contract.isEmergencyModeActive(),\n                    contract.getRegisteredModules(),\n                    contract.UPGRADE_DELAY(),\n                    contract.EMERGENCY_MODE_DURATION(),\n                    contract.getPendingUpgradeIds()\n                ]);\n                setUpgradeInfo({\n                    emergencyModeActive,\n                    registeredModules,\n                    upgradeDelay: Number(upgradeDelay),\n                    emergencyModeDuration: Number(emergencyModeDuration)\n                });\n                // Load pending upgrades\n                const pendingUpgradesData = await Promise.all(pendingUpgradeIds.map({\n                    \"useModularToken.useCallback[loadUpgradeInfo]\": async (id)=>{\n                        const upgrade = await contract.pendingUpgrades(id);\n                        return {\n                            upgradeId: id,\n                            moduleId: upgrade.moduleId,\n                            proxy: upgrade.proxy,\n                            newImplementation: upgrade.newImplementation,\n                            executeTime: Number(upgrade.executeTime),\n                            executed: upgrade.executed,\n                            cancelled: upgrade.cancelled,\n                            description: upgrade.description\n                        };\n                    }\n                }[\"useModularToken.useCallback[loadUpgradeInfo]\"]));\n                setPendingUpgrades(pendingUpgradesData);\n                // Load upgrade history for SecurityTokenCore\n                const SECURITY_TOKEN_CORE_ID = ethers__WEBPACK_IMPORTED_MODULE_6__.keccak256(ethers__WEBPACK_IMPORTED_MODULE_7__.toUtf8Bytes(\"SECURITY_TOKEN_CORE\"));\n                const history = await contract.getUpgradeHistory(SECURITY_TOKEN_CORE_ID);\n                setUpgradeHistory(history.map({\n                    \"useModularToken.useCallback[loadUpgradeInfo]\": (record)=>({\n                            oldImplementation: record.oldImplementation,\n                            newImplementation: record.newImplementation,\n                            timestamp: Number(record.timestamp),\n                            executor: record.executor,\n                            version: record.version,\n                            isEmergency: record.isEmergency,\n                            description: record.description\n                        })\n                }[\"useModularToken.useCallback[loadUpgradeInfo]\"]));\n            } catch (error) {\n                console.error('Error loading upgrade info:', error);\n                setError('Failed to load upgrade information');\n            } finally{\n                setLoading(false);\n            }\n        }\n    }[\"useModularToken.useCallback[loadUpgradeInfo]\"], [\n        provider\n    ]);\n    // Mint tokens\n    const mintTokens = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[mintTokens]\": async (address, amount)=>{\n            const contractAddress = tokenAddress || SECURITY_TOKEN_CORE_ADDRESS;\n            if (!signer || !contractAddress) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(contractAddress, SecurityTokenCoreABI, signer);\n            try {\n                const decimals = (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.decimals) || 0;\n                const amountWei = ethers__WEBPACK_IMPORTED_MODULE_5__.parseUnits(amount, decimals);\n                const tx = await contract.mint(address, amountWei, {\n                    gasLimit: 400000\n                });\n                return tx.wait();\n            } catch (error) {\n                console.error('Mint tokens error:', error);\n                if (error.code === 'ACTION_REJECTED') {\n                    throw new Error('Transaction was rejected by user');\n                } else if (error.reason) {\n                    throw new Error(\"Contract error: \".concat(error.reason));\n                } else {\n                    throw new Error(\"Failed to mint tokens: \".concat(error.message));\n                }\n            }\n        }\n    }[\"useModularToken.useCallback[mintTokens]\"], [\n        signer,\n        tokenInfo,\n        tokenAddress\n    ]);\n    // Toggle pause state with multiple retry strategies\n    const togglePause = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[togglePause]\": async ()=>{\n            const contractAddress = tokenAddress || SECURITY_TOKEN_CORE_ADDRESS;\n            if (!signer || !contractAddress) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(contractAddress, SecurityTokenCoreABI, signer);\n            const isPausing = !(tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.paused);\n            const functionName = isPausing ? 'pause' : 'unpause';\n            // Try multiple strategies to handle Amoy testnet issues\n            const strategies = [\n                {\n                    \"useModularToken.useCallback[togglePause]\": // Strategy 1: Standard call with high gas\n                    async ()=>{\n                        const tx = isPausing ? await contract.pause({\n                            gasLimit: 500000,\n                            gasPrice: ethers__WEBPACK_IMPORTED_MODULE_5__.parseUnits('30', 'gwei')\n                        }) : await contract.unpause({\n                            gasLimit: 500000,\n                            gasPrice: ethers__WEBPACK_IMPORTED_MODULE_5__.parseUnits('30', 'gwei')\n                        });\n                        return tx.wait();\n                    }\n                }[\"useModularToken.useCallback[togglePause]\"],\n                {\n                    \"useModularToken.useCallback[togglePause]\": // Strategy 2: Raw transaction approach (like the working script)\n                    async ()=>{\n                        const functionSelector = isPausing ? '0x8456cb59' : '0x3f4ba83a';\n                        const nonce = await signer.getNonce();\n                        const tx = await signer.sendTransaction({\n                            to: contractAddress,\n                            data: functionSelector,\n                            gasLimit: 500000,\n                            gasPrice: ethers__WEBPACK_IMPORTED_MODULE_5__.parseUnits('30', 'gwei'),\n                            nonce: nonce\n                        });\n                        return tx.wait();\n                    }\n                }[\"useModularToken.useCallback[togglePause]\"]\n            ];\n            for(let i = 0; i < strategies.length; i++){\n                try {\n                    console.log(\"Attempting \".concat(functionName, \" strategy \").concat(i + 1, \"...\"));\n                    const result = await strategies[i]();\n                    console.log(\"\".concat(functionName, \" successful with strategy \").concat(i + 1));\n                    return result;\n                } catch (error) {\n                    console.error(\"Strategy \".concat(i + 1, \" failed:\"), error);\n                    if (error.code === 'ACTION_REJECTED') {\n                        throw new Error('Transaction was rejected by user');\n                    }\n                    // If this is the last strategy, throw a comprehensive error\n                    if (i === strategies.length - 1) {\n                        var _error_error;\n                        if (error.code === 'UNKNOWN_ERROR' && ((_error_error = error.error) === null || _error_error === void 0 ? void 0 : _error_error.code) === -32603) {\n                            throw new Error(\"Amoy testnet RPC error: The network is experiencing issues. Please try again in a few minutes, or use a different RPC endpoint.\");\n                        } else if (error.reason) {\n                            throw new Error(\"Contract error: \".concat(error.reason));\n                        } else {\n                            throw new Error(\"Failed to \".concat(functionName, \" token after trying multiple methods: \").concat(error.message));\n                        }\n                    }\n                    // Wait a bit before trying the next strategy\n                    await new Promise({\n                        \"useModularToken.useCallback[togglePause]\": (resolve)=>setTimeout(resolve, 1000)\n                    }[\"useModularToken.useCallback[togglePause]\"]);\n                }\n            }\n        }\n    }[\"useModularToken.useCallback[togglePause]\"], [\n        signer,\n        tokenInfo,\n        tokenAddress\n    ]);\n    // Schedule upgrade\n    const scheduleUpgrade = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[scheduleUpgrade]\": async (implementationAddress, description)=>{\n            if (!signer || !UPGRADE_MANAGER_ADDRESS) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(UPGRADE_MANAGER_ADDRESS, UpgradeManagerABI, signer);\n            const SECURITY_TOKEN_CORE_ID = ethers__WEBPACK_IMPORTED_MODULE_6__.keccak256(ethers__WEBPACK_IMPORTED_MODULE_7__.toUtf8Bytes(\"SECURITY_TOKEN_CORE\"));\n            const tx = await contract.scheduleUpgrade(SECURITY_TOKEN_CORE_ID, implementationAddress, description);\n            return tx.wait();\n        }\n    }[\"useModularToken.useCallback[scheduleUpgrade]\"], [\n        signer\n    ]);\n    // Execute upgrade\n    const executeUpgrade = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[executeUpgrade]\": async (upgradeId)=>{\n            if (!signer || !UPGRADE_MANAGER_ADDRESS) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(UPGRADE_MANAGER_ADDRESS, UpgradeManagerABI, signer);\n            const tx = await contract.executeUpgrade(upgradeId);\n            return tx.wait();\n        }\n    }[\"useModularToken.useCallback[executeUpgrade]\"], [\n        signer\n    ]);\n    // Emergency upgrade\n    const emergencyUpgrade = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[emergencyUpgrade]\": async (implementationAddress, description)=>{\n            if (!signer || !UPGRADE_MANAGER_ADDRESS) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(UPGRADE_MANAGER_ADDRESS, UpgradeManagerABI, signer);\n            const SECURITY_TOKEN_CORE_ID = ethers__WEBPACK_IMPORTED_MODULE_6__.keccak256(ethers__WEBPACK_IMPORTED_MODULE_7__.toUtf8Bytes(\"SECURITY_TOKEN_CORE\"));\n            const tx = await contract.emergencyUpgrade(SECURITY_TOKEN_CORE_ID, implementationAddress, description);\n            return tx.wait();\n        }\n    }[\"useModularToken.useCallback[emergencyUpgrade]\"], [\n        signer\n    ]);\n    // Toggle emergency mode\n    const toggleEmergencyMode = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[toggleEmergencyMode]\": async ()=>{\n            if (!signer || !UPGRADE_MANAGER_ADDRESS) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(UPGRADE_MANAGER_ADDRESS, UpgradeManagerABI, signer);\n            const tx = (upgradeInfo === null || upgradeInfo === void 0 ? void 0 : upgradeInfo.emergencyModeActive) ? await contract.deactivateEmergencyMode() : await contract.activateEmergencyMode();\n            return tx.wait();\n        }\n    }[\"useModularToken.useCallback[toggleEmergencyMode]\"], [\n        signer,\n        upgradeInfo\n    ]);\n    // Cancel upgrade\n    const cancelUpgrade = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[cancelUpgrade]\": async (upgradeId)=>{\n            if (!signer || !UPGRADE_MANAGER_ADDRESS) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(UPGRADE_MANAGER_ADDRESS, UpgradeManagerABI, signer);\n            const tx = await contract.cancelUpgrade(upgradeId);\n            return tx.wait();\n        }\n    }[\"useModularToken.useCallback[cancelUpgrade]\"], [\n        signer\n    ]);\n    // Update token price (now using direct updateTokenPrice function)\n    const updateTokenPrice = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[updateTokenPrice]\": async (newPrice)=>{\n            const contractAddress = tokenAddress || SECURITY_TOKEN_CORE_ADDRESS;\n            if (!signer || !contractAddress) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(contractAddress, SecurityTokenCoreABI, signer);\n            try {\n                // First check if user has admin role\n                const DEFAULT_ADMIN_ROLE = '0x0000000000000000000000000000000000000000000000000000000000000000';\n                const userAddress = await signer.getAddress();\n                const hasAdminRole = await contract.hasRole(DEFAULT_ADMIN_ROLE, userAddress);\n                if (!hasAdminRole) {\n                    throw new Error(\"Wallet \".concat(userAddress, \" does not have DEFAULT_ADMIN_ROLE on this token contract\"));\n                }\n                console.log('Updating token price from wallet:', userAddress);\n                console.log('New price:', newPrice);\n                console.log('Contract address:', contractAddress);\n                // Try direct updateTokenPrice first (for upgraded contracts)\n                let gasEstimate;\n                try {\n                    gasEstimate = await contract.updateTokenPrice.estimateGas(newPrice);\n                    console.log('Gas estimate (direct):', gasEstimate.toString());\n                    // Execute with estimated gas + buffer\n                    const gasLimit = gasEstimate + gasEstimate / 10n; // Add 10% buffer\n                    const tx = await contract.updateTokenPrice(newPrice, {\n                        gasLimit: gasLimit,\n                        gasPrice: ethers__WEBPACK_IMPORTED_MODULE_5__.parseUnits('30', 'gwei')\n                    });\n                    console.log('Transaction sent (direct):', tx.hash);\n                    return tx.wait();\n                } catch (directError) {\n                    console.log('Direct updateTokenPrice failed, trying updateTokenMetadata fallback:', directError.message);\n                    // Fallback to updateTokenMetadata for older contracts\n                    const currentMetadata = await contract.getTokenMetadata();\n                    const currentBonusTiers = currentMetadata[1];\n                    const currentDetails = currentMetadata[2];\n                    gasEstimate = await contract.updateTokenMetadata.estimateGas(newPrice, currentBonusTiers, currentDetails);\n                    console.log('Gas estimate (fallback):', gasEstimate.toString());\n                    const gasLimit = gasEstimate + gasEstimate / 10n;\n                    const tx = await contract.updateTokenMetadata(newPrice, currentBonusTiers, currentDetails, {\n                        gasLimit: gasLimit,\n                        gasPrice: ethers__WEBPACK_IMPORTED_MODULE_5__.parseUnits('30', 'gwei')\n                    });\n                    console.log('Transaction sent (fallback):', tx.hash);\n                    return tx.wait();\n                }\n            } catch (error) {\n                console.error('Update token price error:', error);\n                if (error.code === 'ACTION_REJECTED') {\n                    throw new Error('Transaction was rejected by user');\n                } else if (error.reason) {\n                    throw new Error(\"Contract error: \".concat(error.reason));\n                } else if (error.message.includes('Gas estimation failed')) {\n                    throw error; // Re-throw gas estimation errors as-is\n                } else {\n                    throw new Error(\"Failed to update token price: \".concat(error.message));\n                }\n            }\n        }\n    }[\"useModularToken.useCallback[updateTokenPrice]\"], [\n        signer,\n        tokenAddress\n    ]);\n    // Update bonus tiers (now using direct updateBonusTiers function)\n    const updateBonusTiers = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[updateBonusTiers]\": async (newBonusTiers)=>{\n            const contractAddress = tokenAddress || SECURITY_TOKEN_CORE_ADDRESS;\n            if (!signer || !contractAddress) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(contractAddress, SecurityTokenCoreABI, signer);\n            try {\n                // Try direct updateBonusTiers first (for upgraded contracts)\n                try {\n                    const tx = await contract.updateBonusTiers(newBonusTiers, {\n                        gasLimit: 300000\n                    });\n                    console.log('Bonus tiers updated (direct):', tx.hash);\n                    return tx.wait();\n                } catch (directError) {\n                    console.log('Direct updateBonusTiers failed, trying updateTokenMetadata fallback:', directError.message);\n                    // Fallback to updateTokenMetadata for older contracts\n                    const currentMetadata = await contract.getTokenMetadata();\n                    const currentPrice = currentMetadata[0];\n                    const currentDetails = currentMetadata[2];\n                    const tx = await contract.updateTokenMetadata(currentPrice, newBonusTiers, currentDetails, {\n                        gasLimit: 300000\n                    });\n                    console.log('Bonus tiers updated (fallback):', tx.hash);\n                    return tx.wait();\n                }\n            } catch (error) {\n                console.error('Update bonus tiers error:', error);\n                if (error.code === 'ACTION_REJECTED') {\n                    throw new Error('Transaction was rejected by user');\n                } else if (error.reason) {\n                    throw new Error(\"Contract error: \".concat(error.reason));\n                } else {\n                    throw new Error(\"Failed to update bonus tiers: \".concat(error.message));\n                }\n            }\n        }\n    }[\"useModularToken.useCallback[updateBonusTiers]\"], [\n        signer,\n        tokenAddress\n    ]);\n    // Update max supply\n    const updateMaxSupply = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[updateMaxSupply]\": async (newMaxSupply)=>{\n            const contractAddress = tokenAddress || SECURITY_TOKEN_CORE_ADDRESS;\n            if (!signer || !contractAddress) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(contractAddress, SecurityTokenCoreABI, signer);\n            try {\n                const maxSupplyWei = ethers__WEBPACK_IMPORTED_MODULE_5__.parseUnits(newMaxSupply, (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.decimals) || 0);\n                const tx = await contract.updateMaxSupply(maxSupplyWei, {\n                    gasLimit: 300000\n                });\n                return tx.wait();\n            } catch (error) {\n                console.error('Update max supply error:', error);\n                if (error.code === 'ACTION_REJECTED') {\n                    throw new Error('Transaction was rejected by user');\n                } else if (error.reason) {\n                    throw new Error(\"Contract error: \".concat(error.reason));\n                } else {\n                    throw new Error(\"Failed to update max supply: \".concat(error.message));\n                }\n            }\n        }\n    }[\"useModularToken.useCallback[updateMaxSupply]\"], [\n        signer,\n        tokenInfo,\n        tokenAddress\n    ]);\n    // Add to whitelist with identity registration\n    const addToWhitelist = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[addToWhitelist]\": async (address)=>{\n            const contractAddress = tokenAddress || SECURITY_TOKEN_CORE_ADDRESS;\n            if (!signer || !contractAddress) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(contractAddress, SecurityTokenCoreABI, signer);\n            try {\n                // First check if the address is already verified/registered\n                const isVerified = await contract.isVerified(address);\n                if (!isVerified) {\n                    // Need to register identity first - use the API approach for this\n                    throw new Error('Address must be registered first. Please use the API method or register the identity manually.');\n                }\n                // Now try to add to whitelist\n                const tx = await contract.addToWhitelist(address, {\n                    gasLimit: 400000,\n                    gasPrice: ethers__WEBPACK_IMPORTED_MODULE_5__.parseUnits('30', 'gwei')\n                });\n                return tx.wait();\n            } catch (error) {\n                console.error('Add to whitelist error:', error);\n                if (error.code === 'ACTION_REJECTED') {\n                    throw new Error('Transaction was rejected by user');\n                } else if (error.reason) {\n                    throw new Error(\"Contract error: \".concat(error.reason));\n                } else if (error.message.includes('identity not registered')) {\n                    throw new Error('Address must be registered first. Please use the API method which handles registration automatically.');\n                } else {\n                    throw new Error(\"Failed to add address to whitelist: \".concat(error.message));\n                }\n            }\n        }\n    }[\"useModularToken.useCallback[addToWhitelist]\"], [\n        signer,\n        tokenAddress\n    ]);\n    // Remove from whitelist\n    const removeFromWhitelist = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[removeFromWhitelist]\": async (address)=>{\n            const contractAddress = tokenAddress || SECURITY_TOKEN_CORE_ADDRESS;\n            if (!signer || !contractAddress) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(contractAddress, SecurityTokenCoreABI, signer);\n            try {\n                const tx = await contract.removeFromWhitelist(address, {\n                    gasLimit: 300000\n                });\n                return tx.wait();\n            } catch (error) {\n                console.error('Remove from whitelist error:', error);\n                if (error.code === 'ACTION_REJECTED') {\n                    throw new Error('Transaction was rejected by user');\n                } else if (error.reason) {\n                    throw new Error(\"Contract error: \".concat(error.reason));\n                } else {\n                    throw new Error(\"Failed to remove address from whitelist: \".concat(error.message));\n                }\n            }\n        }\n    }[\"useModularToken.useCallback[removeFromWhitelist]\"], [\n        signer,\n        tokenAddress\n    ]);\n    // Refresh all data\n    const refreshData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[refreshData]\": async ()=>{\n            await Promise.all([\n                loadTokenInfo(),\n                loadContractAddresses(),\n                loadUserRoles(),\n                loadUpgradeInfo()\n            ]);\n        }\n    }[\"useModularToken.useCallback[refreshData]\"], [\n        loadTokenInfo,\n        loadContractAddresses,\n        loadUserRoles,\n        loadUpgradeInfo\n    ]);\n    // Initialize on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useModularToken.useEffect\": ()=>{\n            initializeProvider();\n        }\n    }[\"useModularToken.useEffect\"], [\n        initializeProvider\n    ]);\n    // Load data when provider is ready\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useModularToken.useEffect\": ()=>{\n            if (provider && signer) {\n                refreshData();\n            }\n        }\n    }[\"useModularToken.useEffect\"], [\n        provider,\n        signer,\n        refreshData\n    ]);\n    return {\n        // State\n        provider,\n        signer,\n        tokenInfo,\n        contractAddresses,\n        userRoles,\n        upgradeInfo,\n        pendingUpgrades,\n        upgradeHistory,\n        loading,\n        error,\n        // Actions\n        initializeProvider,\n        loadTokenInfo,\n        loadContractAddresses,\n        loadUserRoles,\n        loadUpgradeInfo,\n        mintTokens,\n        togglePause,\n        scheduleUpgrade,\n        executeUpgrade,\n        emergencyUpgrade,\n        toggleEmergencyMode,\n        cancelUpgrade,\n        refreshData,\n        // Admin Functions\n        updateTokenPrice,\n        updateBonusTiers,\n        updateMaxSupply,\n        addToWhitelist,\n        removeFromWhitelist,\n        // Utilities\n        setError,\n        setLoading\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useModularToken.ts\n"));

/***/ })

});