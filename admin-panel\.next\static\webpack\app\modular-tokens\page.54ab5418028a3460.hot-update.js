"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/modular-tokens/page",{

/***/ "(app-pages-browser)/./src/hooks/useModularToken.ts":
/*!**************************************!*\
  !*** ./src/hooks/useModularToken.ts ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useModularToken: () => (/* binding */ useModularToken)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/providers/provider-browser.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/contract/contract.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/utils/units.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/crypto/keccak.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/utils/utf8.js\");\n/* harmony import */ var _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/contracts/SecurityTokenCore.json */ \"(app-pages-browser)/./src/contracts/SecurityTokenCore.json\");\n/* harmony import */ var _contracts_UpgradeManager_json__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contracts/UpgradeManager.json */ \"(app-pages-browser)/./src/contracts/UpgradeManager.json\");\n/* __next_internal_client_entry_do_not_use__ useModularToken auto */ \n\n// Import ABIs - extract the abi property from the JSON artifacts\n\n\n// Extract ABIs from artifacts\nconst SecurityTokenCoreABI = _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_1__.abi;\nconst UpgradeManagerABI = _contracts_UpgradeManager_json__WEBPACK_IMPORTED_MODULE_2__.abi;\n// Contract addresses from environment\nconst SECURITY_TOKEN_CORE_ADDRESS = \"******************************************\";\nconst UPGRADE_MANAGER_ADDRESS = \"0xb7a53dC340C2E5e823002B174629e2a44B8ed815\";\nfunction useModularToken(tokenAddress) {\n    const [provider, setProvider] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [signer, setSigner] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [tokenInfo, setTokenInfo] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [upgradeInfo, setUpgradeInfo] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [pendingUpgrades, setPendingUpgrades] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [upgradeHistory, setUpgradeHistory] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    // Initialize provider and signer\n    const initializeProvider = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[initializeProvider]\": async ()=>{\n            try {\n                if ( true && window.ethereum) {\n                    const provider = new ethers__WEBPACK_IMPORTED_MODULE_3__.BrowserProvider(window.ethereum);\n                    await provider.send('eth_requestAccounts', []);\n                    const signer = await provider.getSigner();\n                    setProvider(provider);\n                    setSigner(signer);\n                    return {\n                        provider,\n                        signer\n                    };\n                } else {\n                    throw new Error('MetaMask not found');\n                }\n            } catch (error) {\n                console.error('Error initializing provider:', error);\n                setError('Failed to connect to wallet');\n                return null;\n            }\n        }\n    }[\"useModularToken.useCallback[initializeProvider]\"], []);\n    // Load token information\n    const loadTokenInfo = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[loadTokenInfo]\": async ()=>{\n            const contractAddress = tokenAddress || SECURITY_TOKEN_CORE_ADDRESS;\n            if (!provider || !contractAddress) return;\n            try {\n                setLoading(true);\n                const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(contractAddress, SecurityTokenCoreABI, provider);\n                const [name, symbol, version, totalSupply, maxSupply, decimals, paused, metadata] = await Promise.all([\n                    contract.name(),\n                    contract.symbol(),\n                    contract.version(),\n                    contract.totalSupply(),\n                    contract.maxSupply(),\n                    contract.decimals(),\n                    contract.paused(),\n                    contract.getTokenMetadata()\n                ]);\n                setTokenInfo({\n                    name,\n                    symbol,\n                    version,\n                    totalSupply: ethers__WEBPACK_IMPORTED_MODULE_5__.formatUnits(totalSupply, decimals),\n                    maxSupply: ethers__WEBPACK_IMPORTED_MODULE_5__.formatUnits(maxSupply, decimals),\n                    decimals,\n                    paused,\n                    metadata: {\n                        tokenPrice: metadata[0],\n                        bonusTiers: metadata[1],\n                        tokenDetails: metadata[2],\n                        tokenImageUrl: metadata[3]\n                    }\n                });\n            } catch (error) {\n                console.error('Error loading token info:', error);\n                setError('Failed to load token information');\n            } finally{\n                setLoading(false);\n            }\n        }\n    }[\"useModularToken.useCallback[loadTokenInfo]\"], [\n        provider,\n        tokenAddress\n    ]);\n    // Load upgrade information\n    const loadUpgradeInfo = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[loadUpgradeInfo]\": async ()=>{\n            if (!provider || !UPGRADE_MANAGER_ADDRESS) return;\n            try {\n                setLoading(true);\n                const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(UPGRADE_MANAGER_ADDRESS, UpgradeManagerABI, provider);\n                const [emergencyModeActive, registeredModules, upgradeDelay, emergencyModeDuration, pendingUpgradeIds] = await Promise.all([\n                    contract.isEmergencyModeActive(),\n                    contract.getRegisteredModules(),\n                    contract.UPGRADE_DELAY(),\n                    contract.EMERGENCY_MODE_DURATION(),\n                    contract.getPendingUpgradeIds()\n                ]);\n                setUpgradeInfo({\n                    emergencyModeActive,\n                    registeredModules,\n                    upgradeDelay: Number(upgradeDelay),\n                    emergencyModeDuration: Number(emergencyModeDuration)\n                });\n                // Load pending upgrades\n                const pendingUpgradesData = await Promise.all(pendingUpgradeIds.map({\n                    \"useModularToken.useCallback[loadUpgradeInfo]\": async (id)=>{\n                        const upgrade = await contract.pendingUpgrades(id);\n                        return {\n                            upgradeId: id,\n                            moduleId: upgrade.moduleId,\n                            proxy: upgrade.proxy,\n                            newImplementation: upgrade.newImplementation,\n                            executeTime: Number(upgrade.executeTime),\n                            executed: upgrade.executed,\n                            cancelled: upgrade.cancelled,\n                            description: upgrade.description\n                        };\n                    }\n                }[\"useModularToken.useCallback[loadUpgradeInfo]\"]));\n                setPendingUpgrades(pendingUpgradesData);\n                // Load upgrade history for SecurityTokenCore\n                const SECURITY_TOKEN_CORE_ID = ethers__WEBPACK_IMPORTED_MODULE_6__.keccak256(ethers__WEBPACK_IMPORTED_MODULE_7__.toUtf8Bytes(\"SECURITY_TOKEN_CORE\"));\n                const history = await contract.getUpgradeHistory(SECURITY_TOKEN_CORE_ID);\n                setUpgradeHistory(history.map({\n                    \"useModularToken.useCallback[loadUpgradeInfo]\": (record)=>({\n                            oldImplementation: record.oldImplementation,\n                            newImplementation: record.newImplementation,\n                            timestamp: Number(record.timestamp),\n                            executor: record.executor,\n                            version: record.version,\n                            isEmergency: record.isEmergency,\n                            description: record.description\n                        })\n                }[\"useModularToken.useCallback[loadUpgradeInfo]\"]));\n            } catch (error) {\n                console.error('Error loading upgrade info:', error);\n                setError('Failed to load upgrade information');\n            } finally{\n                setLoading(false);\n            }\n        }\n    }[\"useModularToken.useCallback[loadUpgradeInfo]\"], [\n        provider\n    ]);\n    // Mint tokens\n    const mintTokens = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[mintTokens]\": async (address, amount)=>{\n            const contractAddress = tokenAddress || SECURITY_TOKEN_CORE_ADDRESS;\n            if (!signer || !contractAddress) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(contractAddress, SecurityTokenCoreABI, signer);\n            try {\n                const decimals = (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.decimals) || 0;\n                const amountWei = ethers__WEBPACK_IMPORTED_MODULE_5__.parseUnits(amount, decimals);\n                const tx = await contract.mint(address, amountWei, {\n                    gasLimit: 400000\n                });\n                return tx.wait();\n            } catch (error) {\n                console.error('Mint tokens error:', error);\n                if (error.code === 'ACTION_REJECTED') {\n                    throw new Error('Transaction was rejected by user');\n                } else if (error.reason) {\n                    throw new Error(\"Contract error: \".concat(error.reason));\n                } else {\n                    throw new Error(\"Failed to mint tokens: \".concat(error.message));\n                }\n            }\n        }\n    }[\"useModularToken.useCallback[mintTokens]\"], [\n        signer,\n        tokenInfo,\n        tokenAddress\n    ]);\n    // Toggle pause state with multiple retry strategies\n    const togglePause = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[togglePause]\": async ()=>{\n            const contractAddress = tokenAddress || SECURITY_TOKEN_CORE_ADDRESS;\n            if (!signer || !contractAddress) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(contractAddress, SecurityTokenCoreABI, signer);\n            const isPausing = !(tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.paused);\n            const functionName = isPausing ? 'pause' : 'unpause';\n            // Try multiple strategies to handle Amoy testnet issues\n            const strategies = [\n                {\n                    \"useModularToken.useCallback[togglePause]\": // Strategy 1: Standard call with high gas\n                    async ()=>{\n                        const tx = isPausing ? await contract.pause({\n                            gasLimit: 500000,\n                            gasPrice: ethers__WEBPACK_IMPORTED_MODULE_5__.parseUnits('30', 'gwei')\n                        }) : await contract.unpause({\n                            gasLimit: 500000,\n                            gasPrice: ethers__WEBPACK_IMPORTED_MODULE_5__.parseUnits('30', 'gwei')\n                        });\n                        return tx.wait();\n                    }\n                }[\"useModularToken.useCallback[togglePause]\"],\n                {\n                    \"useModularToken.useCallback[togglePause]\": // Strategy 2: Raw transaction approach (like the working script)\n                    async ()=>{\n                        const functionSelector = isPausing ? '0x8456cb59' : '0x3f4ba83a';\n                        const nonce = await signer.getNonce();\n                        const tx = await signer.sendTransaction({\n                            to: contractAddress,\n                            data: functionSelector,\n                            gasLimit: 500000,\n                            gasPrice: ethers__WEBPACK_IMPORTED_MODULE_5__.parseUnits('30', 'gwei'),\n                            nonce: nonce\n                        });\n                        return tx.wait();\n                    }\n                }[\"useModularToken.useCallback[togglePause]\"]\n            ];\n            for(let i = 0; i < strategies.length; i++){\n                try {\n                    console.log(\"Attempting \".concat(functionName, \" strategy \").concat(i + 1, \"...\"));\n                    const result = await strategies[i]();\n                    console.log(\"\".concat(functionName, \" successful with strategy \").concat(i + 1));\n                    return result;\n                } catch (error) {\n                    console.error(\"Strategy \".concat(i + 1, \" failed:\"), error);\n                    if (error.code === 'ACTION_REJECTED') {\n                        throw new Error('Transaction was rejected by user');\n                    }\n                    // If this is the last strategy, throw a comprehensive error\n                    if (i === strategies.length - 1) {\n                        var _error_error;\n                        if (error.code === 'UNKNOWN_ERROR' && ((_error_error = error.error) === null || _error_error === void 0 ? void 0 : _error_error.code) === -32603) {\n                            throw new Error(\"Amoy testnet RPC error: The network is experiencing issues. Please try again in a few minutes, or use a different RPC endpoint.\");\n                        } else if (error.reason) {\n                            throw new Error(\"Contract error: \".concat(error.reason));\n                        } else {\n                            throw new Error(\"Failed to \".concat(functionName, \" token after trying multiple methods: \").concat(error.message));\n                        }\n                    }\n                    // Wait a bit before trying the next strategy\n                    await new Promise({\n                        \"useModularToken.useCallback[togglePause]\": (resolve)=>setTimeout(resolve, 1000)\n                    }[\"useModularToken.useCallback[togglePause]\"]);\n                }\n            }\n        }\n    }[\"useModularToken.useCallback[togglePause]\"], [\n        signer,\n        tokenInfo,\n        tokenAddress\n    ]);\n    // Schedule upgrade\n    const scheduleUpgrade = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[scheduleUpgrade]\": async (implementationAddress, description)=>{\n            if (!signer || !UPGRADE_MANAGER_ADDRESS) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(UPGRADE_MANAGER_ADDRESS, UpgradeManagerABI, signer);\n            const SECURITY_TOKEN_CORE_ID = ethers__WEBPACK_IMPORTED_MODULE_6__.keccak256(ethers__WEBPACK_IMPORTED_MODULE_7__.toUtf8Bytes(\"SECURITY_TOKEN_CORE\"));\n            const tx = await contract.scheduleUpgrade(SECURITY_TOKEN_CORE_ID, implementationAddress, description);\n            return tx.wait();\n        }\n    }[\"useModularToken.useCallback[scheduleUpgrade]\"], [\n        signer\n    ]);\n    // Execute upgrade\n    const executeUpgrade = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[executeUpgrade]\": async (upgradeId)=>{\n            if (!signer || !UPGRADE_MANAGER_ADDRESS) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(UPGRADE_MANAGER_ADDRESS, UpgradeManagerABI, signer);\n            const tx = await contract.executeUpgrade(upgradeId);\n            return tx.wait();\n        }\n    }[\"useModularToken.useCallback[executeUpgrade]\"], [\n        signer\n    ]);\n    // Emergency upgrade\n    const emergencyUpgrade = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[emergencyUpgrade]\": async (implementationAddress, description)=>{\n            if (!signer || !UPGRADE_MANAGER_ADDRESS) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(UPGRADE_MANAGER_ADDRESS, UpgradeManagerABI, signer);\n            const SECURITY_TOKEN_CORE_ID = ethers__WEBPACK_IMPORTED_MODULE_6__.keccak256(ethers__WEBPACK_IMPORTED_MODULE_7__.toUtf8Bytes(\"SECURITY_TOKEN_CORE\"));\n            const tx = await contract.emergencyUpgrade(SECURITY_TOKEN_CORE_ID, implementationAddress, description);\n            return tx.wait();\n        }\n    }[\"useModularToken.useCallback[emergencyUpgrade]\"], [\n        signer\n    ]);\n    // Toggle emergency mode\n    const toggleEmergencyMode = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[toggleEmergencyMode]\": async ()=>{\n            if (!signer || !UPGRADE_MANAGER_ADDRESS) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(UPGRADE_MANAGER_ADDRESS, UpgradeManagerABI, signer);\n            const tx = (upgradeInfo === null || upgradeInfo === void 0 ? void 0 : upgradeInfo.emergencyModeActive) ? await contract.deactivateEmergencyMode() : await contract.activateEmergencyMode();\n            return tx.wait();\n        }\n    }[\"useModularToken.useCallback[toggleEmergencyMode]\"], [\n        signer,\n        upgradeInfo\n    ]);\n    // Cancel upgrade\n    const cancelUpgrade = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[cancelUpgrade]\": async (upgradeId)=>{\n            if (!signer || !UPGRADE_MANAGER_ADDRESS) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(UPGRADE_MANAGER_ADDRESS, UpgradeManagerABI, signer);\n            const tx = await contract.cancelUpgrade(upgradeId);\n            return tx.wait();\n        }\n    }[\"useModularToken.useCallback[cancelUpgrade]\"], [\n        signer\n    ]);\n    // Update token price\n    const updateTokenPrice = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[updateTokenPrice]\": async (newPrice)=>{\n            const contractAddress = tokenAddress || SECURITY_TOKEN_CORE_ADDRESS;\n            if (!signer || !contractAddress) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(contractAddress, SecurityTokenCoreABI, signer);\n            try {\n                const tx = await contract.updateTokenPrice(newPrice, {\n                    gasLimit: 300000\n                });\n                return tx.wait();\n            } catch (error) {\n                console.error('Update token price error:', error);\n                if (error.code === 'ACTION_REJECTED') {\n                    throw new Error('Transaction was rejected by user');\n                } else if (error.reason) {\n                    throw new Error(\"Contract error: \".concat(error.reason));\n                } else {\n                    throw new Error(\"Failed to update token price: \".concat(error.message));\n                }\n            }\n        }\n    }[\"useModularToken.useCallback[updateTokenPrice]\"], [\n        signer,\n        tokenAddress\n    ]);\n    // Update bonus tiers\n    const updateBonusTiers = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[updateBonusTiers]\": async (newBonusTiers)=>{\n            const contractAddress = tokenAddress || SECURITY_TOKEN_CORE_ADDRESS;\n            if (!signer || !contractAddress) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(contractAddress, SecurityTokenCoreABI, signer);\n            try {\n                const tx = await contract.updateBonusTiers(newBonusTiers, {\n                    gasLimit: 300000\n                });\n                return tx.wait();\n            } catch (error) {\n                console.error('Update bonus tiers error:', error);\n                if (error.code === 'ACTION_REJECTED') {\n                    throw new Error('Transaction was rejected by user');\n                } else if (error.reason) {\n                    throw new Error(\"Contract error: \".concat(error.reason));\n                } else {\n                    throw new Error(\"Failed to update bonus tiers: \".concat(error.message));\n                }\n            }\n        }\n    }[\"useModularToken.useCallback[updateBonusTiers]\"], [\n        signer,\n        tokenAddress\n    ]);\n    // Update max supply\n    const updateMaxSupply = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[updateMaxSupply]\": async (newMaxSupply)=>{\n            const contractAddress = tokenAddress || SECURITY_TOKEN_CORE_ADDRESS;\n            if (!signer || !contractAddress) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(contractAddress, SecurityTokenCoreABI, signer);\n            try {\n                const maxSupplyWei = ethers__WEBPACK_IMPORTED_MODULE_5__.parseUnits(newMaxSupply, (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.decimals) || 0);\n                const tx = await contract.updateMaxSupply(maxSupplyWei, {\n                    gasLimit: 300000\n                });\n                return tx.wait();\n            } catch (error) {\n                console.error('Update max supply error:', error);\n                if (error.code === 'ACTION_REJECTED') {\n                    throw new Error('Transaction was rejected by user');\n                } else if (error.reason) {\n                    throw new Error(\"Contract error: \".concat(error.reason));\n                } else {\n                    throw new Error(\"Failed to update max supply: \".concat(error.message));\n                }\n            }\n        }\n    }[\"useModularToken.useCallback[updateMaxSupply]\"], [\n        signer,\n        tokenInfo,\n        tokenAddress\n    ]);\n    // Add to whitelist\n    const addToWhitelist = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[addToWhitelist]\": async (address)=>{\n            const contractAddress = tokenAddress || SECURITY_TOKEN_CORE_ADDRESS;\n            if (!signer || !contractAddress) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(contractAddress, SecurityTokenCoreABI, signer);\n            try {\n                const tx = await contract.addToWhitelist(address, {\n                    gasLimit: 300000\n                });\n                return tx.wait();\n            } catch (error) {\n                console.error('Add to whitelist error:', error);\n                if (error.code === 'ACTION_REJECTED') {\n                    throw new Error('Transaction was rejected by user');\n                } else if (error.reason) {\n                    throw new Error(\"Contract error: \".concat(error.reason));\n                } else {\n                    throw new Error(\"Failed to add address to whitelist: \".concat(error.message));\n                }\n            }\n        }\n    }[\"useModularToken.useCallback[addToWhitelist]\"], [\n        signer,\n        tokenAddress\n    ]);\n    // Remove from whitelist\n    const removeFromWhitelist = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[removeFromWhitelist]\": async (address)=>{\n            const contractAddress = tokenAddress || SECURITY_TOKEN_CORE_ADDRESS;\n            if (!signer || !contractAddress) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(contractAddress, SecurityTokenCoreABI, signer);\n            try {\n                const tx = await contract.removeFromWhitelist(address, {\n                    gasLimit: 300000\n                });\n                return tx.wait();\n            } catch (error) {\n                console.error('Remove from whitelist error:', error);\n                if (error.code === 'ACTION_REJECTED') {\n                    throw new Error('Transaction was rejected by user');\n                } else if (error.reason) {\n                    throw new Error(\"Contract error: \".concat(error.reason));\n                } else {\n                    throw new Error(\"Failed to remove address from whitelist: \".concat(error.message));\n                }\n            }\n        }\n    }[\"useModularToken.useCallback[removeFromWhitelist]\"], [\n        signer,\n        tokenAddress\n    ]);\n    // Refresh all data\n    const refreshData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[refreshData]\": async ()=>{\n            await Promise.all([\n                loadTokenInfo(),\n                loadUpgradeInfo()\n            ]);\n        }\n    }[\"useModularToken.useCallback[refreshData]\"], [\n        loadTokenInfo,\n        loadUpgradeInfo\n    ]);\n    // Initialize on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useModularToken.useEffect\": ()=>{\n            initializeProvider();\n        }\n    }[\"useModularToken.useEffect\"], [\n        initializeProvider\n    ]);\n    // Load data when provider is ready\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useModularToken.useEffect\": ()=>{\n            if (provider && signer) {\n                refreshData();\n            }\n        }\n    }[\"useModularToken.useEffect\"], [\n        provider,\n        signer,\n        refreshData\n    ]);\n    return {\n        // State\n        provider,\n        signer,\n        tokenInfo,\n        upgradeInfo,\n        pendingUpgrades,\n        upgradeHistory,\n        loading,\n        error,\n        // Actions\n        initializeProvider,\n        loadTokenInfo,\n        loadUpgradeInfo,\n        mintTokens,\n        togglePause,\n        scheduleUpgrade,\n        executeUpgrade,\n        emergencyUpgrade,\n        toggleEmergencyMode,\n        cancelUpgrade,\n        refreshData,\n        // Admin Functions\n        updateTokenPrice,\n        updateBonusTiers,\n        updateMaxSupply,\n        addToWhitelist,\n        removeFromWhitelist,\n        // Utilities\n        setError,\n        setLoading\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useModularToken.ts\n"));

/***/ })

});