import { NextRequest, NextResponse } from 'next/server';
import { ethers } from 'ethers';
import { prisma } from '../../../../../lib/prisma';

// Import ABI
import SecurityTokenCoreArtifact from '../../../../../contracts/SecurityTokenCore.json';

// Extract ABI from artifact
const SecurityTokenCoreABI = SecurityTokenCoreArtifact.abi;

// Network configuration
const RPC_URLS = {
  amoy: 'https://rpc-amoy.polygon.technology/',
  polygon: 'https://polygon-rpc.com/'
};

export async function GET(
  request: NextRequest,
  { params }: { params: { address: string } }
) {
  try {
    const tokenAddress = params.address;

    if (!tokenAddress || !ethers.isAddress(tokenAddress)) {
      return NextResponse.json(
        { error: 'Invalid token address' },
        { status: 400 }
      );
    }

    // First try to get whitelisted addresses from database
    let whitelistedAddresses: string[] = [];
    
    try {
      // Get token from database
      const token = await prisma.token.findFirst({
        where: {
          address: {
            equals: tokenAddress,
            mode: 'insensitive'
          }
        },
        include: {
          tokenApprovals: {
            where: {
              whitelistApproved: true
            },
            include: {
              client: {
                select: {
                  walletAddress: true
                }
              }
            }
          }
        }
      });

      if (token && token.tokenApprovals.length > 0) {
        whitelistedAddresses = token.tokenApprovals
          .map(approval => approval.client.walletAddress)
          .filter(address => address) as string[];
      }
    } catch (dbError) {
      console.warn('Database query failed, will try blockchain:', dbError);
    }

    // If no addresses from database, try to get from blockchain
    if (whitelistedAddresses.length === 0) {
      try {
        // Determine network (default to amoy for now)
        const network = 'amoy';
        const rpcUrl = RPC_URLS[network];
        const provider = new ethers.JsonRpcProvider(rpcUrl);

        // Create contract instance
        const contract = new ethers.Contract(tokenAddress, SecurityTokenCoreABI, provider);

        // Try to get whitelist events (this is a fallback method)
        // Note: This requires the contract to emit events when addresses are whitelisted
        try {
          const filter = contract.filters.AddressWhitelisted?.();
          if (filter) {
            const events = await contract.queryFilter(filter, 0, 'latest');
            whitelistedAddresses = events.map(event => event.args?.[0]).filter(Boolean);
            
            // Remove duplicates
            whitelistedAddresses = [...new Set(whitelistedAddresses)];
          }
        } catch (eventError) {
          console.warn('Could not fetch whitelist events:', eventError);
        }

        // If still no addresses, try some known test addresses
        if (whitelistedAddresses.length === 0) {
          // Check if any known addresses are whitelisted
          const testAddresses = [
            '******************************************', // Your test address
            '******************************************'  // Another test address
          ];

          for (const addr of testAddresses) {
            try {
              const isWhitelisted = await contract.isWhitelisted(addr);
              if (isWhitelisted) {
                whitelistedAddresses.push(addr);
              }
            } catch (checkError) {
              console.warn(`Could not check whitelist status for ${addr}:`, checkError);
            }
          }
        }
      } catch (blockchainError) {
        console.error('Blockchain query failed:', blockchainError);
      }
    }

    return NextResponse.json({
      success: true,
      tokenAddress,
      whitelistedAddresses,
      count: whitelistedAddresses.length,
      source: whitelistedAddresses.length > 0 ? 'database_and_blockchain' : 'none'
    });

  } catch (error: any) {
    console.error('Error fetching whitelist:', error);
    return NextResponse.json(
      { 
        error: 'Failed to fetch whitelist',
        details: error.message 
      },
      { status: 500 }
    );
  }
}

// POST endpoint to manually refresh whitelist from blockchain
export async function POST(
  request: NextRequest,
  { params }: { params: { address: string } }
) {
  try {
    const tokenAddress = params.address;
    const body = await request.json();
    const { action } = body;

    if (action === 'refresh') {
      // Force refresh from blockchain
      const network = 'amoy';
      const rpcUrl = RPC_URLS[network];
      const provider = new ethers.JsonRpcProvider(rpcUrl);

      const contract = new ethers.Contract(tokenAddress, SecurityTokenCoreABI, provider);

      // Get recent whitelist events
      const filter = contract.filters.AddressWhitelisted?.();
      let whitelistedAddresses: string[] = [];

      if (filter) {
        const events = await contract.queryFilter(filter, -10000, 'latest'); // Last 10k blocks
        whitelistedAddresses = events.map(event => event.args?.[0]).filter(Boolean);
        whitelistedAddresses = [...new Set(whitelistedAddresses)];
      }

      return NextResponse.json({
        success: true,
        tokenAddress,
        whitelistedAddresses,
        count: whitelistedAddresses.length,
        source: 'blockchain_refresh'
      });
    }

    return NextResponse.json(
      { error: 'Invalid action' },
      { status: 400 }
    );

  } catch (error: any) {
    console.error('Error refreshing whitelist:', error);
    return NextResponse.json(
      { 
        error: 'Failed to refresh whitelist',
        details: error.message 
      },
      { status: 500 }
    );
  }
}
