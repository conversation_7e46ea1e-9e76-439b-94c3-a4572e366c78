[{"fullName": "@openzeppelin/contracts/access/AccessControl.sol:AccessControl", "displayName": "AccessControl", "deploySize": 0, "previousDeploySize": null, "initSize": 0, "previousInitSize": null}, {"fullName": "@openzeppelin/contracts-upgradeable/access/AccessControlUpgradeable.sol:AccessControlUpgradeable", "displayName": "AccessControlUpgradeable", "deploySize": 0, "previousDeploySize": null, "initSize": 0, "previousInitSize": null}, {"fullName": "@openzeppelin/contracts/utils/Address.sol:Address", "displayName": "Address", "deploySize": 58, "previousDeploySize": 58, "initSize": 87, "previousInitSize": 87}, {"fullName": "contracts/base/BaseIdentityRegistry.sol:BaseIdentityRegistry", "displayName": "BaseIdentityRegistry", "deploySize": 0, "previousDeploySize": null, "initSize": 0, "previousInitSize": null}, {"fullName": "contracts/base/BaseKYCRegistry.sol:BaseKYCRegistry", "displayName": "BaseKYCRegistry", "deploySize": 0, "previousDeploySize": null, "initSize": 0, "previousInitSize": null}, {"fullName": "contracts/ClaimRegistry.sol:ClaimRegistry", "displayName": "ClaimRegistry", "deploySize": 11189, "previousDeploySize": 11189, "initSize": 11400, "previousInitSize": 11400}, {"fullName": "contracts/Compliance.sol:Compliance", "displayName": "Compliance", "deploySize": 10980, "previousDeploySize": 10980, "initSize": 11028, "previousInitSize": 11028}, {"fullName": "@openzeppelin/contracts/utils/Context.sol:Context", "displayName": "Context", "deploySize": 0, "previousDeploySize": null, "initSize": 0, "previousInitSize": null}, {"fullName": "@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol:ContextUpgradeable", "displayName": "ContextUpgradeable", "deploySize": 0, "previousDeploySize": null, "initSize": 0, "previousInitSize": null}, {"fullName": "@openzeppelin/contracts/utils/introspection/ERC165.sol:ERC165", "displayName": "ERC165", "deploySize": 0, "previousDeploySize": null, "initSize": 0, "previousInitSize": null}, {"fullName": "@openzeppelin/contracts-upgradeable/utils/introspection/ERC165Upgradeable.sol:ERC165Upgradeable", "displayName": "ERC165Upgradeable", "deploySize": 0, "previousDeploySize": null, "initSize": 0, "previousInitSize": null}, {"fullName": "@openzeppelin/contracts/proxy/ERC1967/ERC1967Proxy.sol:ERC1967Proxy", "displayName": "ERC1967Proxy", "deploySize": 133, "previousDeploySize": 133, "initSize": 723, "previousInitSize": 723}, {"fullName": "@openzeppelin/contracts/proxy/ERC1967/ERC1967Utils.sol:ERC1967Utils", "displayName": "ERC1967Utils", "deploySize": 58, "previousDeploySize": 58, "initSize": 87, "previousInitSize": 87}, {"fullName": "@openzeppelin/contracts/token/ERC20/ERC20.sol:ERC20", "displayName": "ERC20", "deploySize": 0, "previousDeploySize": null, "initSize": 0, "previousInitSize": null}, {"fullName": "@openzeppelin/contracts-upgradeable/token/ERC20/extensions/ERC20BurnableUpgradeable.sol:ERC20BurnableUpgradeable", "displayName": "ERC20BurnableUpgradeable", "deploySize": 0, "previousDeploySize": null, "initSize": 0, "previousInitSize": null}, {"fullName": "@openzeppelin/contracts-upgradeable/token/ERC20/ERC20Upgradeable.sol:ERC20Upgradeable", "displayName": "ERC20Upgradeable", "deploySize": 0, "previousDeploySize": null, "initSize": 0, "previousInitSize": null}, {"fullName": "@openzeppelin/contracts/utils/Errors.sol:Errors", "displayName": "Errors", "deploySize": 58, "previousDeploySize": 58, "initSize": 87, "previousInitSize": 87}, {"fullName": "@openzeppelin/contracts/access/IAccessControl.sol:IAccessControl", "displayName": "IAccessControl", "deploySize": 0, "previousDeploySize": null, "initSize": 0, "previousInitSize": null}, {"fullName": "contracts/interfaces/IModularToken.sol:IAgentManager", "displayName": "IAgentManager", "deploySize": 0, "previousDeploySize": null, "initSize": 0, "previousInitSize": null}, {"fullName": "@openzeppelin/contracts/proxy/beacon/IBeacon.sol:IBeacon", "displayName": "IBeacon", "deploySize": 0, "previousDeploySize": null, "initSize": 0, "previousInitSize": null}, {"fullName": "contracts/modules/KYCClaimsModule.sol:IClaimRegistry", "displayName": "IClaimRegistry", "deploySize": 0, "previousDeploySize": null, "initSize": 0, "previousInitSize": null}, {"fullName": "contracts/interfaces/ICompleteWhitelist.sol:ICompleteWhitelist", "displayName": "ICompleteW<PERSON>elist", "deploySize": 0, "previousDeploySize": null, "initSize": 0, "previousInitSize": null}, {"fullName": "contracts/interfaces/IModularToken.sol:IComplianceEngine", "displayName": "IComplianceEngine", "deploySize": 0, "previousDeploySize": null, "initSize": 0, "previousInitSize": null}, {"fullName": "contracts/IdentityRegistry.sol:IdentityRegistry", "displayName": "IdentityRegistry", "deploySize": 12085, "previousDeploySize": 12085, "initSize": 12133, "previousInitSize": 12133}, {"fullName": "contracts/interfaces/IModularToken.sol:IEmergencyManager", "displayName": "IEmergencyManager", "deploySize": 0, "previousDeploySize": null, "initSize": 0, "previousInitSize": null}, {"fullName": "@openzeppelin/contracts/interfaces/draft-IERC6093.sol:IERC1155Errors", "displayName": "IERC1155Errors", "deploySize": 0, "previousDeploySize": null, "initSize": 0, "previousInitSize": null}, {"fullName": "@openzeppelin/contracts/utils/introspection/IERC165.sol:IERC165", "displayName": "IERC165", "deploySize": 0, "previousDeploySize": null, "initSize": 0, "previousInitSize": null}, {"fullName": "@openzeppelin/contracts/interfaces/draft-IERC1822.sol:IERC1822Proxiable", "displayName": "IERC1822Proxiable", "deploySize": 0, "previousDeploySize": null, "initSize": 0, "previousInitSize": null}, {"fullName": "@openzeppelin/contracts/interfaces/IERC1967.sol:IERC1967", "displayName": "IERC1967", "deploySize": 0, "previousDeploySize": null, "initSize": 0, "previousInitSize": null}, {"fullName": "@openzeppelin/contracts/token/ERC20/IERC20.sol:IERC20", "displayName": "IERC20", "deploySize": 0, "previousDeploySize": null, "initSize": 0, "previousInitSize": null}, {"fullName": "@openzeppelin/contracts/interfaces/draft-IERC6093.sol:IERC20Errors", "displayName": "IERC20Errors", "deploySize": 0, "previousDeploySize": null, "initSize": 0, "previousInitSize": null}, {"fullName": "@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol:IERC20Metadata", "displayName": "IERC20Metadata", "deploySize": 0, "previousDeploySize": null, "initSize": 0, "previousInitSize": null}, {"fullName": "@openzeppelin/contracts/interfaces/draft-IERC6093.sol:IERC721Errors", "displayName": "IERC721Errors", "deploySize": 0, "previousDeploySize": null, "initSize": 0, "previousInitSize": null}, {"fullName": "contracts/interfaces/IModularToken.sol:IIdentityManager", "displayName": "IIdentityManager", "deploySize": 0, "previousDeploySize": null, "initSize": 0, "previousInitSize": null}, {"fullName": "contracts/interfaces/IIdentityRegistry.sol:IIdentityRegistry", "displayName": "IIdentityRegistry", "deploySize": 0, "previousDeploySize": null, "initSize": 0, "previousInitSize": null}, {"fullName": "contracts/interfaces/IModularToken.sol:IKYCClaimsModule", "displayName": "IKYCClaimsModule", "deploySize": 0, "previousDeploySize": null, "initSize": 0, "previousInitSize": null}, {"fullName": "contracts/interfaces/IKYCRegistry.sol:IKYCRegistry", "displayName": "IKYCRegistry", "deploySize": 0, "previousDeploySize": null, "initSize": 0, "previousInitSize": null}, {"fullName": "contracts/interfaces/IModularToken.sol:IModuleRegistry", "displayName": "IModuleRegistry", "deploySize": 0, "previousDeploySize": null, "initSize": 0, "previousInitSize": null}, {"fullName": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:Initializable", "displayName": "Initializable", "deploySize": 0, "previousDeploySize": null, "initSize": 0, "previousInitSize": null}, {"fullName": "contracts/interfaces/ISecurityToken.sol:ISecurityToken", "displayName": "ISecurityToken", "deploySize": 0, "previousDeploySize": null, "initSize": 0, "previousInitSize": null}, {"fullName": "contracts/interfaces/ISecurityTokenModule.sol:ISecurityTokenModule", "displayName": "ISecurityTokenModule", "deploySize": 0, "previousDeploySize": null, "initSize": 0, "previousInitSize": null}, {"fullName": "contracts/interfaces/IModularToken.sol:ITransferController", "displayName": "ITransferController", "deploySize": 0, "previousDeploySize": null, "initSize": 0, "previousInitSize": null}, {"fullName": "contracts/ModularTokenFactory.sol:IUpgradeManager", "displayName": "IUpgradeManager", "deploySize": 0, "previousDeploySize": null, "initSize": 0, "previousInitSize": null}, {"fullName": "contracts/interfaces/IWhitelist.sol:IWhitelist", "displayName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "deploySize": 0, "previousDeploySize": null, "initSize": 0, "previousInitSize": null}, {"fullName": "contracts/modules/KYCClaimsModule.sol:KYCClaimsModule", "displayName": "KYCClaimsModule", "deploySize": 8870, "previousDeploySize": 8870, "initSize": 9080, "previousInitSize": 9080}, {"fullName": "contracts/ModularTokenFactory.sol:ModularTokenFactory", "displayName": "ModularTokenFactory", "deploySize": 7533, "previousDeploySize": 7533, "initSize": 8331, "previousInitSize": 8331}, {"fullName": "contracts/interfaces/IModularToken.sol:ModuleIds", "displayName": "ModuleIds", "deploySize": 475, "previousDeploySize": 475, "initSize": 507, "previousInitSize": 507}, {"fullName": "@openzeppelin/contracts-upgradeable/utils/PausableUpgradeable.sol:PausableUpgradeable", "displayName": "PausableUpgradeable", "deploySize": 0, "previousDeploySize": null, "initSize": 0, "previousInitSize": null}, {"fullName": "@openzeppelin/contracts/proxy/Proxy.sol:Proxy", "displayName": "Proxy", "deploySize": 0, "previousDeploySize": null, "initSize": 0, "previousInitSize": null}, {"fullName": "@openzeppelin/contracts/utils/ReentrancyGuard.sol:ReentrancyGuard", "displayName": "Reentrancy<PERSON><PERSON>", "deploySize": 0, "previousDeploySize": null, "initSize": 0, "previousInitSize": null}, {"fullName": "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol:ReentrancyGuardUpgradeable", "displayName": "ReentrancyGuardUpgradeable", "deploySize": 0, "previousDeploySize": null, "initSize": 0, "previousInitSize": null}, {"fullName": "contracts/SecurityToken.sol:SecurityToken", "displayName": "SecurityToken", "deploySize": 25151, "previousDeploySize": 25151, "initSize": 25366, "previousInitSize": 25366}, {"fullName": "contracts/SecurityTokenCore.sol:SecurityTokenCore", "displayName": "SecurityTokenCore", "deploySize": 21631, "previousDeploySize": 21341, "initSize": 21679, "previousInitSize": 21389}, {"fullName": "contracts/SecurityTokenFactory.sol:SecurityTokenFactory", "displayName": "SecurityTokenFactory", "deploySize": 8513, "previousDeploySize": 8513, "initSize": 64525, "previousInitSize": 64525}, {"fullName": "contracts/SimpleClaimRegistry.sol:SimpleClaimRegistry", "displayName": "SimpleClaimRegistry", "deploySize": 8409, "previousDeploySize": 8409, "initSize": 14172, "previousInitSize": 14172}, {"fullName": "@openzeppelin/contracts/utils/StorageSlot.sol:StorageSlot", "displayName": "StorageSlot", "deploySize": 58, "previousDeploySize": 58, "initSize": 87, "previousInitSize": 87}, {"fullName": "contracts/UpgradeManager.sol:UpgradeManager", "displayName": "UpgradeManager", "deploySize": 12612, "previousDeploySize": 12612, "initSize": 12660, "previousInitSize": 12660}, {"fullName": "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol:UUPSUpgradeable", "displayName": "UUPSUpgradeable", "deploySize": 0, "previousDeploySize": null, "initSize": 0, "previousInitSize": null}, {"fullName": "contracts/Whitelist.sol:Whitelist", "displayName": "Whitelist", "deploySize": 8372, "previousDeploySize": 8372, "initSize": 8582, "previousInitSize": 8582}, {"fullName": "contracts/WhitelistV2.sol:WhitelistV2", "displayName": "WhitelistV2", "deploySize": 6487, "previousDeploySize": 6487, "initSize": 6697, "previousInitSize": 6697}, {"fullName": "contracts/WhitelistWithClaims.sol:WhitelistWithClaims", "displayName": "WhitelistWithClaims", "deploySize": 11130, "previousDeploySize": 11130, "initSize": 11341, "previousInitSize": 11341}, {"fullName": "contracts/WhitelistWithKYC.sol:WhitelistWithKYC", "displayName": "WhitelistWithKYC", "deploySize": 9945, "previousDeploySize": 9945, "initSize": 10155, "previousInitSize": 10155}]