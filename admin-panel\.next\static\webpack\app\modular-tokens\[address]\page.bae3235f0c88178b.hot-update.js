"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/modular-tokens/[address]/page",{

/***/ "(app-pages-browser)/./src/app/modular-tokens/[address]/page.tsx":
/*!***************************************************!*\
  !*** ./src/app/modular-tokens/[address]/page.tsx ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ModularTokenDetailsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _hooks_useModularToken__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../hooks/useModularToken */ \"(app-pages-browser)/./src/hooks/useModularToken.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n// Import custom hook\n\nfunction ModularTokenDetailsPage() {\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const tokenAddress = params.address;\n    // Use the custom hook for modular token functionality\n    const { provider, signer, tokenInfo, upgradeInfo, pendingUpgrades, upgradeHistory, loading, error, initializeProvider, mintTokens, togglePause, scheduleUpgrade, toggleEmergencyMode, refreshData, setError, updateTokenPrice, updateBonusTiers, updateMaxSupply, addToWhitelist, removeFromWhitelist } = (0,_hooks_useModularToken__WEBPACK_IMPORTED_MODULE_3__.useModularToken)(tokenAddress); // Pass the token address to the hook\n    // Local state for UI\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [mintAmount, setMintAmount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [mintAddress, setMintAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [upgradeDescription, setUpgradeDescription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [newImplementationAddress, setNewImplementationAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [actionLoading, setActionLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('overview');\n    // KYC & Claims state\n    const [kycUserAddress, setKycUserAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [claimUserAddress, setClaimUserAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [claimTopicId, setClaimTopicId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('10101010000001');\n    const [claimData, setClaimData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [checkUserAddress, setCheckUserAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [verificationStatus, setVerificationStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Admin Controls form states\n    const [newTokenPrice, setNewTokenPrice] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [newBonusTiers, setNewBonusTiers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [newMaxSupply, setNewMaxSupply] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [whitelistAddress, setWhitelistAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Clear success message after 5 seconds\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect({\n        \"ModularTokenDetailsPage.useEffect\": ()=>{\n            if (success) {\n                const timer = setTimeout({\n                    \"ModularTokenDetailsPage.useEffect.timer\": ()=>setSuccess(null)\n                }[\"ModularTokenDetailsPage.useEffect.timer\"], 5000);\n                return ({\n                    \"ModularTokenDetailsPage.useEffect\": ()=>clearTimeout(timer)\n                })[\"ModularTokenDetailsPage.useEffect\"];\n            }\n        }\n    }[\"ModularTokenDetailsPage.useEffect\"], [\n        success\n    ]);\n    // Clear error message after 10 seconds\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect({\n        \"ModularTokenDetailsPage.useEffect\": ()=>{\n            if (error) {\n                const timer = setTimeout({\n                    \"ModularTokenDetailsPage.useEffect.timer\": ()=>setError(null)\n                }[\"ModularTokenDetailsPage.useEffect.timer\"], 10000);\n                return ({\n                    \"ModularTokenDetailsPage.useEffect\": ()=>clearTimeout(timer)\n                })[\"ModularTokenDetailsPage.useEffect\"];\n            }\n        }\n    }[\"ModularTokenDetailsPage.useEffect\"], [\n        error,\n        setError\n    ]);\n    const handleMint = async ()=>{\n        if (!mintAddress || !mintAmount) return;\n        setActionLoading(true);\n        setError(null);\n        setSuccess(null);\n        try {\n            await mintTokens(mintAddress, mintAmount);\n            setSuccess(\"Successfully minted \".concat(mintAmount, \" tokens to \").concat(mintAddress));\n            setMintAmount('');\n            setMintAddress('');\n            await refreshData(); // Refresh token info\n        } catch (error) {\n            console.error('Error minting tokens:', error);\n            setError(\"Failed to mint tokens: \".concat(error.message));\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handleMintAPI = async ()=>{\n        if (!mintAddress || !mintAmount) return;\n        setActionLoading(true);\n        setError(null);\n        setSuccess(null);\n        try {\n            const response = await fetch('/api/admin/mint-tokens', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    tokenAddress: tokenAddress,\n                    toAddress: mintAddress,\n                    amount: mintAmount\n                })\n            });\n            const result = await response.json();\n            if (!response.ok) {\n                throw new Error(result.error || 'API request failed');\n            }\n            setSuccess(\"API Mint: \".concat(result.message, \" (Tx: \").concat(result.txHash, \")\"));\n            setMintAddress('');\n            setMintAmount('');\n            await refreshData();\n        } catch (error) {\n            console.error('Error with API mint:', error);\n            setError(\"API mint failed: \".concat(error.message));\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handlePauseToggle = async ()=>{\n        setActionLoading(true);\n        setError(null);\n        setSuccess(null);\n        try {\n            await togglePause();\n            setSuccess(\"Token \".concat((tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.paused) ? 'unpaused' : 'paused', \" successfully\"));\n            await refreshData(); // Refresh token info\n        } catch (error) {\n            console.error('Error toggling pause:', error);\n            setError(\"Failed to \".concat((tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.paused) ? 'unpause' : 'pause', \" token: \").concat(error.message));\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    // Admin Controls Handlers\n    const handleUpdatePrice = async ()=>{\n        if (!newTokenPrice) return;\n        setActionLoading(true);\n        try {\n            await updateTokenPrice(newTokenPrice);\n            setSuccess(\"Token price updated to \".concat(newTokenPrice));\n            setNewTokenPrice('');\n            await refreshData();\n        } catch (err) {\n            setError(err.message);\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handleUpdateBonusTiers = async ()=>{\n        if (!newBonusTiers) return;\n        setActionLoading(true);\n        try {\n            await updateBonusTiers(newBonusTiers);\n            setSuccess(\"Bonus tiers updated to: \".concat(newBonusTiers));\n            setNewBonusTiers('');\n            await refreshData();\n        } catch (err) {\n            setError(err.message);\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handleUpdateMaxSupply = async ()=>{\n        if (!newMaxSupply) return;\n        setActionLoading(true);\n        try {\n            await updateMaxSupply(newMaxSupply);\n            setSuccess(\"Max supply updated to \".concat(newMaxSupply));\n            setNewMaxSupply('');\n            await refreshData();\n        } catch (err) {\n            setError(err.message);\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handleAddToWhitelistAdmin = async ()=>{\n        if (!whitelistAddress) return;\n        setActionLoading(true);\n        try {\n            await addToWhitelist(whitelistAddress);\n            setSuccess(\"Address \".concat(whitelistAddress, \" added to whitelist\"));\n            setWhitelistAddress('');\n        } catch (err) {\n            setError(err.message);\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handleRemoveFromWhitelist = async ()=>{\n        if (!whitelistAddress) return;\n        setActionLoading(true);\n        try {\n            await removeFromWhitelist(whitelistAddress);\n            setSuccess(\"Address \".concat(whitelistAddress, \" removed from whitelist\"));\n            setWhitelistAddress('');\n        } catch (err) {\n            setError(err.message);\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    // Utility functions\n    const formatAddress = (address)=>{\n        return \"\".concat(address.slice(0, 6), \"...\").concat(address.slice(-4));\n    };\n    const formatTimestamp = (timestamp)=>{\n        return new Date(timestamp * 1000).toLocaleString();\n    };\n    if (!provider || !signer) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-6 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold text-yellow-800 mb-4\",\n                        children: \"\\uD83D\\uDD17 Wallet Connection Required\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                        lineNumber: 245,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-yellow-700 mb-4\",\n                        children: \"Please connect your MetaMask wallet to manage this modular token.\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                        lineNumber: 248,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: initializeProvider,\n                        className: \"bg-yellow-600 hover:bg-yellow-700 text-white font-bold py-2 px-4 rounded\",\n                        children: \"Connect Wallet\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                        lineNumber: 251,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                lineNumber: 244,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n            lineNumber: 243,\n            columnNumber: 7\n        }, this);\n    }\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-center items-center h-64\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                    lineNumber: 266,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                lineNumber: 265,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n            lineNumber: 264,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto px-4 py-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-gray-900 mb-2\",\n                                children: \"Modular Token Management\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 278,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: [\n                                    \"Token Address: \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-mono text-sm\",\n                                        children: tokenAddress\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 282,\n                                        columnNumber: 30\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 281,\n                                columnNumber: 13\n                            }, this),\n                            tokenInfo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: [\n                                    tokenInfo.name,\n                                    \" (\",\n                                    tokenInfo.symbol,\n                                    \") - Version \",\n                                    tokenInfo.version\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 285,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                        lineNumber: 277,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                    lineNumber: 276,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                lineNumber: 275,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-50 border border-red-200 rounded-lg p-4 mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-shrink-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"h-5 w-5 text-red-400\",\n                                viewBox: \"0 0 20 20\",\n                                fill: \"currentColor\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    fillRule: \"evenodd\",\n                                    d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\",\n                                    clipRule: \"evenodd\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                    lineNumber: 299,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 298,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                            lineNumber: 297,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"ml-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-sm font-medium text-red-800\",\n                                    children: \"Error\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                    lineNumber: 303,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-red-700 mt-1\",\n                                    children: error\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                    lineNumber: 304,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                            lineNumber: 302,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                    lineNumber: 296,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                lineNumber: 295,\n                columnNumber: 9\n            }, this),\n            success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-green-50 border border-green-200 rounded-lg p-4 mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-shrink-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"h-5 w-5 text-green-400\",\n                                viewBox: \"0 0 20 20\",\n                                fill: \"currentColor\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    fillRule: \"evenodd\",\n                                    d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",\n                                    clipRule: \"evenodd\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                    lineNumber: 315,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 314,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                            lineNumber: 313,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"ml-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-sm font-medium text-green-800\",\n                                    children: \"Success\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                    lineNumber: 319,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-green-700 mt-1\",\n                                    children: success\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                    lineNumber: 320,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                            lineNumber: 318,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                    lineNumber: 312,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                lineNumber: 311,\n                columnNumber: 9\n            }, this),\n            tokenInfo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow p-6 mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold mb-4\",\n                        children: \"Token Overview\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                        lineNumber: 329,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-500\",\n                                        children: \"Total Supply\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 332,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-blue-600\",\n                                        children: tokenInfo.totalSupply\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 333,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 331,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-500\",\n                                        children: \"Max Supply\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 338,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-green-600\",\n                                        children: tokenInfo.maxSupply\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 339,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 337,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-500\",\n                                        children: \"Status\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 344,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold \".concat(tokenInfo.paused ? 'text-red-600' : 'text-green-600'),\n                                        children: tokenInfo.paused ? 'Paused' : 'Active'\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 345,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 343,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                        lineNumber: 330,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-6 grid grid-cols-1 md:grid-cols-2 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-500\",\n                                        children: \"Token Price\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 353,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"font-medium\",\n                                        children: tokenInfo.metadata.tokenPrice\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 354,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 352,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-500\",\n                                        children: \"Bonus Tiers\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 357,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"font-medium\",\n                                        children: tokenInfo.metadata.bonusTiers\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 358,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 356,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                        lineNumber: 351,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                lineNumber: 328,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-600\",\n                    children: \"Full modular token management interface will be implemented here. This includes minting, pausing, admin controls, and upgrade management.\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                    lineNumber: 366,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                lineNumber: 365,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n        lineNumber: 273,\n        columnNumber: 5\n    }, this);\n}\n_s(ModularTokenDetailsPage, \"svge7dOzaj3+vx3iINKQ9qZOzC0=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        _hooks_useModularToken__WEBPACK_IMPORTED_MODULE_3__.useModularToken\n    ];\n});\n_c = ModularTokenDetailsPage;\nvar _c;\n$RefreshReg$(_c, \"ModularTokenDetailsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvbW9kdWxhci10b2tlbnMvW2FkZHJlc3NdL3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBRW1EO0FBQ1A7QUFHNUMscUJBQXFCO0FBQzRDO0FBRWxELFNBQVNJOztJQUN0QixNQUFNQyxTQUFTSCwwREFBU0E7SUFDeEIsTUFBTUksZUFBZUQsT0FBT0UsT0FBTztJQUVuQyxzREFBc0Q7SUFDdEQsTUFBTSxFQUNKQyxRQUFRLEVBQ1JDLE1BQU0sRUFDTkMsU0FBUyxFQUNUQyxXQUFXLEVBQ1hDLGVBQWUsRUFDZkMsY0FBYyxFQUNkQyxPQUFPLEVBQ1BDLEtBQUssRUFDTEMsa0JBQWtCLEVBQ2xCQyxVQUFVLEVBQ1ZDLFdBQVcsRUFDWEMsZUFBZSxFQUNmQyxtQkFBbUIsRUFDbkJDLFdBQVcsRUFDWEMsUUFBUSxFQUNSQyxnQkFBZ0IsRUFDaEJDLGdCQUFnQixFQUNoQkMsZUFBZSxFQUNmQyxjQUFjLEVBQ2RDLG1CQUFtQixFQUNwQixHQUFHeEIsdUVBQWVBLENBQUNHLGVBQWUscUNBQXFDO0lBRXhFLHFCQUFxQjtJQUNyQixNQUFNLENBQUNzQixTQUFTQyxXQUFXLEdBQUc1QiwrQ0FBUUEsQ0FBZ0I7SUFDdEQsTUFBTSxDQUFDNkIsWUFBWUMsY0FBYyxHQUFHOUIsK0NBQVFBLENBQUM7SUFDN0MsTUFBTSxDQUFDK0IsYUFBYUMsZUFBZSxHQUFHaEMsK0NBQVFBLENBQUM7SUFDL0MsTUFBTSxDQUFDaUMsb0JBQW9CQyxzQkFBc0IsR0FBR2xDLCtDQUFRQSxDQUFDO0lBQzdELE1BQU0sQ0FBQ21DLDBCQUEwQkMsNEJBQTRCLEdBQUdwQywrQ0FBUUEsQ0FBQztJQUN6RSxNQUFNLENBQUNxQyxlQUFlQyxpQkFBaUIsR0FBR3RDLCtDQUFRQSxDQUFDO0lBQ25ELE1BQU0sQ0FBQ3VDLFdBQVdDLGFBQWEsR0FBR3hDLCtDQUFRQSxDQUFDO0lBRTNDLHFCQUFxQjtJQUNyQixNQUFNLENBQUN5QyxnQkFBZ0JDLGtCQUFrQixHQUFHMUMsK0NBQVFBLENBQUM7SUFDckQsTUFBTSxDQUFDMkMsa0JBQWtCQyxvQkFBb0IsR0FBRzVDLCtDQUFRQSxDQUFDO0lBQ3pELE1BQU0sQ0FBQzZDLGNBQWNDLGdCQUFnQixHQUFHOUMsK0NBQVFBLENBQUM7SUFDakQsTUFBTSxDQUFDK0MsV0FBV0MsYUFBYSxHQUFHaEQsK0NBQVFBLENBQUM7SUFDM0MsTUFBTSxDQUFDaUQsa0JBQWtCQyxvQkFBb0IsR0FBR2xELCtDQUFRQSxDQUFDO0lBQ3pELE1BQU0sQ0FBQ21ELG9CQUFvQkMsc0JBQXNCLEdBQUdwRCwrQ0FBUUEsQ0FBQztJQUU3RCw2QkFBNkI7SUFDN0IsTUFBTSxDQUFDcUQsZUFBZUMsaUJBQWlCLEdBQUd0RCwrQ0FBUUEsQ0FBQztJQUNuRCxNQUFNLENBQUN1RCxlQUFlQyxpQkFBaUIsR0FBR3hELCtDQUFRQSxDQUFDO0lBQ25ELE1BQU0sQ0FBQ3lELGNBQWNDLGdCQUFnQixHQUFHMUQsK0NBQVFBLENBQUM7SUFDakQsTUFBTSxDQUFDMkQsa0JBQWtCQyxvQkFBb0IsR0FBRzVELCtDQUFRQSxDQUFDO0lBRXpELHdDQUF3QztJQUN4Q0Qsc0RBQWU7NkNBQUM7WUFDZCxJQUFJNEIsU0FBUztnQkFDWCxNQUFNbUMsUUFBUUM7K0RBQVcsSUFBTW5DLFdBQVc7OERBQU87Z0JBQ2pEO3lEQUFPLElBQU1vQyxhQUFhRjs7WUFDNUI7UUFDRjs0Q0FBRztRQUFDbkM7S0FBUTtJQUVaLHVDQUF1QztJQUN2QzVCLHNEQUFlOzZDQUFDO1lBQ2QsSUFBSWUsT0FBTztnQkFDVCxNQUFNZ0QsUUFBUUM7K0RBQVcsSUFBTTFDLFNBQVM7OERBQU87Z0JBQy9DO3lEQUFPLElBQU0yQyxhQUFhRjs7WUFDNUI7UUFDRjs0Q0FBRztRQUFDaEQ7UUFBT087S0FBUztJQUVwQixNQUFNNEMsYUFBYTtRQUNqQixJQUFJLENBQUNsQyxlQUFlLENBQUNGLFlBQVk7UUFFakNTLGlCQUFpQjtRQUNqQmpCLFNBQVM7UUFDVE8sV0FBVztRQUVYLElBQUk7WUFDRixNQUFNWixXQUFXZSxhQUFhRjtZQUM5QkQsV0FBVyx1QkFBK0NHLE9BQXhCRixZQUFXLGVBQXlCLE9BQVpFO1lBQzFERCxjQUFjO1lBQ2RFLGVBQWU7WUFDZixNQUFNWixlQUFlLHFCQUFxQjtRQUM1QyxFQUFFLE9BQU9OLE9BQVk7WUFDbkJvRCxRQUFRcEQsS0FBSyxDQUFDLHlCQUF5QkE7WUFDdkNPLFNBQVMsMEJBQXdDLE9BQWRQLE1BQU1xRCxPQUFPO1FBQ2xELFNBQVU7WUFDUjdCLGlCQUFpQjtRQUNuQjtJQUNGO0lBRUEsTUFBTThCLGdCQUFnQjtRQUNwQixJQUFJLENBQUNyQyxlQUFlLENBQUNGLFlBQVk7UUFFakNTLGlCQUFpQjtRQUNqQmpCLFNBQVM7UUFDVE8sV0FBVztRQUVYLElBQUk7WUFDRixNQUFNeUMsV0FBVyxNQUFNQyxNQUFNLDBCQUEwQjtnQkFDckRDLFFBQVE7Z0JBQ1JDLFNBQVM7b0JBQ1AsZ0JBQWdCO2dCQUNsQjtnQkFDQUMsTUFBTUMsS0FBS0MsU0FBUyxDQUFDO29CQUNuQnRFLGNBQWNBO29CQUNkdUUsV0FBVzdDO29CQUNYOEMsUUFBUWhEO2dCQUNWO1lBQ0Y7WUFFQSxNQUFNaUQsU0FBUyxNQUFNVCxTQUFTVSxJQUFJO1lBRWxDLElBQUksQ0FBQ1YsU0FBU1csRUFBRSxFQUFFO2dCQUNoQixNQUFNLElBQUlDLE1BQU1ILE9BQU9oRSxLQUFLLElBQUk7WUFDbEM7WUFFQWMsV0FBVyxhQUFvQ2tELE9BQXZCQSxPQUFPWCxPQUFPLEVBQUMsVUFBc0IsT0FBZFcsT0FBT0ksTUFBTSxFQUFDO1lBQzdEbEQsZUFBZTtZQUNmRixjQUFjO1lBQ2QsTUFBTVY7UUFDUixFQUFFLE9BQU9OLE9BQVk7WUFDbkJvRCxRQUFRcEQsS0FBSyxDQUFDLHdCQUF3QkE7WUFDdENPLFNBQVMsb0JBQWtDLE9BQWRQLE1BQU1xRCxPQUFPO1FBQzVDLFNBQVU7WUFDUjdCLGlCQUFpQjtRQUNuQjtJQUNGO0lBRUEsTUFBTTZDLG9CQUFvQjtRQUN4QjdDLGlCQUFpQjtRQUNqQmpCLFNBQVM7UUFDVE8sV0FBVztRQUVYLElBQUk7WUFDRixNQUFNWDtZQUNOVyxXQUFXLFNBQW1ELE9BQTFDbkIsQ0FBQUEsc0JBQUFBLGdDQUFBQSxVQUFXMkUsTUFBTSxJQUFHLGFBQWEsVUFBUztZQUM5RCxNQUFNaEUsZUFBZSxxQkFBcUI7UUFDNUMsRUFBRSxPQUFPTixPQUFZO1lBQ25Cb0QsUUFBUXBELEtBQUssQ0FBQyx5QkFBeUJBO1lBQ3ZDTyxTQUFTLGFBQStEUCxPQUFsREwsQ0FBQUEsc0JBQUFBLGdDQUFBQSxVQUFXMkUsTUFBTSxJQUFHLFlBQVksU0FBUSxZQUF3QixPQUFkdEUsTUFBTXFELE9BQU87UUFDdkYsU0FBVTtZQUNSN0IsaUJBQWlCO1FBQ25CO0lBQ0Y7SUFFQSwwQkFBMEI7SUFDMUIsTUFBTStDLG9CQUFvQjtRQUN4QixJQUFJLENBQUNoQyxlQUFlO1FBRXBCZixpQkFBaUI7UUFDakIsSUFBSTtZQUNGLE1BQU1oQixpQkFBaUIrQjtZQUN2QnpCLFdBQVcsMEJBQXdDLE9BQWR5QjtZQUNyQ0MsaUJBQWlCO1lBQ2pCLE1BQU1sQztRQUNSLEVBQUUsT0FBT2tFLEtBQVU7WUFDakJqRSxTQUFTaUUsSUFBSW5CLE9BQU87UUFDdEIsU0FBVTtZQUNSN0IsaUJBQWlCO1FBQ25CO0lBQ0Y7SUFFQSxNQUFNaUQseUJBQXlCO1FBQzdCLElBQUksQ0FBQ2hDLGVBQWU7UUFFcEJqQixpQkFBaUI7UUFDakIsSUFBSTtZQUNGLE1BQU1mLGlCQUFpQmdDO1lBQ3ZCM0IsV0FBVywyQkFBeUMsT0FBZDJCO1lBQ3RDQyxpQkFBaUI7WUFDakIsTUFBTXBDO1FBQ1IsRUFBRSxPQUFPa0UsS0FBVTtZQUNqQmpFLFNBQVNpRSxJQUFJbkIsT0FBTztRQUN0QixTQUFVO1lBQ1I3QixpQkFBaUI7UUFDbkI7SUFDRjtJQUVBLE1BQU1rRCx3QkFBd0I7UUFDNUIsSUFBSSxDQUFDL0IsY0FBYztRQUVuQm5CLGlCQUFpQjtRQUNqQixJQUFJO1lBQ0YsTUFBTWQsZ0JBQWdCaUM7WUFDdEI3QixXQUFXLHlCQUFzQyxPQUFiNkI7WUFDcENDLGdCQUFnQjtZQUNoQixNQUFNdEM7UUFDUixFQUFFLE9BQU9rRSxLQUFVO1lBQ2pCakUsU0FBU2lFLElBQUluQixPQUFPO1FBQ3RCLFNBQVU7WUFDUjdCLGlCQUFpQjtRQUNuQjtJQUNGO0lBRUEsTUFBTW1ELDRCQUE0QjtRQUNoQyxJQUFJLENBQUM5QixrQkFBa0I7UUFFdkJyQixpQkFBaUI7UUFDakIsSUFBSTtZQUNGLE1BQU1iLGVBQWVrQztZQUNyQi9CLFdBQVcsV0FBNEIsT0FBakIrQixrQkFBaUI7WUFDdkNDLG9CQUFvQjtRQUN0QixFQUFFLE9BQU8wQixLQUFVO1lBQ2pCakUsU0FBU2lFLElBQUluQixPQUFPO1FBQ3RCLFNBQVU7WUFDUjdCLGlCQUFpQjtRQUNuQjtJQUNGO0lBRUEsTUFBTW9ELDRCQUE0QjtRQUNoQyxJQUFJLENBQUMvQixrQkFBa0I7UUFFdkJyQixpQkFBaUI7UUFDakIsSUFBSTtZQUNGLE1BQU1aLG9CQUFvQmlDO1lBQzFCL0IsV0FBVyxXQUE0QixPQUFqQitCLGtCQUFpQjtZQUN2Q0Msb0JBQW9CO1FBQ3RCLEVBQUUsT0FBTzBCLEtBQVU7WUFDakJqRSxTQUFTaUUsSUFBSW5CLE9BQU87UUFDdEIsU0FBVTtZQUNSN0IsaUJBQWlCO1FBQ25CO0lBQ0Y7SUFFQSxvQkFBb0I7SUFDcEIsTUFBTXFELGdCQUFnQixDQUFDckY7UUFDckIsT0FBTyxHQUE0QkEsT0FBekJBLFFBQVFzRixLQUFLLENBQUMsR0FBRyxJQUFHLE9BQXVCLE9BQWxCdEYsUUFBUXNGLEtBQUssQ0FBQyxDQUFDO0lBQ3BEO0lBRUEsTUFBTUMsa0JBQWtCLENBQUNDO1FBQ3ZCLE9BQU8sSUFBSUMsS0FBS0QsWUFBWSxNQUFNRSxjQUFjO0lBQ2xEO0lBRUEsSUFBSSxDQUFDekYsWUFBWSxDQUFDQyxRQUFRO1FBQ3hCLHFCQUNFLDhEQUFDeUY7WUFBSUMsV0FBVTtzQkFDYiw0RUFBQ0Q7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDQzt3QkFBR0QsV0FBVTtrQ0FBNkM7Ozs7OztrQ0FHM0QsOERBQUNFO3dCQUFFRixXQUFVO2tDQUF1Qjs7Ozs7O2tDQUdwQyw4REFBQ0c7d0JBQ0NDLFNBQVN2Rjt3QkFDVG1GLFdBQVU7a0NBQ1g7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBTVQ7SUFFQSxJQUFJckYsU0FBUztRQUNYLHFCQUNFLDhEQUFDb0Y7WUFBSUMsV0FBVTtzQkFDYiw0RUFBQ0Q7Z0JBQUlDLFdBQVU7MEJBQ2IsNEVBQUNEO29CQUFJQyxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7O0lBSXZCO0lBRUEscUJBQ0UsOERBQUNEO1FBQUlDLFdBQVU7OzBCQUViLDhEQUFDRDtnQkFBSUMsV0FBVTswQkFDYiw0RUFBQ0Q7b0JBQUlDLFdBQVU7OEJBQ2IsNEVBQUNEOzswQ0FDQyw4REFBQ007Z0NBQUdMLFdBQVU7MENBQXdDOzs7Ozs7MENBR3RELDhEQUFDRTtnQ0FBRUYsV0FBVTs7b0NBQWdCO2tEQUNaLDhEQUFDTTt3Q0FBS04sV0FBVTtrREFBcUI3Rjs7Ozs7Ozs7Ozs7OzRCQUVyREksMkJBQ0MsOERBQUMyRjtnQ0FBRUYsV0FBVTs7b0NBQ1Z6RixVQUFVZ0csSUFBSTtvQ0FBQztvQ0FBR2hHLFVBQVVpRyxNQUFNO29DQUFDO29DQUFhakcsVUFBVWtHLE9BQU87Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O1lBUTNFN0YsdUJBQ0MsOERBQUNtRjtnQkFBSUMsV0FBVTswQkFDYiw0RUFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDRDs0QkFBSUMsV0FBVTtzQ0FDYiw0RUFBQ1U7Z0NBQUlWLFdBQVU7Z0NBQXVCVyxTQUFRO2dDQUFZQyxNQUFLOzBDQUM3RCw0RUFBQ0M7b0NBQUtDLFVBQVM7b0NBQVVDLEdBQUU7b0NBQTBOQyxVQUFTOzs7Ozs7Ozs7Ozs7Ozs7O3NDQUdsUSw4REFBQ2pCOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ2lCO29DQUFHakIsV0FBVTs4Q0FBbUM7Ozs7Ozs4Q0FDakQsOERBQUNFO29DQUFFRixXQUFVOzhDQUE2QnBGOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztZQU1qRGEseUJBQ0MsOERBQUNzRTtnQkFBSUMsV0FBVTswQkFDYiw0RUFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDRDs0QkFBSUMsV0FBVTtzQ0FDYiw0RUFBQ1U7Z0NBQUlWLFdBQVU7Z0NBQXlCVyxTQUFRO2dDQUFZQyxNQUFLOzBDQUMvRCw0RUFBQ0M7b0NBQUtDLFVBQVM7b0NBQVVDLEdBQUU7b0NBQXdJQyxVQUFTOzs7Ozs7Ozs7Ozs7Ozs7O3NDQUdoTCw4REFBQ2pCOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ2lCO29DQUFHakIsV0FBVTs4Q0FBcUM7Ozs7Ozs4Q0FDbkQsOERBQUNFO29DQUFFRixXQUFVOzhDQUErQnZFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztZQU9uRGxCLDJCQUNDLDhEQUFDd0Y7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDQzt3QkFBR0QsV0FBVTtrQ0FBNkI7Ozs7OztrQ0FDM0MsOERBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0Q7O2tEQUNDLDhEQUFDQTt3Q0FBSUMsV0FBVTtrREFBd0I7Ozs7OztrREFDdkMsOERBQUNEO3dDQUFJQyxXQUFVO2tEQUNaekYsVUFBVTJHLFdBQVc7Ozs7Ozs7Ozs7OzswQ0FHMUIsOERBQUNuQjs7a0RBQ0MsOERBQUNBO3dDQUFJQyxXQUFVO2tEQUF3Qjs7Ozs7O2tEQUN2Qyw4REFBQ0Q7d0NBQUlDLFdBQVU7a0RBQ1p6RixVQUFVNEcsU0FBUzs7Ozs7Ozs7Ozs7OzBDQUd4Qiw4REFBQ3BCOztrREFDQyw4REFBQ0E7d0NBQUlDLFdBQVU7a0RBQXdCOzs7Ozs7a0RBQ3ZDLDhEQUFDRDt3Q0FBSUMsV0FBVyxzQkFBMkUsT0FBckR6RixVQUFVMkUsTUFBTSxHQUFHLGlCQUFpQjtrREFDdkUzRSxVQUFVMkUsTUFBTSxHQUFHLFdBQVc7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FLckMsOERBQUNhO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0Q7O2tEQUNDLDhEQUFDQTt3Q0FBSUMsV0FBVTtrREFBd0I7Ozs7OztrREFDdkMsOERBQUNEO3dDQUFJQyxXQUFVO2tEQUFlekYsVUFBVTZHLFFBQVEsQ0FBQ0MsVUFBVTs7Ozs7Ozs7Ozs7OzBDQUU3RCw4REFBQ3RCOztrREFDQyw4REFBQ0E7d0NBQUlDLFdBQVU7a0RBQXdCOzs7Ozs7a0RBQ3ZDLDhEQUFDRDt3Q0FBSUMsV0FBVTtrREFBZXpGLFVBQVU2RyxRQUFRLENBQUNFLFVBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFPbkUsOERBQUN2QjtnQkFBSUMsV0FBVTswQkFDYiw0RUFBQ0U7b0JBQUVGLFdBQVU7OEJBQWdCOzs7Ozs7Ozs7Ozs7Ozs7OztBQU9yQztHQTNXd0IvRjs7UUFDUEYsc0RBQVNBO1FBeUJwQkMsbUVBQWVBOzs7S0ExQkdDIiwic291cmNlcyI6WyJEOlxcZ2l0aHViXFx0b2tlbmRldi1uZXdyb29cXGFkbWluLXBhbmVsXFxzcmNcXGFwcFxcbW9kdWxhci10b2tlbnNcXFthZGRyZXNzXVxccGFnZS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgUmVhY3QsIHsgdXNlU3RhdGUsIHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IHVzZVBhcmFtcyB9IGZyb20gJ25leHQvbmF2aWdhdGlvbic7XG5pbXBvcnQgeyBldGhlcnMgfSBmcm9tICdldGhlcnMnO1xuXG4vLyBJbXBvcnQgY3VzdG9tIGhvb2tcbmltcG9ydCB7IHVzZU1vZHVsYXJUb2tlbiB9IGZyb20gJy4uLy4uLy4uL2hvb2tzL3VzZU1vZHVsYXJUb2tlbic7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIE1vZHVsYXJUb2tlbkRldGFpbHNQYWdlKCkge1xuICBjb25zdCBwYXJhbXMgPSB1c2VQYXJhbXMoKTtcbiAgY29uc3QgdG9rZW5BZGRyZXNzID0gcGFyYW1zLmFkZHJlc3MgYXMgc3RyaW5nO1xuXG4gIC8vIFVzZSB0aGUgY3VzdG9tIGhvb2sgZm9yIG1vZHVsYXIgdG9rZW4gZnVuY3Rpb25hbGl0eVxuICBjb25zdCB7XG4gICAgcHJvdmlkZXIsXG4gICAgc2lnbmVyLFxuICAgIHRva2VuSW5mbyxcbiAgICB1cGdyYWRlSW5mbyxcbiAgICBwZW5kaW5nVXBncmFkZXMsXG4gICAgdXBncmFkZUhpc3RvcnksXG4gICAgbG9hZGluZyxcbiAgICBlcnJvcixcbiAgICBpbml0aWFsaXplUHJvdmlkZXIsXG4gICAgbWludFRva2VucyxcbiAgICB0b2dnbGVQYXVzZSxcbiAgICBzY2hlZHVsZVVwZ3JhZGUsXG4gICAgdG9nZ2xlRW1lcmdlbmN5TW9kZSxcbiAgICByZWZyZXNoRGF0YSxcbiAgICBzZXRFcnJvcixcbiAgICB1cGRhdGVUb2tlblByaWNlLFxuICAgIHVwZGF0ZUJvbnVzVGllcnMsXG4gICAgdXBkYXRlTWF4U3VwcGx5LFxuICAgIGFkZFRvV2hpdGVsaXN0LFxuICAgIHJlbW92ZUZyb21XaGl0ZWxpc3RcbiAgfSA9IHVzZU1vZHVsYXJUb2tlbih0b2tlbkFkZHJlc3MpOyAvLyBQYXNzIHRoZSB0b2tlbiBhZGRyZXNzIHRvIHRoZSBob29rXG5cbiAgLy8gTG9jYWwgc3RhdGUgZm9yIFVJXG4gIGNvbnN0IFtzdWNjZXNzLCBzZXRTdWNjZXNzXSA9IHVzZVN0YXRlPHN0cmluZyB8IG51bGw+KG51bGwpO1xuICBjb25zdCBbbWludEFtb3VudCwgc2V0TWludEFtb3VudF0gPSB1c2VTdGF0ZSgnJyk7XG4gIGNvbnN0IFttaW50QWRkcmVzcywgc2V0TWludEFkZHJlc3NdID0gdXNlU3RhdGUoJycpO1xuICBjb25zdCBbdXBncmFkZURlc2NyaXB0aW9uLCBzZXRVcGdyYWRlRGVzY3JpcHRpb25dID0gdXNlU3RhdGUoJycpO1xuICBjb25zdCBbbmV3SW1wbGVtZW50YXRpb25BZGRyZXNzLCBzZXROZXdJbXBsZW1lbnRhdGlvbkFkZHJlc3NdID0gdXNlU3RhdGUoJycpO1xuICBjb25zdCBbYWN0aW9uTG9hZGluZywgc2V0QWN0aW9uTG9hZGluZ10gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFthY3RpdmVUYWIsIHNldEFjdGl2ZVRhYl0gPSB1c2VTdGF0ZSgnb3ZlcnZpZXcnKTtcblxuICAvLyBLWUMgJiBDbGFpbXMgc3RhdGVcbiAgY29uc3QgW2t5Y1VzZXJBZGRyZXNzLCBzZXRLeWNVc2VyQWRkcmVzc10gPSB1c2VTdGF0ZSgnJyk7XG4gIGNvbnN0IFtjbGFpbVVzZXJBZGRyZXNzLCBzZXRDbGFpbVVzZXJBZGRyZXNzXSA9IHVzZVN0YXRlKCcnKTtcbiAgY29uc3QgW2NsYWltVG9waWNJZCwgc2V0Q2xhaW1Ub3BpY0lkXSA9IHVzZVN0YXRlKCcxMDEwMTAxMDAwMDAwMScpO1xuICBjb25zdCBbY2xhaW1EYXRhLCBzZXRDbGFpbURhdGFdID0gdXNlU3RhdGUoJycpO1xuICBjb25zdCBbY2hlY2tVc2VyQWRkcmVzcywgc2V0Q2hlY2tVc2VyQWRkcmVzc10gPSB1c2VTdGF0ZSgnJyk7XG4gIGNvbnN0IFt2ZXJpZmljYXRpb25TdGF0dXMsIHNldFZlcmlmaWNhdGlvblN0YXR1c10gPSB1c2VTdGF0ZShudWxsKTtcblxuICAvLyBBZG1pbiBDb250cm9scyBmb3JtIHN0YXRlc1xuICBjb25zdCBbbmV3VG9rZW5QcmljZSwgc2V0TmV3VG9rZW5QcmljZV0gPSB1c2VTdGF0ZSgnJyk7XG4gIGNvbnN0IFtuZXdCb251c1RpZXJzLCBzZXROZXdCb251c1RpZXJzXSA9IHVzZVN0YXRlKCcnKTtcbiAgY29uc3QgW25ld01heFN1cHBseSwgc2V0TmV3TWF4U3VwcGx5XSA9IHVzZVN0YXRlKCcnKTtcbiAgY29uc3QgW3doaXRlbGlzdEFkZHJlc3MsIHNldFdoaXRlbGlzdEFkZHJlc3NdID0gdXNlU3RhdGUoJycpO1xuXG4gIC8vIENsZWFyIHN1Y2Nlc3MgbWVzc2FnZSBhZnRlciA1IHNlY29uZHNcbiAgUmVhY3QudXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAoc3VjY2Vzcykge1xuICAgICAgY29uc3QgdGltZXIgPSBzZXRUaW1lb3V0KCgpID0+IHNldFN1Y2Nlc3MobnVsbCksIDUwMDApO1xuICAgICAgcmV0dXJuICgpID0+IGNsZWFyVGltZW91dCh0aW1lcik7XG4gICAgfVxuICB9LCBbc3VjY2Vzc10pO1xuXG4gIC8vIENsZWFyIGVycm9yIG1lc3NhZ2UgYWZ0ZXIgMTAgc2Vjb25kc1xuICBSZWFjdC51c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmIChlcnJvcikge1xuICAgICAgY29uc3QgdGltZXIgPSBzZXRUaW1lb3V0KCgpID0+IHNldEVycm9yKG51bGwpLCAxMDAwMCk7XG4gICAgICByZXR1cm4gKCkgPT4gY2xlYXJUaW1lb3V0KHRpbWVyKTtcbiAgICB9XG4gIH0sIFtlcnJvciwgc2V0RXJyb3JdKTtcblxuICBjb25zdCBoYW5kbGVNaW50ID0gYXN5bmMgKCkgPT4ge1xuICAgIGlmICghbWludEFkZHJlc3MgfHwgIW1pbnRBbW91bnQpIHJldHVybjtcblxuICAgIHNldEFjdGlvbkxvYWRpbmcodHJ1ZSk7XG4gICAgc2V0RXJyb3IobnVsbCk7XG4gICAgc2V0U3VjY2VzcyhudWxsKTtcblxuICAgIHRyeSB7XG4gICAgICBhd2FpdCBtaW50VG9rZW5zKG1pbnRBZGRyZXNzLCBtaW50QW1vdW50KTtcbiAgICAgIHNldFN1Y2Nlc3MoYFN1Y2Nlc3NmdWxseSBtaW50ZWQgJHttaW50QW1vdW50fSB0b2tlbnMgdG8gJHttaW50QWRkcmVzc31gKTtcbiAgICAgIHNldE1pbnRBbW91bnQoJycpO1xuICAgICAgc2V0TWludEFkZHJlc3MoJycpO1xuICAgICAgYXdhaXQgcmVmcmVzaERhdGEoKTsgLy8gUmVmcmVzaCB0b2tlbiBpbmZvXG4gICAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgbWludGluZyB0b2tlbnM6JywgZXJyb3IpO1xuICAgICAgc2V0RXJyb3IoYEZhaWxlZCB0byBtaW50IHRva2VuczogJHtlcnJvci5tZXNzYWdlfWApO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRBY3Rpb25Mb2FkaW5nKGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlTWludEFQSSA9IGFzeW5jICgpID0+IHtcbiAgICBpZiAoIW1pbnRBZGRyZXNzIHx8ICFtaW50QW1vdW50KSByZXR1cm47XG5cbiAgICBzZXRBY3Rpb25Mb2FkaW5nKHRydWUpO1xuICAgIHNldEVycm9yKG51bGwpO1xuICAgIHNldFN1Y2Nlc3MobnVsbCk7XG5cbiAgICB0cnkge1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaCgnL2FwaS9hZG1pbi9taW50LXRva2VucycsIHtcbiAgICAgICAgbWV0aG9kOiAnUE9TVCcsXG4gICAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICB9LFxuICAgICAgICBib2R5OiBKU09OLnN0cmluZ2lmeSh7XG4gICAgICAgICAgdG9rZW5BZGRyZXNzOiB0b2tlbkFkZHJlc3MsXG4gICAgICAgICAgdG9BZGRyZXNzOiBtaW50QWRkcmVzcyxcbiAgICAgICAgICBhbW91bnQ6IG1pbnRBbW91bnQsXG4gICAgICAgIH0pLFxuICAgICAgfSk7XG5cbiAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcblxuICAgICAgaWYgKCFyZXNwb25zZS5vaykge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IocmVzdWx0LmVycm9yIHx8ICdBUEkgcmVxdWVzdCBmYWlsZWQnKTtcbiAgICAgIH1cblxuICAgICAgc2V0U3VjY2VzcyhgQVBJIE1pbnQ6ICR7cmVzdWx0Lm1lc3NhZ2V9IChUeDogJHtyZXN1bHQudHhIYXNofSlgKTtcbiAgICAgIHNldE1pbnRBZGRyZXNzKCcnKTtcbiAgICAgIHNldE1pbnRBbW91bnQoJycpO1xuICAgICAgYXdhaXQgcmVmcmVzaERhdGEoKTtcbiAgICB9IGNhdGNoIChlcnJvcjogYW55KSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciB3aXRoIEFQSSBtaW50OicsIGVycm9yKTtcbiAgICAgIHNldEVycm9yKGBBUEkgbWludCBmYWlsZWQ6ICR7ZXJyb3IubWVzc2FnZX1gKTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0QWN0aW9uTG9hZGluZyhmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGhhbmRsZVBhdXNlVG9nZ2xlID0gYXN5bmMgKCkgPT4ge1xuICAgIHNldEFjdGlvbkxvYWRpbmcodHJ1ZSk7XG4gICAgc2V0RXJyb3IobnVsbCk7XG4gICAgc2V0U3VjY2VzcyhudWxsKTtcblxuICAgIHRyeSB7XG4gICAgICBhd2FpdCB0b2dnbGVQYXVzZSgpO1xuICAgICAgc2V0U3VjY2VzcyhgVG9rZW4gJHt0b2tlbkluZm8/LnBhdXNlZCA/ICd1bnBhdXNlZCcgOiAncGF1c2VkJ30gc3VjY2Vzc2Z1bGx5YCk7XG4gICAgICBhd2FpdCByZWZyZXNoRGF0YSgpOyAvLyBSZWZyZXNoIHRva2VuIGluZm9cbiAgICB9IGNhdGNoIChlcnJvcjogYW55KSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciB0b2dnbGluZyBwYXVzZTonLCBlcnJvcik7XG4gICAgICBzZXRFcnJvcihgRmFpbGVkIHRvICR7dG9rZW5JbmZvPy5wYXVzZWQgPyAndW5wYXVzZScgOiAncGF1c2UnfSB0b2tlbjogJHtlcnJvci5tZXNzYWdlfWApO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRBY3Rpb25Mb2FkaW5nKGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgLy8gQWRtaW4gQ29udHJvbHMgSGFuZGxlcnNcbiAgY29uc3QgaGFuZGxlVXBkYXRlUHJpY2UgPSBhc3luYyAoKSA9PiB7XG4gICAgaWYgKCFuZXdUb2tlblByaWNlKSByZXR1cm47XG5cbiAgICBzZXRBY3Rpb25Mb2FkaW5nKHRydWUpO1xuICAgIHRyeSB7XG4gICAgICBhd2FpdCB1cGRhdGVUb2tlblByaWNlKG5ld1Rva2VuUHJpY2UpO1xuICAgICAgc2V0U3VjY2VzcyhgVG9rZW4gcHJpY2UgdXBkYXRlZCB0byAke25ld1Rva2VuUHJpY2V9YCk7XG4gICAgICBzZXROZXdUb2tlblByaWNlKCcnKTtcbiAgICAgIGF3YWl0IHJlZnJlc2hEYXRhKCk7XG4gICAgfSBjYXRjaCAoZXJyOiBhbnkpIHtcbiAgICAgIHNldEVycm9yKGVyci5tZXNzYWdlKTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0QWN0aW9uTG9hZGluZyhmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGhhbmRsZVVwZGF0ZUJvbnVzVGllcnMgPSBhc3luYyAoKSA9PiB7XG4gICAgaWYgKCFuZXdCb251c1RpZXJzKSByZXR1cm47XG5cbiAgICBzZXRBY3Rpb25Mb2FkaW5nKHRydWUpO1xuICAgIHRyeSB7XG4gICAgICBhd2FpdCB1cGRhdGVCb251c1RpZXJzKG5ld0JvbnVzVGllcnMpO1xuICAgICAgc2V0U3VjY2VzcyhgQm9udXMgdGllcnMgdXBkYXRlZCB0bzogJHtuZXdCb251c1RpZXJzfWApO1xuICAgICAgc2V0TmV3Qm9udXNUaWVycygnJyk7XG4gICAgICBhd2FpdCByZWZyZXNoRGF0YSgpO1xuICAgIH0gY2F0Y2ggKGVycjogYW55KSB7XG4gICAgICBzZXRFcnJvcihlcnIubWVzc2FnZSk7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldEFjdGlvbkxvYWRpbmcoZmFsc2UpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBoYW5kbGVVcGRhdGVNYXhTdXBwbHkgPSBhc3luYyAoKSA9PiB7XG4gICAgaWYgKCFuZXdNYXhTdXBwbHkpIHJldHVybjtcblxuICAgIHNldEFjdGlvbkxvYWRpbmcodHJ1ZSk7XG4gICAgdHJ5IHtcbiAgICAgIGF3YWl0IHVwZGF0ZU1heFN1cHBseShuZXdNYXhTdXBwbHkpO1xuICAgICAgc2V0U3VjY2VzcyhgTWF4IHN1cHBseSB1cGRhdGVkIHRvICR7bmV3TWF4U3VwcGx5fWApO1xuICAgICAgc2V0TmV3TWF4U3VwcGx5KCcnKTtcbiAgICAgIGF3YWl0IHJlZnJlc2hEYXRhKCk7XG4gICAgfSBjYXRjaCAoZXJyOiBhbnkpIHtcbiAgICAgIHNldEVycm9yKGVyci5tZXNzYWdlKTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0QWN0aW9uTG9hZGluZyhmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGhhbmRsZUFkZFRvV2hpdGVsaXN0QWRtaW4gPSBhc3luYyAoKSA9PiB7XG4gICAgaWYgKCF3aGl0ZWxpc3RBZGRyZXNzKSByZXR1cm47XG5cbiAgICBzZXRBY3Rpb25Mb2FkaW5nKHRydWUpO1xuICAgIHRyeSB7XG4gICAgICBhd2FpdCBhZGRUb1doaXRlbGlzdCh3aGl0ZWxpc3RBZGRyZXNzKTtcbiAgICAgIHNldFN1Y2Nlc3MoYEFkZHJlc3MgJHt3aGl0ZWxpc3RBZGRyZXNzfSBhZGRlZCB0byB3aGl0ZWxpc3RgKTtcbiAgICAgIHNldFdoaXRlbGlzdEFkZHJlc3MoJycpO1xuICAgIH0gY2F0Y2ggKGVycjogYW55KSB7XG4gICAgICBzZXRFcnJvcihlcnIubWVzc2FnZSk7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldEFjdGlvbkxvYWRpbmcoZmFsc2UpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBoYW5kbGVSZW1vdmVGcm9tV2hpdGVsaXN0ID0gYXN5bmMgKCkgPT4ge1xuICAgIGlmICghd2hpdGVsaXN0QWRkcmVzcykgcmV0dXJuO1xuXG4gICAgc2V0QWN0aW9uTG9hZGluZyh0cnVlKTtcbiAgICB0cnkge1xuICAgICAgYXdhaXQgcmVtb3ZlRnJvbVdoaXRlbGlzdCh3aGl0ZWxpc3RBZGRyZXNzKTtcbiAgICAgIHNldFN1Y2Nlc3MoYEFkZHJlc3MgJHt3aGl0ZWxpc3RBZGRyZXNzfSByZW1vdmVkIGZyb20gd2hpdGVsaXN0YCk7XG4gICAgICBzZXRXaGl0ZWxpc3RBZGRyZXNzKCcnKTtcbiAgICB9IGNhdGNoIChlcnI6IGFueSkge1xuICAgICAgc2V0RXJyb3IoZXJyLm1lc3NhZ2UpO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRBY3Rpb25Mb2FkaW5nKGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgLy8gVXRpbGl0eSBmdW5jdGlvbnNcbiAgY29uc3QgZm9ybWF0QWRkcmVzcyA9IChhZGRyZXNzOiBzdHJpbmcpID0+IHtcbiAgICByZXR1cm4gYCR7YWRkcmVzcy5zbGljZSgwLCA2KX0uLi4ke2FkZHJlc3Muc2xpY2UoLTQpfWA7XG4gIH07XG5cbiAgY29uc3QgZm9ybWF0VGltZXN0YW1wID0gKHRpbWVzdGFtcDogbnVtYmVyKSA9PiB7XG4gICAgcmV0dXJuIG5ldyBEYXRlKHRpbWVzdGFtcCAqIDEwMDApLnRvTG9jYWxlU3RyaW5nKCk7XG4gIH07XG5cbiAgaWYgKCFwcm92aWRlciB8fCAhc2lnbmVyKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY29udGFpbmVyIG14LWF1dG8gcHgtNCBweS04XCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmcteWVsbG93LTUwIGJvcmRlciBib3JkZXIteWVsbG93LTIwMCByb3VuZGVkLWxnIHAtNiB0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtc2VtaWJvbGQgdGV4dC15ZWxsb3ctODAwIG1iLTRcIj5cbiAgICAgICAgICAgIPCflJcgV2FsbGV0IENvbm5lY3Rpb24gUmVxdWlyZWRcbiAgICAgICAgICA8L2gyPlxuICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteWVsbG93LTcwMCBtYi00XCI+XG4gICAgICAgICAgICBQbGVhc2UgY29ubmVjdCB5b3VyIE1ldGFNYXNrIHdhbGxldCB0byBtYW5hZ2UgdGhpcyBtb2R1bGFyIHRva2VuLlxuICAgICAgICAgIDwvcD5cbiAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICBvbkNsaWNrPXtpbml0aWFsaXplUHJvdmlkZXJ9XG4gICAgICAgICAgICBjbGFzc05hbWU9XCJiZy15ZWxsb3ctNjAwIGhvdmVyOmJnLXllbGxvdy03MDAgdGV4dC13aGl0ZSBmb250LWJvbGQgcHktMiBweC00IHJvdW5kZWRcIlxuICAgICAgICAgID5cbiAgICAgICAgICAgIENvbm5lY3QgV2FsbGV0XG4gICAgICAgICAgPC9idXR0b24+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgKTtcbiAgfVxuXG4gIGlmIChsb2FkaW5nKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY29udGFpbmVyIG14LWF1dG8gcHgtNCBweS04XCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWNlbnRlciBpdGVtcy1jZW50ZXIgaC02NFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYW5pbWF0ZS1zcGluIHJvdW5kZWQtZnVsbCBoLTMyIHctMzIgYm9yZGVyLWItMiBib3JkZXItYmx1ZS02MDBcIj48L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICApO1xuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cImNvbnRhaW5lciBteC1hdXRvIHB4LTQgcHktOFwiPlxuICAgICAgey8qIEhlYWRlciAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItOFwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuIGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICA8aDEgY2xhc3NOYW1lPVwidGV4dC0zeGwgZm9udC1ib2xkIHRleHQtZ3JheS05MDAgbWItMlwiPlxuICAgICAgICAgICAgICBNb2R1bGFyIFRva2VuIE1hbmFnZW1lbnRcbiAgICAgICAgICAgIDwvaDE+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwXCI+XG4gICAgICAgICAgICAgIFRva2VuIEFkZHJlc3M6IDxzcGFuIGNsYXNzTmFtZT1cImZvbnQtbW9ubyB0ZXh0LXNtXCI+e3Rva2VuQWRkcmVzc308L3NwYW4+XG4gICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICB7dG9rZW5JbmZvICYmIChcbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMFwiPlxuICAgICAgICAgICAgICAgIHt0b2tlbkluZm8ubmFtZX0gKHt0b2tlbkluZm8uc3ltYm9sfSkgLSBWZXJzaW9uIHt0b2tlbkluZm8udmVyc2lvbn1cbiAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIEVycm9yL1N1Y2Nlc3MgTWVzc2FnZXMgKi99XG4gICAgICB7ZXJyb3IgJiYgKFxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXJlZC01MCBib3JkZXIgYm9yZGVyLXJlZC0yMDAgcm91bmRlZC1sZyBwLTQgbWItNlwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleFwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LXNocmluay0wXCI+XG4gICAgICAgICAgICAgIDxzdmcgY2xhc3NOYW1lPVwiaC01IHctNSB0ZXh0LXJlZC00MDBcIiB2aWV3Qm94PVwiMCAwIDIwIDIwXCIgZmlsbD1cImN1cnJlbnRDb2xvclwiPlxuICAgICAgICAgICAgICAgIDxwYXRoIGZpbGxSdWxlPVwiZXZlbm9kZFwiIGQ9XCJNMTAgMThhOCA4IDAgMTAwLTE2IDggOCAwIDAwMCAxNnpNOC43MDcgNy4yOTNhMSAxIDAgMDAtMS40MTQgMS40MTRMOC41ODYgMTBsLTEuMjkzIDEuMjkzYTEgMSAwIDEwMS40MTQgMS40MTRMMTAgMTEuNDE0bDEuMjkzIDEuMjkzYTEgMSAwIDAwMS40MTQtMS40MTRMMTEuNDE0IDEwbDEuMjkzLTEuMjkzYTEgMSAwIDAwLTEuNDE0LTEuNDE0TDEwIDguNTg2IDguNzA3IDcuMjkzelwiIGNsaXBSdWxlPVwiZXZlbm9kZFwiIC8+XG4gICAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1sLTNcIj5cbiAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1yZWQtODAwXCI+RXJyb3I8L2gzPlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtcmVkLTcwMCBtdC0xXCI+e2Vycm9yfTwvcD5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgICl9XG5cbiAgICAgIHtzdWNjZXNzICYmIChcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmVlbi01MCBib3JkZXIgYm9yZGVyLWdyZWVuLTIwMCByb3VuZGVkLWxnIHAtNCBtYi02XCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4XCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtc2hyaW5rLTBcIj5cbiAgICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJoLTUgdy01IHRleHQtZ3JlZW4tNDAwXCIgdmlld0JveD1cIjAgMCAyMCAyMFwiIGZpbGw9XCJjdXJyZW50Q29sb3JcIj5cbiAgICAgICAgICAgICAgICA8cGF0aCBmaWxsUnVsZT1cImV2ZW5vZGRcIiBkPVwiTTEwIDE4YTggOCAwIDEwMC0xNiA4IDggMCAwMDAgMTZ6bTMuNzA3LTkuMjkzYTEgMSAwIDAwLTEuNDE0LTEuNDE0TDkgMTAuNTg2IDcuNzA3IDkuMjkzYTEgMSAwIDAwLTEuNDE0IDEuNDE0bDIgMmExIDEgMCAwMDEuNDE0IDBsNC00elwiIGNsaXBSdWxlPVwiZXZlbm9kZFwiIC8+XG4gICAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1sLTNcIj5cbiAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmVlbi04MDBcIj5TdWNjZXNzPC9oMz5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyZWVuLTcwMCBtdC0xXCI+e3N1Y2Nlc3N9PC9wPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgKX1cblxuICAgICAgey8qIFRva2VuIE92ZXJ2aWV3ICovfVxuICAgICAge3Rva2VuSW5mbyAmJiAoXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgcm91bmRlZC1sZyBzaGFkb3cgcC02IG1iLThcIj5cbiAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC14bCBmb250LXNlbWlib2xkIG1iLTRcIj5Ub2tlbiBPdmVydmlldzwvaDI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0zIGdhcC02XCI+XG4gICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTUwMFwiPlRvdGFsIFN1cHBseTwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LWJsdWUtNjAwXCI+XG4gICAgICAgICAgICAgICAge3Rva2VuSW5mby50b3RhbFN1cHBseX1cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNTAwXCI+TWF4IFN1cHBseTwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LWdyZWVuLTYwMFwiPlxuICAgICAgICAgICAgICAgIHt0b2tlbkluZm8ubWF4U3VwcGx5fVxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS01MDBcIj5TdGF0dXM8L2Rpdj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e2B0ZXh0LTJ4bCBmb250LWJvbGQgJHt0b2tlbkluZm8ucGF1c2VkID8gJ3RleHQtcmVkLTYwMCcgOiAndGV4dC1ncmVlbi02MDAnfWB9PlxuICAgICAgICAgICAgICAgIHt0b2tlbkluZm8ucGF1c2VkID8gJ1BhdXNlZCcgOiAnQWN0aXZlJ31cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICBcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTYgZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMiBnYXAtNFwiPlxuICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS01MDBcIj5Ub2tlbiBQcmljZTwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtXCI+e3Rva2VuSW5mby5tZXRhZGF0YS50b2tlblByaWNlfTwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTUwMFwiPkJvbnVzIFRpZXJzPC9kaXY+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW1cIj57dG9rZW5JbmZvLm1ldGFkYXRhLmJvbnVzVGllcnN9PC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICApfVxuXG4gICAgICB7LyogVGhpcyBpcyBhIGJhc2ljIHN0cnVjdHVyZSAtIHRoZSBmdWxsIGltcGxlbWVudGF0aW9uIHdvdWxkIGNvbnRpbnVlIGhlcmUgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIHJvdW5kZWQtbGcgc2hhZG93IHAtNlwiPlxuICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwXCI+XG4gICAgICAgICAgRnVsbCBtb2R1bGFyIHRva2VuIG1hbmFnZW1lbnQgaW50ZXJmYWNlIHdpbGwgYmUgaW1wbGVtZW50ZWQgaGVyZS5cbiAgICAgICAgICBUaGlzIGluY2x1ZGVzIG1pbnRpbmcsIHBhdXNpbmcsIGFkbWluIGNvbnRyb2xzLCBhbmQgdXBncmFkZSBtYW5hZ2VtZW50LlxuICAgICAgICA8L3A+XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZVN0YXRlIiwidXNlUGFyYW1zIiwidXNlTW9kdWxhclRva2VuIiwiTW9kdWxhclRva2VuRGV0YWlsc1BhZ2UiLCJwYXJhbXMiLCJ0b2tlbkFkZHJlc3MiLCJhZGRyZXNzIiwicHJvdmlkZXIiLCJzaWduZXIiLCJ0b2tlbkluZm8iLCJ1cGdyYWRlSW5mbyIsInBlbmRpbmdVcGdyYWRlcyIsInVwZ3JhZGVIaXN0b3J5IiwibG9hZGluZyIsImVycm9yIiwiaW5pdGlhbGl6ZVByb3ZpZGVyIiwibWludFRva2VucyIsInRvZ2dsZVBhdXNlIiwic2NoZWR1bGVVcGdyYWRlIiwidG9nZ2xlRW1lcmdlbmN5TW9kZSIsInJlZnJlc2hEYXRhIiwic2V0RXJyb3IiLCJ1cGRhdGVUb2tlblByaWNlIiwidXBkYXRlQm9udXNUaWVycyIsInVwZGF0ZU1heFN1cHBseSIsImFkZFRvV2hpdGVsaXN0IiwicmVtb3ZlRnJvbVdoaXRlbGlzdCIsInN1Y2Nlc3MiLCJzZXRTdWNjZXNzIiwibWludEFtb3VudCIsInNldE1pbnRBbW91bnQiLCJtaW50QWRkcmVzcyIsInNldE1pbnRBZGRyZXNzIiwidXBncmFkZURlc2NyaXB0aW9uIiwic2V0VXBncmFkZURlc2NyaXB0aW9uIiwibmV3SW1wbGVtZW50YXRpb25BZGRyZXNzIiwic2V0TmV3SW1wbGVtZW50YXRpb25BZGRyZXNzIiwiYWN0aW9uTG9hZGluZyIsInNldEFjdGlvbkxvYWRpbmciLCJhY3RpdmVUYWIiLCJzZXRBY3RpdmVUYWIiLCJreWNVc2VyQWRkcmVzcyIsInNldEt5Y1VzZXJBZGRyZXNzIiwiY2xhaW1Vc2VyQWRkcmVzcyIsInNldENsYWltVXNlckFkZHJlc3MiLCJjbGFpbVRvcGljSWQiLCJzZXRDbGFpbVRvcGljSWQiLCJjbGFpbURhdGEiLCJzZXRDbGFpbURhdGEiLCJjaGVja1VzZXJBZGRyZXNzIiwic2V0Q2hlY2tVc2VyQWRkcmVzcyIsInZlcmlmaWNhdGlvblN0YXR1cyIsInNldFZlcmlmaWNhdGlvblN0YXR1cyIsIm5ld1Rva2VuUHJpY2UiLCJzZXROZXdUb2tlblByaWNlIiwibmV3Qm9udXNUaWVycyIsInNldE5ld0JvbnVzVGllcnMiLCJuZXdNYXhTdXBwbHkiLCJzZXROZXdNYXhTdXBwbHkiLCJ3aGl0ZWxpc3RBZGRyZXNzIiwic2V0V2hpdGVsaXN0QWRkcmVzcyIsInVzZUVmZmVjdCIsInRpbWVyIiwic2V0VGltZW91dCIsImNsZWFyVGltZW91dCIsImhhbmRsZU1pbnQiLCJjb25zb2xlIiwibWVzc2FnZSIsImhhbmRsZU1pbnRBUEkiLCJyZXNwb25zZSIsImZldGNoIiwibWV0aG9kIiwiaGVhZGVycyIsImJvZHkiLCJKU09OIiwic3RyaW5naWZ5IiwidG9BZGRyZXNzIiwiYW1vdW50IiwicmVzdWx0IiwianNvbiIsIm9rIiwiRXJyb3IiLCJ0eEhhc2giLCJoYW5kbGVQYXVzZVRvZ2dsZSIsInBhdXNlZCIsImhhbmRsZVVwZGF0ZVByaWNlIiwiZXJyIiwiaGFuZGxlVXBkYXRlQm9udXNUaWVycyIsImhhbmRsZVVwZGF0ZU1heFN1cHBseSIsImhhbmRsZUFkZFRvV2hpdGVsaXN0QWRtaW4iLCJoYW5kbGVSZW1vdmVGcm9tV2hpdGVsaXN0IiwiZm9ybWF0QWRkcmVzcyIsInNsaWNlIiwiZm9ybWF0VGltZXN0YW1wIiwidGltZXN0YW1wIiwiRGF0ZSIsInRvTG9jYWxlU3RyaW5nIiwiZGl2IiwiY2xhc3NOYW1lIiwiaDIiLCJwIiwiYnV0dG9uIiwib25DbGljayIsImgxIiwic3BhbiIsIm5hbWUiLCJzeW1ib2wiLCJ2ZXJzaW9uIiwic3ZnIiwidmlld0JveCIsImZpbGwiLCJwYXRoIiwiZmlsbFJ1bGUiLCJkIiwiY2xpcFJ1bGUiLCJoMyIsInRvdGFsU3VwcGx5IiwibWF4U3VwcGx5IiwibWV0YWRhdGEiLCJ0b2tlblByaWNlIiwiYm9udXNUaWVycyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/modular-tokens/[address]/page.tsx\n"));

/***/ })

});