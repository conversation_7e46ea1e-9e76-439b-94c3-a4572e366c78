import { ethers } from 'ethers';
import { NextRequest, NextResponse } from 'next/server';
import SecurityTokenCoreArtifact from '../../../../../contracts/SecurityTokenCore.json';

// Extract ABI from artifact
const SecurityTokenABI = SecurityTokenCoreArtifact.abi;

// Load private key from environment variable
const PRIVATE_KEY = process.env.CONTRACT_ADMIN_PRIVATE_KEY;
const RPC_URLS = {
  amoy: process.env.AMOY_RPC_URL || 'https://rpc-amoy.polygon.technology/',
  polygon: process.env.POLYGON_RPC_URL || 'https://polygon-rpc.com',
  unknown: process.env.AMOY_RPC_URL || 'https://rpc-amoy.polygon.technology/', // Default to Amoy
};

// Network chain IDs
const CHAIN_IDS = {
  amoy: 80002,
  polygon: 137,
  unknown: 80002, // Default to Amoy
};

// Vault address - this should be a controlled wallet where frozen tokens are stored
// The ideal approach would be to have a dedicated escrow contract for this purpose
// For now, we'll use a hardcoded address that should be managed by the admin
const VAULT_ADDRESSES = {
  amoy: process.env.AMOY_VAULT_ADDRESS || '******************************************', // Default to admin address
  polygon: process.env.POLYGON_VAULT_ADDRESS || '******************************************',
  unknown: process.env.AMOY_VAULT_ADDRESS || '******************************************'
};

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { 
      tokenAddress, 
      fromAddress,
      amount,
      network = 'amoy' 
    } = body;
    
    if (!tokenAddress || !fromAddress || !amount) {
      return NextResponse.json(
        { error: 'Token address, from address, and amount are required' },
        { status: 400 }
      );
    }
    
    if (!PRIVATE_KEY) {
      return NextResponse.json(
        {
          error: 'CONTRACT_ADMIN_PRIVATE_KEY environment variable not set',
          details: 'For security reasons, the API requires a secure method to sign transactions.',
          clientSideInstructions: true,
          message: 'The server is not configured with admin credentials.'
        },
        { status: 422 }
      );
    }
    
    // Validate addresses
    if (!ethers.isAddress(tokenAddress) || !ethers.isAddress(fromAddress)) {
      return NextResponse.json(
        { error: 'Invalid address format provided' },
        { status: 400 }
      );
    }

    // Get RPC URL for the specified network, defaulting to Amoy
    const actualNetwork = network === 'unknown' ? 'amoy' : network;
    const rpcUrl = RPC_URLS[actualNetwork as keyof typeof RPC_URLS] || RPC_URLS.amoy;
    const chainId = CHAIN_IDS[actualNetwork as keyof typeof CHAIN_IDS] || CHAIN_IDS.amoy;

    // Get vault address for the network
    const vaultAddress = VAULT_ADDRESSES[actualNetwork as keyof typeof VAULT_ADDRESSES] || VAULT_ADDRESSES.amoy;

    // Connect to the token contract first to get decimals
    const provider = new ethers.JsonRpcProvider(rpcUrl, {
      chainId,
      name: actualNetwork
    });

    const wallet = new ethers.Wallet(PRIVATE_KEY, provider);
    const tokenContract = new ethers.Contract(tokenAddress, SecurityTokenABI, wallet);

    // Get token decimals
    const decimals = await tokenContract.decimals();
    console.log(`Token decimals: ${decimals}`);

    // Validate amount
    let parsedAmount;
    try {
      parsedAmount = ethers.parseUnits(amount.toString(), decimals);
      console.log(`Parsed amount: ${parsedAmount.toString()}`);
    } catch (error) {
      return NextResponse.json(
        { error: 'Invalid amount format' },
        { status: 400 }
      );
    }
    
    console.log(`Using network: ${actualNetwork}, RPC URL: ${rpcUrl}, Chain ID: ${chainId}`);
    console.log(`Vault address: ${vaultAddress}`);

    // Ensure the network is connected
    const network_details = await provider.getNetwork();
    console.log(`Connected to network: ${network_details.name} (Chain ID: ${network_details.chainId})`);
    console.log(`Wallet address: ${wallet.address}`);
    
    // Check current balance of the address
    const currentBalance = await tokenContract.balanceOf(fromAddress);
    console.log(`Current balance of ${fromAddress}: ${ethers.formatUnits(currentBalance, decimals)}`);

    if (currentBalance < parsedAmount) {
      return NextResponse.json({
        success: false,
        error: `Insufficient balance. Address has ${ethers.formatUnits(currentBalance, decimals)} tokens but trying to freeze ${amount} tokens.`
      }, { status: 400 });
    }
    
    // Execute the force transfer using adminTransfer function
    console.log(`Freezing ${amount} tokens from ${fromAddress} by transferring to vault ${vaultAddress}...`);
    
    try {
      // Since forcedTransfer requires TransferController module, we'll use burn + mint pattern
      // This effectively "freezes" tokens by removing them from user and adding to vault
      console.log(`Freezing ${amount} tokens: burning from ${fromAddress} and minting to vault ${vaultAddress}...`);

      // Step 1: Burn tokens from the user's address using burnFrom
      console.log(`Step 1: Burning ${amount} tokens from ${fromAddress}...`);
      const burnTx = await tokenContract.burnFrom(fromAddress, parsedAmount);
      console.log(`Burn transaction hash: ${burnTx.hash}`);
      await burnTx.wait();

      // Step 2: Mint the same amount to the vault address
      console.log(`Step 2: Minting ${amount} tokens to vault ${vaultAddress}...`);
      const mintTx = await tokenContract.mint(vaultAddress, parsedAmount);
      console.log(`Mint transaction hash: ${mintTx.hash}`);

      // Wait for the mint transaction to be mined
      const receipt = await mintTx.wait();

      // Return the mint transaction as the primary transaction
      const tx = mintTx;
      
      return NextResponse.json({
        success: true,
        action: 'partialFreeze',
        txHash: tx.hash,
        blockNumber: receipt.blockNumber,
        from: fromAddress,
        to: vaultAddress,
        amount: amount
      });
      
    } catch (txError: any) {
      console.error('Transaction error:', txError);
      
      // Check if the issue might be a permissions problem
      try {
        const DEFAULT_ADMIN_ROLE = await tokenContract.DEFAULT_ADMIN_ROLE();
        const hasAdminRole = await tokenContract.hasRole(DEFAULT_ADMIN_ROLE, wallet.address);
        
        if (!hasAdminRole) {
          return NextResponse.json({
            success: false,
            error: "The connected wallet doesn't have the DEFAULT_ADMIN_ROLE required for partial freezing",
            details: `Please grant DEFAULT_ADMIN_ROLE to ${wallet.address} on the token contract`
          }, { status: 403 });
        }
      } catch (roleCheckError) {
        console.error('Role check error:', roleCheckError);
      }
      
      throw txError; // Re-throw to be caught by the outer catch
    }
    
  } catch (error: any) {
    console.error('Error freezing tokens:', error);
    return NextResponse.json(
      { error: error.message || 'An unknown error occurred' },
      { status: 500 }
    );
  }
} 