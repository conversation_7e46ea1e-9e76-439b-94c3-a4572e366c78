/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/claims/route";
exports.ids = ["app/api/claims/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fclaims%2Froute&page=%2Fapi%2Fclaims%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fclaims%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fclaims%2Froute&page=%2Fapi%2Fclaims%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fclaims%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_github_tokendev_newroo_admin_panel_src_app_api_claims_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/claims/route.ts */ \"(rsc)/./src/app/api/claims/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/claims/route\",\n        pathname: \"/api/claims\",\n        filename: \"route\",\n        bundlePath: \"app/api/claims/route\"\n    },\n    resolvedPagePath: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\api\\\\claims\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_github_tokendev_newroo_admin_panel_src_app_api_claims_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fclaims%2Froute&page=%2Fapi%2Fclaims%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fclaims%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/claims/route.ts":
/*!*************************************!*\
  !*** ./src/app/api/claims/route.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/providers/provider-jsonrpc.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/contract/contract.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/wallet/wallet.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/abi/abi-coder.js\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_1__);\n\n\n\nconst prisma = new _prisma_client__WEBPACK_IMPORTED_MODULE_1__.PrismaClient();\n// GET /api/claims - Get claims for an address\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const walletAddress = searchParams.get('walletAddress');\n        const claimType = searchParams.get('claimType');\n        if (!walletAddress) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Wallet address is required'\n            }, {\n                status: 400\n            });\n        }\n        // Get claim registry address from environment or database\n        const claimRegistryAddress = process.env.CLAIM_REGISTRY_ADDRESS || \"******************************************\";\n        if (!claimRegistryAddress) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Claim registry not configured'\n            }, {\n                status: 500\n            });\n        }\n        // Connect to blockchain\n        const provider = new ethers__WEBPACK_IMPORTED_MODULE_2__.JsonRpcProvider(process.env.POLYGON_AMOY_RPC_URL || \"https://rpc-amoy.polygon.technology/\");\n        const claimRegistryABI = [\n            \"function hasValidClaim(address subject, uint256 claimType) external view returns (bool)\",\n            \"function getClaimIds(address subject, uint256 claimType) external view returns (bytes32[])\",\n            \"function getClaim(address subject, uint256 claimType, bytes32 claimId) external view returns (tuple(uint256 claimType, address issuer, bytes signature, bytes data, string uri, uint256 issuedAt, uint256 expiresAt, bool revoked))\"\n        ];\n        const claimRegistry = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(claimRegistryAddress, claimRegistryABI, provider);\n        // Tokeny ERC-3643 Claim types\n        const CLAIM_TYPES = {\n            KYC_CLAIM: 1,\n            AML_CLAIM: 2,\n            IDENTITY_CLAIM: 3,\n            QUALIFICATION_CLAIM: 4,\n            ACCREDITATION_CLAIM: 5,\n            RESIDENCE_CLAIM: 6,\n            TOKEN_ISSUER_CLAIM: 7\n        };\n        const claims = [];\n        // If specific claim type requested\n        if (claimType && CLAIM_TYPES[claimType]) {\n            const typeId = CLAIM_TYPES[claimType];\n            const hasValid = await claimRegistry.hasValidClaim(walletAddress, typeId);\n            const claimIds = await claimRegistry.getClaimIds(walletAddress, typeId);\n            for (const claimId of claimIds){\n                try {\n                    const claim = await claimRegistry.getClaim(walletAddress, typeId, claimId);\n                    claims.push({\n                        claimId,\n                        claimType: claimType,\n                        claimTypeId: typeId,\n                        issuer: claim.issuer,\n                        issuedAt: new Date(Number(claim.issuedAt) * 1000),\n                        expiresAt: claim.expiresAt > 0 ? new Date(Number(claim.expiresAt) * 1000) : null,\n                        revoked: claim.revoked,\n                        valid: !claim.revoked && (claim.expiresAt === 0n || claim.expiresAt > BigInt(Math.floor(Date.now() / 1000))),\n                        data: claim.data,\n                        uri: claim.uri\n                    });\n                } catch (error) {\n                    console.error('Error fetching claim details:', error);\n                }\n            }\n        } else {\n            // Get all claim types\n            for (const [typeName, typeId] of Object.entries(CLAIM_TYPES)){\n                try {\n                    const hasValid = await claimRegistry.hasValidClaim(walletAddress, typeId);\n                    const claimIds = await claimRegistry.getClaimIds(walletAddress, typeId);\n                    for (const claimId of claimIds){\n                        try {\n                            const claim = await claimRegistry.getClaim(walletAddress, typeId, claimId);\n                            claims.push({\n                                claimId,\n                                claimType: typeName,\n                                claimTypeId: typeId,\n                                issuer: claim.issuer,\n                                issuedAt: new Date(Number(claim.issuedAt) * 1000),\n                                expiresAt: claim.expiresAt > 0 ? new Date(Number(claim.expiresAt) * 1000) : null,\n                                revoked: claim.revoked,\n                                valid: !claim.revoked && (claim.expiresAt === 0n || claim.expiresAt > BigInt(Math.floor(Date.now() / 1000))),\n                                data: claim.data,\n                                uri: claim.uri\n                            });\n                        } catch (error) {\n                            console.error('Error fetching claim details:', error);\n                        }\n                    }\n                } catch (error) {\n                    console.error(`Error checking ${typeName} claims:`, error);\n                }\n            }\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            walletAddress,\n            claims,\n            totalClaims: claims.length,\n            validClaims: claims.filter((c)=>c.valid).length\n        });\n    } catch (error) {\n        console.error('Error fetching claims:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to fetch claims'\n        }, {\n            status: 500\n        });\n    }\n}\n// POST /api/claims - Issue a new claim\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { walletAddress, claimType, data, uri, expiresAt } = body;\n        if (!walletAddress || !claimType) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Wallet address and claim type are required'\n            }, {\n                status: 400\n            });\n        }\n        // Get claim registry address\n        const claimRegistryAddress = process.env.CLAIM_REGISTRY_ADDRESS || \"******************************************\";\n        if (!claimRegistryAddress) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Claim registry not configured'\n            }, {\n                status: 500\n            });\n        }\n        // Connect to blockchain with admin wallet\n        const provider = new ethers__WEBPACK_IMPORTED_MODULE_2__.JsonRpcProvider(process.env.POLYGON_AMOY_RPC_URL || \"https://rpc-amoy.polygon.technology/\");\n        const adminWallet = new ethers__WEBPACK_IMPORTED_MODULE_4__.Wallet(process.env.CONTRACT_ADMIN_PRIVATE_KEY, provider);\n        const claimRegistryABI = [\n            \"function issueClaim(address subject, uint256 claimType, bytes calldata signature, bytes calldata data, string calldata uri, uint256 expiresAt) external returns (bytes32)\"\n        ];\n        const claimRegistry = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(claimRegistryAddress, claimRegistryABI, adminWallet);\n        // Get claim type from request (now using custom claim types)\n        const claimTypeId = parseInt(claimType);\n        if (isNaN(claimTypeId) || claimTypeId <= 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Invalid claim type ID'\n            }, {\n                status: 400\n            });\n        }\n        // Validate claim type exists\n        const claimRegistryValidationABI = [\n            \"function isValidClaimType(uint256 claimTypeId) external view returns (bool)\"\n        ];\n        const validationContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(claimRegistryAddress, claimRegistryValidationABI, provider);\n        const isValidType = await validationContract.isValidClaimType(claimTypeId);\n        if (!isValidType) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Invalid or inactive claim type'\n            }, {\n                status: 400\n            });\n        }\n        // Issue the claim\n        const encodedData = ethers__WEBPACK_IMPORTED_MODULE_5__.AbiCoder.defaultAbiCoder().encode([\n            'string',\n            'uint256'\n        ], [\n            data || 'APPROVED',\n            Math.floor(Date.now() / 1000)\n        ]);\n        const expirationTimestamp = expiresAt ? Math.floor(new Date(expiresAt).getTime() / 1000) : 0;\n        const tx = await claimRegistry.issueClaim(walletAddress, claimTypeId, '0x', encodedData, uri || '', expirationTimestamp);\n        const receipt = await tx.wait();\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            transactionHash: receipt.transactionHash,\n            walletAddress,\n            claimType,\n            claimTypeId,\n            message: 'Claim issued successfully'\n        });\n    } catch (error) {\n        console.error('Error issuing claim:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to issue claim'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/claims/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/ethers","vendor-chunks/@noble","vendor-chunks/@adraffy","vendor-chunks/aes-js"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fclaims%2Froute&page=%2Fapi%2Fclaims%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fclaims%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();