{"_format": "hh-sol-artifact-1", "contractName": "ModularTokenFactory", "sourceName": "contracts/ModularTokenFactory.sol", "abi": [{"inputs": [{"internalType": "address", "name": "_securityTokenImplementation", "type": "address"}, {"internalType": "address", "name": "_upgradeManager", "type": "address"}, {"internalType": "address", "name": "_admin", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "name": "AccessControlBadConfirmation", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "bytes32", "name": "neededRole", "type": "bytes32"}], "name": "AccessControlUnauthorizedAccount", "type": "error"}, {"inputs": [], "name": "ReentrancyGuardReentrantCall", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "previousAdminRole", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "newAdminRole", "type": "bytes32"}], "name": "RoleAdminChanged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleGranted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleRevoked", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "tokenAddress", "type": "address"}, {"indexed": true, "internalType": "address", "name": "admin", "type": "address"}], "name": "TokenDeactivated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "tokenAddress", "type": "address"}, {"indexed": true, "internalType": "address", "name": "deployer", "type": "address"}, {"indexed": true, "internalType": "address", "name": "admin", "type": "address"}, {"indexed": false, "internalType": "string", "name": "name", "type": "string"}, {"indexed": false, "internalType": "string", "name": "symbol", "type": "string"}, {"indexed": false, "internalType": "uint8", "name": "decimals", "type": "uint8"}, {"indexed": false, "internalType": "uint256", "name": "maxSupply", "type": "uint256"}], "name": "TokenDeployed", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "oldManager", "type": "address"}, {"indexed": true, "internalType": "address", "name": "newManager", "type": "address"}], "name": "UpgradeManagerUpdated", "type": "event"}, {"inputs": [], "name": "DEFAULT_ADMIN_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "DEPLOYER_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "tokenAddress", "type": "address"}], "name": "deactivateToken", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "string", "name": "name_", "type": "string"}, {"internalType": "string", "name": "symbol_", "type": "string"}, {"internalType": "uint8", "name": "decimals_", "type": "uint8"}, {"internalType": "uint256", "name": "maxSupply_", "type": "uint256"}, {"internalType": "address", "name": "admin_", "type": "address"}, {"internalType": "string", "name": "tokenPrice_", "type": "string"}, {"internalType": "string", "name": "bonusTiers_", "type": "string"}, {"internalType": "string", "name": "tokenDetails_", "type": "string"}, {"internalType": "string", "name": "tokenImageUrl_", "type": "string"}], "name": "deployToken", "outputs": [{"internalType": "address", "name": "tokenAddress", "type": "address"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "deployedTokens", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}, {"internalType": "uint256", "name": "", "type": "uint256"}], "name": "deployerTokens", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getActiveTokens", "outputs": [{"internalType": "address[]", "name": "activeTokens", "type": "address[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "offset", "type": "uint256"}, {"internalType": "uint256", "name": "limit", "type": "uint256"}], "name": "getDeployedTokens", "outputs": [{"internalType": "address[]", "name": "tokens", "type": "address[]"}, {"internalType": "bool", "name": "hasMore", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getDeployedTokensCount", "outputs": [{"internalType": "uint256", "name": "count", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "name": "getRoleAdmin", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "tokenAddress", "type": "address"}], "name": "getTokenInfo", "outputs": [{"components": [{"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "symbol", "type": "string"}, {"internalType": "uint8", "name": "decimals", "type": "uint8"}, {"internalType": "uint256", "name": "maxSupply", "type": "uint256"}, {"internalType": "address", "name": "admin", "type": "address"}, {"internalType": "address", "name": "deployer", "type": "address"}, {"internalType": "uint256", "name": "deploymentTime", "type": "uint256"}, {"internalType": "bool", "name": "isActive", "type": "bool"}], "internalType": "struct ModularTokenFactory.TokenInfo", "name": "info", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "deployer", "type": "address"}], "name": "getTokensByDeployer", "outputs": [{"internalType": "address[]", "name": "tokens", "type": "address[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "grantRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "hasRole", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "tokenAddress", "type": "address"}], "name": "isDeployedToken", "outputs": [{"internalType": "bool", "name": "isDeployed", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "callerConfirmation", "type": "address"}], "name": "renounceRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "revokeRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "securityTokenImplementation", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "newUpgradeManager", "type": "address"}], "name": "setUpgradeManager", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "interfaceId", "type": "bytes4"}], "name": "supportsInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "tokenInfo", "outputs": [{"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "symbol", "type": "string"}, {"internalType": "uint8", "name": "decimals", "type": "uint8"}, {"internalType": "uint256", "name": "maxSupply", "type": "uint256"}, {"internalType": "address", "name": "admin", "type": "address"}, {"internalType": "address", "name": "deployer", "type": "address"}, {"internalType": "uint256", "name": "deploymentTime", "type": "uint256"}, {"internalType": "bool", "name": "isActive", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "upgradeManager", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}