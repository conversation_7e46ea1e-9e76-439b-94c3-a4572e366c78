/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/auth/generate-key/route";
exports.ids = ["app/api/auth/generate-key/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Fgenerate-key%2Froute&page=%2Fapi%2Fauth%2Fgenerate-key%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fgenerate-key%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Fgenerate-key%2Froute&page=%2Fapi%2Fauth%2Fgenerate-key%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fgenerate-key%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_github_tokendev_newroo_admin_panel_src_app_api_auth_generate_key_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/auth/generate-key/route.ts */ \"(rsc)/./src/app/api/auth/generate-key/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/auth/generate-key/route\",\n        pathname: \"/api/auth/generate-key\",\n        filename: \"route\",\n        bundlePath: \"app/api/auth/generate-key/route\"\n    },\n    resolvedPagePath: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\api\\\\auth\\\\generate-key\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_github_tokendev_newroo_admin_panel_src_app_api_auth_generate_key_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Fgenerate-key%2Froute&page=%2Fapi%2Fauth%2Fgenerate-key%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fgenerate-key%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/auth/generate-key/route.ts":
/*!************************************************!*\
  !*** ./src/app/api/auth/generate-key/route.ts ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST),\n/* harmony export */   validateApiKey: () => (/* binding */ validateApiKey)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! crypto */ \"crypto\");\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(crypto__WEBPACK_IMPORTED_MODULE_1__);\n\n\n// Simple in-memory storage for demo (use database in production)\nconst apiKeys = new Map();\n// Generate API key\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { name, permissions = [\n            'read'\n        ] } = body;\n        if (!name) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'API key name is required'\n            }, {\n                status: 400\n            });\n        }\n        // Generate a secure API key\n        const apiKey = `ak_${(0,crypto__WEBPACK_IMPORTED_MODULE_1__.randomBytes)(32).toString('hex')}`;\n        const keyHash = (0,crypto__WEBPACK_IMPORTED_MODULE_1__.createHash)('sha256').update(apiKey).digest('hex');\n        // Store API key info (hash only, not the actual key)\n        apiKeys.set(apiKey, {\n            keyHash,\n            name,\n            permissions,\n            createdAt: new Date(),\n            isActive: true\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: {\n                apiKey,\n                name,\n                permissions,\n                createdAt: new Date().toISOString(),\n                usage: {\n                    endpoint: '/api/external/*',\n                    header: 'Authorization: Bearer ' + apiKey,\n                    example: `curl -H \"Authorization: Bearer ${apiKey}\" https://your-domain.com/api/external/tokens`\n                }\n            }\n        });\n    } catch (error) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to generate API key',\n            message: error.message\n        }, {\n            status: 500\n        });\n    }\n}\n// List API keys (without showing actual keys)\nasync function GET() {\n    try {\n        const keys = Array.from(apiKeys.entries()).map(([key, data])=>({\n                keyId: key.substring(0, 12) + '...',\n                name: data.name,\n                permissions: data.permissions,\n                createdAt: data.createdAt.toISOString(),\n                lastUsed: data.lastUsed?.toISOString(),\n                isActive: data.isActive\n            }));\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: {\n                keys,\n                totalCount: keys.length\n            }\n        });\n    } catch (error) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to list API keys',\n            message: error.message\n        }, {\n            status: 500\n        });\n    }\n}\n// Validate API key (internal function)\nfunction validateApiKey(apiKey) {\n    const keyData = apiKeys.get(apiKey);\n    if (!keyData || !keyData.isActive) {\n        return {\n            valid: false,\n            permissions: []\n        };\n    }\n    // Update last used timestamp\n    keyData.lastUsed = new Date();\n    apiKeys.set(apiKey, keyData);\n    return {\n        valid: true,\n        permissions: keyData.permissions\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/auth/generate-key/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Fgenerate-key%2Froute&page=%2Fapi%2Fauth%2Fgenerate-key%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fgenerate-key%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();