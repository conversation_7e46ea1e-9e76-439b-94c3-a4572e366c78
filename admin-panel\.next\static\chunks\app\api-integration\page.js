/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/api-integration/page"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Csrc%5C%5Capp%5C%5Capi-integration%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!****************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Csrc%5C%5Capp%5C%5Capi-integration%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/api-integration/page.tsx */ \"(app-pages-browser)/./src/app/api-integration/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRCUzQSU1QyU1Q2dpdGh1YiU1QyU1Q3Rva2VuZGV2LW5ld3JvbyU1QyU1Q2FkbWluLXBhbmVsJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDYXBpLWludGVncmF0aW9uJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPWZhbHNlISIsIm1hcHBpbmdzIjoiQUFBQSw4TEFBa0giLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXGdpdGh1YlxcXFx0b2tlbmRldi1uZXdyb29cXFxcYWRtaW4tcGFuZWxcXFxcc3JjXFxcXGFwcFxcXFxhcGktaW50ZWdyYXRpb25cXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Csrc%5C%5Capp%5C%5Capi-integration%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n true &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      \"react-stack-bottom-frame\": function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React[\"react-stack-bottom-frame\"].bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSw4TEFBc0U7QUFDeEUiLCJzb3VyY2VzIjpbIkQ6XFxnaXRodWJcXHRva2VuZGV2LW5ld3Jvb1xcYWRtaW4tcGFuZWxcXG5vZGVfbW9kdWxlc1xcbmV4dFxcZGlzdFxcY29tcGlsZWRcXHJlYWN0XFxqc3gtZGV2LXJ1bnRpbWUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09ICdwcm9kdWN0aW9uJykge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vY2pzL3JlYWN0LWpzeC1kZXYtcnVudGltZS5wcm9kdWN0aW9uLmpzJyk7XG59IGVsc2Uge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vY2pzL3JlYWN0LWpzeC1kZXYtcnVudGltZS5kZXZlbG9wbWVudC5qcycpO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/api-integration/page.tsx":
/*!******************************************!*\
  !*** ./src/app/api-integration/page.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ APIIntegrationPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction APIIntegrationPage() {\n    _s();\n    const [testResults, setTestResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isRunningTests, setIsRunningTests] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    // Define API endpoints to test\n    const apiEndpoints = [\n        // Status & Health\n        {\n            name: 'System Status',\n            method: 'GET',\n            path: '/api/status',\n            description: 'Check system health and status',\n            category: 'System',\n            expectedStatus: 200\n        },\n        // Token Management\n        {\n            name: 'Get Tokens',\n            method: 'GET',\n            path: '/api/tokens',\n            description: 'Retrieve all deployed tokens',\n            category: 'Tokens',\n            expectedStatus: 200\n        },\n        {\n            name: 'Get Modular Tokens',\n            method: 'GET',\n            path: '/api/modular-tokens',\n            description: 'Retrieve modular token information',\n            category: 'Tokens',\n            expectedStatus: 200\n        },\n        // Admin Functions\n        {\n            name: 'Token Info',\n            method: 'GET',\n            path: '/api/admin/token-info',\n            description: 'Get detailed token information',\n            category: 'Admin',\n            expectedStatus: 200\n        },\n        // Client Management\n        {\n            name: 'Get Clients',\n            method: 'GET',\n            path: '/api/clients',\n            description: 'Retrieve client list',\n            category: 'Clients',\n            expectedStatus: 200\n        },\n        {\n            name: 'Client Stats',\n            method: 'GET',\n            path: '/api/clients/stats',\n            description: 'Get client statistics',\n            category: 'Clients',\n            expectedStatus: 200\n        },\n        // KYC & Claims\n        {\n            name: 'KYC Status',\n            method: 'GET',\n            path: '/api/kyc/status',\n            description: 'Check KYC status endpoint',\n            category: 'KYC',\n            expectedStatus: 200\n        },\n        {\n            name: 'Claims',\n            method: 'GET',\n            path: '/api/claims',\n            description: 'Retrieve claims information',\n            category: 'Claims',\n            expectedStatus: 200\n        },\n        {\n            name: 'Claim Types',\n            method: 'GET',\n            path: '/api/claim-types',\n            description: 'Get available claim types',\n            category: 'Claims',\n            expectedStatus: 200\n        },\n        // Orders\n        {\n            name: 'Get Orders',\n            method: 'GET',\n            path: '/api/orders',\n            description: 'Retrieve order information',\n            category: 'Orders',\n            expectedStatus: 200\n        },\n        // Qualifications\n        {\n            name: 'Get Qualifications',\n            method: 'GET',\n            path: '/api/qualifications',\n            description: 'Retrieve qualification data',\n            category: 'Qualifications',\n            expectedStatus: 200\n        },\n        {\n            name: 'Qualification Progress',\n            method: 'GET',\n            path: '/api/qualification-progress',\n            description: 'Get qualification progress',\n            category: 'Qualifications',\n            expectedStatus: 200\n        },\n        // Identity & Whitelist\n        {\n            name: 'Identity',\n            method: 'GET',\n            path: '/api/identity',\n            description: 'Get identity information',\n            category: 'Identity',\n            expectedStatus: 200\n        },\n        {\n            name: 'Whitelist Check',\n            method: 'GET',\n            path: '/api/whitelist/check',\n            description: 'Check whitelist status',\n            category: 'Whitelist',\n            expectedStatus: 200\n        },\n        // Token Agreements\n        {\n            name: 'Token Agreements',\n            method: 'GET',\n            path: '/api/token-agreements',\n            description: 'Get token agreements',\n            category: 'Agreements',\n            expectedStatus: 200\n        }\n    ];\n    // Initialize test results\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"APIIntegrationPage.useEffect\": ()=>{\n            const initialResults = apiEndpoints.map({\n                \"APIIntegrationPage.useEffect.initialResults\": (endpoint)=>({\n                        endpoint,\n                        status: 'pending'\n                    })\n            }[\"APIIntegrationPage.useEffect.initialResults\"]);\n            setTestResults(initialResults);\n        }\n    }[\"APIIntegrationPage.useEffect\"], []);\n    // Test single endpoint\n    const testEndpoint = async (endpoint)=>{\n        const startTime = Date.now();\n        try {\n            const response = await fetch(endpoint.path, {\n                method: endpoint.method,\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                ...endpoint.testData && {\n                    body: JSON.stringify(endpoint.testData)\n                }\n            });\n            const responseTime = Date.now() - startTime;\n            let responseData;\n            try {\n                responseData = await response.json();\n            } catch (e) {\n                responseData = await response.text();\n            }\n            const passed = endpoint.expectedStatus ? response.status === endpoint.expectedStatus : response.ok;\n            return {\n                endpoint,\n                status: passed ? 'passed' : 'failed',\n                responseStatus: response.status,\n                responseTime,\n                responseData,\n                error: passed ? undefined : \"Expected \".concat(endpoint.expectedStatus, \", got \").concat(response.status)\n            };\n        } catch (error) {\n            return {\n                endpoint,\n                status: 'failed',\n                responseTime: Date.now() - startTime,\n                error: error.message\n            };\n        }\n    };\n    // Run all tests\n    const runAllTests = async ()=>{\n        setIsRunningTests(true);\n        for(let i = 0; i < apiEndpoints.length; i++){\n            const endpoint = apiEndpoints[i];\n            // Update status to running\n            setTestResults((prev)=>prev.map((result, index)=>index === i ? {\n                        ...result,\n                        status: 'running'\n                    } : result));\n            // Run test\n            const result = await testEndpoint(endpoint);\n            // Update with result\n            setTestResults((prev)=>prev.map((prevResult, index)=>index === i ? result : prevResult));\n            // Add delay between tests\n            await new Promise((resolve)=>setTimeout(resolve, 500));\n        }\n        setIsRunningTests(false);\n    };\n    // Run tests for specific category\n    const runCategoryTests = async (category)=>{\n        setIsRunningTests(true);\n        const categoryEndpoints = apiEndpoints.map((endpoint, index)=>({\n                endpoint,\n                index\n            })).filter((param)=>{\n            let { endpoint } = param;\n            return category === 'all' || endpoint.category === category;\n        });\n        for (const { endpoint, index } of categoryEndpoints){\n            // Update status to running\n            setTestResults((prev)=>prev.map((result, i)=>i === index ? {\n                        ...result,\n                        status: 'running'\n                    } : result));\n            // Run test\n            const result = await testEndpoint(endpoint);\n            // Update with result\n            setTestResults((prev)=>prev.map((prevResult, i)=>i === index ? result : prevResult));\n            // Add delay between tests\n            await new Promise((resolve)=>setTimeout(resolve, 500));\n        }\n        setIsRunningTests(false);\n    };\n    // Get unique categories\n    const categories = [\n        'all',\n        ...Array.from(new Set(apiEndpoints.map((e)=>e.category)))\n    ];\n    // Filter results by category\n    const filteredResults = selectedCategory === 'all' ? testResults : testResults.filter((result)=>result.endpoint.category === selectedCategory);\n    // Calculate stats\n    const totalTests = filteredResults.length;\n    const passedTests = filteredResults.filter((r)=>r.status === 'passed').length;\n    const failedTests = filteredResults.filter((r)=>r.status === 'failed').length;\n    const pendingTests = filteredResults.filter((r)=>r.status === 'pending').length;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto p-6 space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold\",\n                                children: \"API Integration Testing\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\api-integration\\\\page.tsx\",\n                                lineNumber: 298,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: \"Test all API endpoints for functionality and performance\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\api-integration\\\\page.tsx\",\n                                lineNumber: 299,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\api-integration\\\\page.tsx\",\n                        lineNumber: 297,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                value: selectedCategory,\n                                onChange: (e)=>setSelectedCategory(e.target.value),\n                                className: \"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: category,\n                                        children: category === 'all' ? 'All Categories' : category\n                                    }, category, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\api-integration\\\\page.tsx\",\n                                        lineNumber: 310,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\api-integration\\\\page.tsx\",\n                                lineNumber: 304,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>runCategoryTests(selectedCategory),\n                                disabled: isRunningTests,\n                                className: \"bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50\",\n                                children: isRunningTests ? '🔄 Running Tests...' : '🧪 Run Tests'\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\api-integration\\\\page.tsx\",\n                                lineNumber: 315,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\api-integration\\\\page.tsx\",\n                        lineNumber: 303,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\api-integration\\\\page.tsx\",\n                lineNumber: 296,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-2 md:grid-cols-4 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow p-6 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-3xl font-bold text-blue-600\",\n                                children: totalTests\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\api-integration\\\\page.tsx\",\n                                lineNumber: 328,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-600\",\n                                children: \"Total Tests\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\api-integration\\\\page.tsx\",\n                                lineNumber: 329,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\api-integration\\\\page.tsx\",\n                        lineNumber: 327,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow p-6 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-3xl font-bold text-green-600\",\n                                children: passedTests\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\api-integration\\\\page.tsx\",\n                                lineNumber: 333,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-600\",\n                                children: \"Passed\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\api-integration\\\\page.tsx\",\n                                lineNumber: 334,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\api-integration\\\\page.tsx\",\n                        lineNumber: 332,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow p-6 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-3xl font-bold text-red-600\",\n                                children: failedTests\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\api-integration\\\\page.tsx\",\n                                lineNumber: 338,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-600\",\n                                children: \"Failed\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\api-integration\\\\page.tsx\",\n                                lineNumber: 339,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\api-integration\\\\page.tsx\",\n                        lineNumber: 337,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow p-6 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-3xl font-bold text-gray-600\",\n                                children: pendingTests\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\api-integration\\\\page.tsx\",\n                                lineNumber: 343,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-600\",\n                                children: \"Pending\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\api-integration\\\\page.tsx\",\n                                lineNumber: 344,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\api-integration\\\\page.tsx\",\n                        lineNumber: 342,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\api-integration\\\\page.tsx\",\n                lineNumber: 326,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold mb-4\",\n                        children: [\n                            \"API Test Results\",\n                            selectedCategory !== 'all' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-normal text-gray-600\",\n                                children: [\n                                    \" - \",\n                                    selectedCategory\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\api-integration\\\\page.tsx\",\n                                lineNumber: 353,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\api-integration\\\\page.tsx\",\n                        lineNumber: 350,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: filteredResults.map((result, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-start\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-4 h-4 rounded-full flex items-center justify-center text-xs \".concat(result.status === 'passed' ? 'bg-green-500 text-white' : result.status === 'failed' ? 'bg-red-500 text-white' : result.status === 'running' ? 'bg-blue-500 text-white animate-pulse' : 'bg-gray-300 text-gray-600'),\n                                                                children: result.status === 'passed' ? '✓' : result.status === 'failed' ? '✗' : result.status === 'running' ? '⟳' : '○'\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\api-integration\\\\page.tsx\",\n                                                                lineNumber: 363,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium\",\n                                                                        children: result.endpoint.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\api-integration\\\\page.tsx\",\n                                                                        lineNumber: 375,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"ml-2 px-2 py-1 text-xs rounded \".concat(result.endpoint.method === 'GET' ? 'bg-blue-100 text-blue-800' : result.endpoint.method === 'POST' ? 'bg-green-100 text-green-800' : result.endpoint.method === 'PUT' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800'),\n                                                                        children: result.endpoint.method\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\api-integration\\\\page.tsx\",\n                                                                        lineNumber: 376,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"ml-2 text-xs bg-gray-100 text-gray-800 px-2 py-1 rounded\",\n                                                                        children: result.endpoint.category\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\api-integration\\\\page.tsx\",\n                                                                        lineNumber: 384,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\api-integration\\\\page.tsx\",\n                                                                lineNumber: 374,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\api-integration\\\\page.tsx\",\n                                                        lineNumber: 362,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600 mt-1\",\n                                                        children: result.endpoint.description\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\api-integration\\\\page.tsx\",\n                                                        lineNumber: 389,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500 mt-1 font-mono\",\n                                                        children: result.endpoint.path\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\api-integration\\\\page.tsx\",\n                                                        lineNumber: 390,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    result.responseTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500 mt-1\",\n                                                        children: [\n                                                            \"Response time: \",\n                                                            result.responseTime,\n                                                            \"ms\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\api-integration\\\\page.tsx\",\n                                                        lineNumber: 393,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    result.error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-red-600 mt-1\",\n                                                        children: [\n                                                            \"Error: \",\n                                                            result.error\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\api-integration\\\\page.tsx\",\n                                                        lineNumber: 399,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\api-integration\\\\page.tsx\",\n                                                lineNumber: 361,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-right\",\n                                                children: result.responseStatus && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full \".concat(result.responseStatus >= 200 && result.responseStatus < 300 ? 'bg-green-100 text-green-800' : result.responseStatus >= 400 ? 'bg-red-100 text-red-800' : 'bg-yellow-100 text-yellow-800'),\n                                                    children: result.responseStatus\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\api-integration\\\\page.tsx\",\n                                                    lineNumber: 405,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\api-integration\\\\page.tsx\",\n                                                lineNumber: 403,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\api-integration\\\\page.tsx\",\n                                        lineNumber: 360,\n                                        columnNumber: 15\n                                    }, this),\n                                    result.responseData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"details\", {\n                                        className: \"mt-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"summary\", {\n                                                className: \"text-xs text-gray-500 cursor-pointer\",\n                                                children: \"View Response Data\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\api-integration\\\\page.tsx\",\n                                                lineNumber: 418,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                className: \"text-xs bg-gray-50 p-2 rounded mt-1 overflow-auto max-h-40\",\n                                                children: typeof result.responseData === 'string' ? result.responseData : JSON.stringify(result.responseData, null, 2)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\api-integration\\\\page.tsx\",\n                                                lineNumber: 419,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\api-integration\\\\page.tsx\",\n                                        lineNumber: 417,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, index, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\api-integration\\\\page.tsx\",\n                                lineNumber: 359,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\api-integration\\\\page.tsx\",\n                        lineNumber: 357,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\api-integration\\\\page.tsx\",\n                lineNumber: 349,\n                columnNumber: 7\n            }, this),\n            filteredResults.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-50 rounded-lg p-8 text-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-500\",\n                    children: \"No tests available for the selected category\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\api-integration\\\\page.tsx\",\n                    lineNumber: 433,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\api-integration\\\\page.tsx\",\n                lineNumber: 432,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\api-integration\\\\page.tsx\",\n        lineNumber: 294,\n        columnNumber: 5\n    }, this);\n}\n_s(APIIntegrationPage, \"SdUS+SdQRWEa+KrtxPDYclpkD2g=\");\n_c = APIIntegrationPage;\nvar _c;\n$RefreshReg$(_c, \"APIIntegrationPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/api-integration/page.tsx\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Csrc%5C%5Capp%5C%5Capi-integration%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);