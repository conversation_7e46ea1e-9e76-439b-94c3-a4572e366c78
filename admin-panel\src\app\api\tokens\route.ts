import { NextRequest, NextResponse } from 'next/server';
import { ethers } from 'ethers';
import { prisma } from '../../../lib/prisma';
import SecurityTokenFactoryArtifact from '../../../contracts/SecurityTokenFactory.json';
import SecurityTokenArtifact from '../../../contracts/SecurityToken.json';
import ModularTokenFactoryArtifact from '../../../contracts/ModularTokenFactory.json';
import SecurityTokenCoreArtifact from '../../../contracts/SecurityTokenCore.json';

// Extract ABIs from artifacts
const SecurityTokenFactoryABI = SecurityTokenFactoryArtifact.abi;
const SecurityTokenABI = SecurityTokenArtifact.abi;
const ModularTokenFactoryABI = ModularTokenFactoryArtifact.abi;
const SecurityTokenCoreABI = SecurityTokenCoreArtifact.abi;

// RPC URLs for different networks
const RPC_URLS = {
  amoy: process.env.AMOY_RPC_URL || 'https://rpc-amoy.polygon.technology/',
  polygon: process.env.POLYGON_RPC_URL || 'https://polygon-rpc.com',
  unknown: process.env.AMOY_RPC_URL || 'https://rpc-amoy.polygon.technology/',
};

// Factory contract addresses
const FACTORY_ADDRESS = process.env.FACTORY_ADDRESS || '******************************************';
const MODULAR_FACTORY_ADDRESS = process.env.AMOY_MODULAR_TOKEN_FACTORY_ADDRESS || '******************************************';

// Helper function to determine if an address looks like a real contract address
function isRealContractAddress(address: string): boolean {
  // Skip obvious test addresses
  if (!address || address === ethers.ZeroAddress) {
    return false;
  }

  // Skip addresses that are clearly test patterns (all zeros with small numbers)
  const testPatterns = [
    /^0x0+[0-9a-f]{1,2}$/i,  // 0x000...0018, 0x000...006, etc.
    /^******************************************$/i, // Our test address
    /^0x0+$/i // All zeros
  ];

  return !testPatterns.some(pattern => pattern.test(address));
}

// Helper function to fetch modular tokens from the ModularTokenFactory
async function fetchModularTokens(provider: ethers.JsonRpcProvider) {
  try {
    console.log(`Fetching modular tokens from factory: ${MODULAR_FACTORY_ADDRESS}`);

    const modularFactory = new ethers.Contract(MODULAR_FACTORY_ADDRESS, ModularTokenFactoryABI, provider);

    // Get token count
    const tokenCount = await modularFactory.getDeployedTokensCount();
    console.log(`Modular factory reports ${tokenCount} deployed tokens`);

    if (tokenCount === 0) {
      return [];
    }

    // Get all active tokens
    const activeTokens = await modularFactory.getActiveTokens();
    console.log("Retrieved modular token addresses:", activeTokens);

    const tokens = [];

    for (const address of activeTokens) {
      try {
        // Get token info from factory
        const tokenInfo = await modularFactory.getTokenInfo(address);

        // Get additional details from the token contract
        const tokenContract = new ethers.Contract(address, SecurityTokenCoreABI, provider);

        const [totalSupply, version] = await Promise.all([
          tokenContract.totalSupply(),
          tokenContract.version().catch(() => "1.0.0")
        ]);

        // Get metadata if available
        let metadata = null;
        try {
          metadata = await tokenContract.getMetadata();
        } catch (e) {
          // Metadata might not be available
        }

        tokens.push({
          id: `modular-${address}`,
          address,
          name: tokenInfo.name,
          symbol: tokenInfo.symbol,
          decimals: Number(tokenInfo.decimals),
          maxSupply: ethers.formatUnits(tokenInfo.maxSupply, tokenInfo.decimals),
          totalSupply: ethers.formatUnits(totalSupply, tokenInfo.decimals),
          tokenType: 'Modular Security Token',
          tokenPrice: metadata?.tokenPrice || 'N/A',
          currency: 'USD', // Default, could be parsed from tokenPrice
          network: 'amoy',
          hasKYC: true, // Modular tokens support KYC
          isActive: tokenInfo.isActive,
          adminAddress: tokenInfo.admin,
          whitelistAddress: null, // Would need to get from modules
          transactionHash: null, // Could get from deployment events
          blockNumber: null,
          createdAt: new Date(Number(tokenInfo.deploymentTime) * 1000).toISOString(),
          updatedAt: new Date().toISOString(),
          version: version,
          isModular: true
        });

      } catch (error) {
        console.warn(`Failed to load modular token details for ${address}:`, error);
      }
    }

    console.log(`Successfully loaded ${tokens.length} modular tokens`);
    return tokens;

  } catch (error) {
    console.error('Error fetching modular tokens:', error);
    return [];
  }
}

// GET /api/tokens - Get all tokens from database and optionally sync with blockchain
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const source = searchParams.get('source') || 'database'; // 'database', 'blockchain', or 'both'

    if (source === 'database' || source === 'both') {
      // Get tokens from database
      const dbTokens = await prisma.token.findMany({
        orderBy: { createdAt: 'desc' },
        include: {
          transactions: {
            take: 5,
            orderBy: { createdAt: 'desc' }
          }
        }
      });

      if (source === 'database') {
        // Sync totalSupply from blockchain for database tokens if needed
        const tokensWithUpdatedSupply = await Promise.all(
          dbTokens.map(async (token) => {
            // Only attempt blockchain sync for tokens that:
            // 1. Have totalSupply of 0 or not set
            // 2. Have a real-looking address (not test addresses)
            // 3. Are on a supported network
            const shouldSyncFromBlockchain = (
              (token.totalSupply === '0' || !token.totalSupply) &&
              isRealContractAddress(token.address) &&
              (token.network === 'amoy' || token.network === 'polygon')
            );

            if (shouldSyncFromBlockchain) {
              try {
                const network = token.network || 'amoy';
                const rpcUrl = RPC_URLS[network as keyof typeof RPC_URLS] || RPC_URLS.amoy;
                const provider = new ethers.JsonRpcProvider(rpcUrl);

                // First check if there's code at this address
                const code = await provider.getCode(token.address);
                if (code === '0x') {
                  console.log(`Skipping blockchain sync for ${token.address} - no contract deployed`);
                  return token;
                }

                const tokenContract = new ethers.Contract(token.address, SecurityTokenABI, provider);

                const totalSupplyRaw = await tokenContract.totalSupply();
                const decimals = token.decimals || 18;
                const totalSupply = decimals === 0
                  ? totalSupplyRaw.toString()
                  : ethers.formatUnits(totalSupplyRaw, decimals);

                console.log(`Successfully synced totalSupply for ${token.symbol}: ${totalSupply}`);

                // Update the database with the fetched totalSupply
                await prisma.token.update({
                  where: { id: token.id },
                  data: { totalSupply }
                });

                return { ...token, totalSupply };
              } catch (error) {
                console.warn(`Could not fetch totalSupply for token ${token.address}: ${error.message}`);
                return token;
              }
            }
            return token;
          })
        );

        // Also fetch modular tokens from blockchain
        try {
          const network = 'amoy';
          const rpcUrl = RPC_URLS[network];
          const provider = new ethers.JsonRpcProvider(rpcUrl);
          const modularTokens = await fetchModularTokens(provider);

          // Combine database tokens with modular tokens
          const allTokens = [...tokensWithUpdatedSupply, ...modularTokens];

          return NextResponse.json(allTokens);
        } catch (error) {
          console.warn('Could not fetch modular tokens:', error);
          // Return just database tokens if modular fetch fails
          return NextResponse.json(tokensWithUpdatedSupply);
        }
      }

      // If 'both', we'll merge with blockchain data below
      if (source === 'both') {
        // For now, just return database tokens
        // TODO: Implement blockchain sync if needed
        return NextResponse.json(dbTokens);
      }
    }

    // Blockchain-only fetch (legacy behavior)
    const network = 'amoy'; // Default to amoy network
    const rpcUrl = RPC_URLS[network];

    // Create provider
    const provider = new ethers.JsonRpcProvider(rpcUrl);

    // Create factory contract instance
    const factory = new ethers.Contract(FACTORY_ADDRESS, SecurityTokenFactoryABI, provider);

    console.log(`Fetching tokens from factory: ${FACTORY_ADDRESS}`);

    // Get token count
    const tokenCount = await factory.getTokenCount();
    console.log(`Factory reports ${tokenCount} deployed tokens`);

    if (tokenCount === 0) {
      return NextResponse.json([]);
    }

    // Get all token addresses
    const tokenAddresses = await factory.getAllDeployedTokens();
    console.log("Retrieved token addresses:", tokenAddresses);

    // Load details for each token
    const tokens = [];

    for (const address of tokenAddresses) {
      try {
        const tokenContract = new ethers.Contract(address, SecurityTokenABI, provider);

        // Get basic token info
        const [name, symbol, totalSupply, decimals, owner] = await Promise.all([
          tokenContract.name(),
          tokenContract.symbol(),
          tokenContract.totalSupply(),
          tokenContract.decimals(),
          tokenContract.owner()
        ]);

        // Get additional token details
        let tokenType = 'UNKNOWN';
        let securityType = 'UNKNOWN';
        let createdAt = new Date().toISOString();

        try {
          // Try to get token metadata if available
          const tokenDetails = await tokenContract.tokenDetails();
          if (tokenDetails) {
            // Parse token details if it's a JSON string
            try {
              const parsed = JSON.parse(tokenDetails);
              tokenType = parsed.tokenType || 'UNKNOWN';
              securityType = parsed.securityType || 'UNKNOWN';
            } catch {
              // If not JSON, use as is
              tokenType = 'EQUITY'; // Default
              securityType = 'REGULATION_D'; // Default
            }
          }
        } catch (error) {
          console.warn(`Could not fetch token details for ${address}:`, error);
        }

        // Try to get creation timestamp from events
        try {
          const filter = factory.filters.TokenDeployed(null, address);
          const events = await factory.queryFilter(filter, 0, 'latest');
          if (events.length > 0) {
            const block = await provider.getBlock(events[0].blockNumber);
            if (block) {
              createdAt = new Date(block.timestamp * 1000).toISOString();
            }
          }
        } catch (error) {
          console.warn(`Could not fetch creation time for ${address}:`, error);
        }

        tokens.push({
          address,
          name,
          symbol,
          decimals: Number(decimals),
          totalSupply: totalSupply.toString(),
          owner,
          securityType,
          tokenType,
          createdAt
        });

      } catch (error) {
        console.warn(`Failed to load token details for ${address}:`, error);
        // Add minimal token info even if details fail
        tokens.push({
          address,
          name: 'Unknown Token',
          symbol: 'UNKNOWN',
          decimals: 0,
          totalSupply: '0',
          owner: ethers.ZeroAddress,
          securityType: 'UNKNOWN',
          tokenType: 'UNKNOWN',
          createdAt: new Date().toISOString()
        });
      }
    }

    console.log(`Successfully loaded ${tokens.length} tokens`);
    return NextResponse.json(tokens);

  } catch (error) {
    console.error('Error fetching tokens:', error);
    return NextResponse.json(
      { error: 'Failed to fetch tokens from factory' },
      { status: 500 }
    );
  }
}

// POST /api/tokens - Create a new token record in database
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Validate required fields
    const requiredFields = [
      'address', 'name', 'symbol'
    ];

    for (const field of requiredFields) {
      if (!body[field]) {
        return NextResponse.json(
          { error: `Missing required field: ${field}` },
          { status: 400 }
        );
      }
    }

    // Check if token already exists
    const existingToken = await prisma.token.findUnique({
      where: { address: body.address }
    });

    if (existingToken) {
      return NextResponse.json(
        { error: 'Token with this address already exists' },
        { status: 409 }
      );
    }

    // Create new token record
    const newToken = await prisma.token.create({
      data: {
        address: body.address,
        transactionHash: body.transactionHash || null,
        blockNumber: body.blockNumber || null,
        network: body.network || 'amoy',
        name: body.name,
        symbol: body.symbol,
        decimals: body.decimals !== undefined ? parseInt(body.decimals) : 18,
        maxSupply: body.maxSupply || '1000000',
        totalSupply: body.totalSupply || '0', // Initialize with 0 or provided value
        tokenType: body.tokenType || 'equity',
        tokenPrice: body.tokenPrice || '10 USD',
        currency: body.currency || 'USD',
        bonusTiers: body.bonusTiers || null,
        tokenImageUrl: body.tokenImageUrl || null,
        whitelistAddress: body.whitelistAddress || null,
        adminAddress: body.adminAddress || null,
        hasKYC: body.hasKYC || false,
        isActive: body.isActive !== undefined ? body.isActive : true,
        selectedClaims: Array.isArray(body.selectedClaims) ? body.selectedClaims.join(',') : body.selectedClaims || null,
        deployedBy: body.deployedBy || null,
        deploymentNotes: body.deploymentNotes || null
      }
    });

    console.log(`Created new token record: ${newToken.symbol} (${newToken.address})`);
    return NextResponse.json(newToken, { status: 201 });

  } catch (error) {
    console.error('Error creating token:', error);

    // Handle Prisma unique constraint errors
    if (error.code === 'P2002') {
      return NextResponse.json(
        { error: 'Token with this address or symbol already exists' },
        { status: 409 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to create token record' },
      { status: 500 }
    );
  }
}
