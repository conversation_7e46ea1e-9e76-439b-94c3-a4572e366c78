/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/tokens/route";
exports.ids = ["app/api/tokens/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftokens%2Froute&page=%2Fapi%2Ftokens%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftokens%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftokens%2Froute&page=%2Fapi%2Ftokens%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftokens%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_github_tokendev_newroo_admin_panel_src_app_api_tokens_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/tokens/route.ts */ \"(rsc)/./src/app/api/tokens/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/tokens/route\",\n        pathname: \"/api/tokens\",\n        filename: \"route\",\n        bundlePath: \"app/api/tokens/route\"\n    },\n    resolvedPagePath: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\api\\\\tokens\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_github_tokendev_newroo_admin_panel_src_app_api_tokens_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftokens%2Froute&page=%2Fapi%2Ftokens%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftokens%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/tokens/route.ts":
/*!*************************************!*\
  !*** ./src/app/api/tokens/route.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/constants/addresses.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/contract/contract.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/utils/units.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/providers/provider-jsonrpc.js\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../lib/prisma */ \"(rsc)/./src/lib/prisma.ts\");\n/* harmony import */ var _contracts_SecurityTokenFactory_json__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../contracts/SecurityTokenFactory.json */ \"(rsc)/./src/contracts/SecurityTokenFactory.json\");\n/* harmony import */ var _contracts_SecurityToken_json__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../contracts/SecurityToken.json */ \"(rsc)/./src/contracts/SecurityToken.json\");\n/* harmony import */ var _contracts_ModularTokenFactory_json__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../contracts/ModularTokenFactory.json */ \"(rsc)/./src/contracts/ModularTokenFactory.json\");\n/* harmony import */ var _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../contracts/SecurityTokenCore.json */ \"(rsc)/./src/contracts/SecurityTokenCore.json\");\n\n\n\n\n\n\n\n// RPC URLs for different networks\nconst RPC_URLS = {\n    amoy: process.env.AMOY_RPC_URL || 'https://rpc-amoy.polygon.technology/',\n    polygon: process.env.POLYGON_RPC_URL || 'https://polygon-rpc.com',\n    unknown: process.env.AMOY_RPC_URL || 'https://rpc-amoy.polygon.technology/'\n};\n// Factory contract addresses\nconst FACTORY_ADDRESS = process.env.FACTORY_ADDRESS || '******************************************';\nconst MODULAR_FACTORY_ADDRESS = process.env.AMOY_MODULAR_TOKEN_FACTORY_ADDRESS || '******************************************';\n// Helper function to determine if an address looks like a real contract address\nfunction isRealContractAddress(address) {\n    // Skip obvious test addresses\n    if (!address || address === ethers__WEBPACK_IMPORTED_MODULE_6__.ZeroAddress) {\n        return false;\n    }\n    // Skip addresses that are clearly test patterns (all zeros with small numbers)\n    const testPatterns = [\n        /^0x0+[0-9a-f]{1,2}$/i,\n        /^******************************************$/i,\n        /^0x0+$/i // All zeros\n    ];\n    return !testPatterns.some((pattern)=>pattern.test(address));\n}\n// Helper function to fetch modular tokens from the ModularTokenFactory\nasync function fetchModularTokens(provider) {\n    try {\n        console.log(`Fetching modular tokens from factory: ${MODULAR_FACTORY_ADDRESS}`);\n        const modularFactory = new ethers__WEBPACK_IMPORTED_MODULE_7__.Contract(MODULAR_FACTORY_ADDRESS, _contracts_ModularTokenFactory_json__WEBPACK_IMPORTED_MODULE_4__, provider);\n        // Get token count\n        const tokenCount = await modularFactory.getDeployedTokensCount();\n        console.log(`Modular factory reports ${tokenCount} deployed tokens`);\n        if (tokenCount === 0) {\n            return [];\n        }\n        // Get all active tokens\n        const activeTokens = await modularFactory.getActiveTokens();\n        console.log(\"Retrieved modular token addresses:\", activeTokens);\n        const tokens = [];\n        for (const address of activeTokens){\n            try {\n                // Get token info from factory\n                const tokenInfo = await modularFactory.getTokenInfo(address);\n                // Get additional details from the token contract\n                const tokenContract = new ethers__WEBPACK_IMPORTED_MODULE_7__.Contract(address, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__, provider);\n                const [totalSupply, version] = await Promise.all([\n                    tokenContract.totalSupply(),\n                    tokenContract.version().catch(()=>\"1.0.0\")\n                ]);\n                // Get metadata if available\n                let metadata = null;\n                try {\n                    metadata = await tokenContract.getMetadata();\n                } catch (e) {\n                // Metadata might not be available\n                }\n                tokens.push({\n                    id: `modular-${address}`,\n                    address,\n                    name: tokenInfo.name,\n                    symbol: tokenInfo.symbol,\n                    decimals: Number(tokenInfo.decimals),\n                    maxSupply: ethers__WEBPACK_IMPORTED_MODULE_8__.formatUnits(tokenInfo.maxSupply, tokenInfo.decimals),\n                    totalSupply: ethers__WEBPACK_IMPORTED_MODULE_8__.formatUnits(totalSupply, tokenInfo.decimals),\n                    tokenType: 'Modular Security Token',\n                    tokenPrice: metadata?.tokenPrice || 'N/A',\n                    currency: 'USD',\n                    network: 'amoy',\n                    hasKYC: true,\n                    isActive: tokenInfo.isActive,\n                    adminAddress: tokenInfo.admin,\n                    whitelistAddress: null,\n                    transactionHash: null,\n                    blockNumber: null,\n                    createdAt: new Date(Number(tokenInfo.deploymentTime) * 1000).toISOString(),\n                    updatedAt: new Date().toISOString(),\n                    version: version,\n                    isModular: true\n                });\n            } catch (error) {\n                console.warn(`Failed to load modular token details for ${address}:`, error);\n            }\n        }\n        console.log(`Successfully loaded ${tokens.length} modular tokens`);\n        return tokens;\n    } catch (error) {\n        console.error('Error fetching modular tokens:', error);\n        return [];\n    }\n}\n// GET /api/tokens - Get all tokens from database and optionally sync with blockchain\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const source = searchParams.get('source') || 'database'; // 'database', 'blockchain', or 'both'\n        if (source === 'database' || source === 'both') {\n            // Get tokens from database\n            const dbTokens = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.token.findMany({\n                orderBy: {\n                    createdAt: 'desc'\n                },\n                include: {\n                    transactions: {\n                        take: 5,\n                        orderBy: {\n                            createdAt: 'desc'\n                        }\n                    }\n                }\n            });\n            if (source === 'database') {\n                // Sync totalSupply from blockchain for database tokens if needed\n                const tokensWithUpdatedSupply = await Promise.all(dbTokens.map(async (token)=>{\n                    // Only attempt blockchain sync for tokens that:\n                    // 1. Have totalSupply of 0 or not set\n                    // 2. Have a real-looking address (not test addresses)\n                    // 3. Are on a supported network\n                    const shouldSyncFromBlockchain = (token.totalSupply === '0' || !token.totalSupply) && isRealContractAddress(token.address) && (token.network === 'amoy' || token.network === 'polygon');\n                    if (shouldSyncFromBlockchain) {\n                        try {\n                            const network = token.network || 'amoy';\n                            const rpcUrl = RPC_URLS[network] || RPC_URLS.amoy;\n                            const provider = new ethers__WEBPACK_IMPORTED_MODULE_9__.JsonRpcProvider(rpcUrl);\n                            // First check if there's code at this address\n                            const code = await provider.getCode(token.address);\n                            if (code === '0x') {\n                                console.log(`Skipping blockchain sync for ${token.address} - no contract deployed`);\n                                return token;\n                            }\n                            const tokenContract = new ethers__WEBPACK_IMPORTED_MODULE_7__.Contract(token.address, _contracts_SecurityToken_json__WEBPACK_IMPORTED_MODULE_3__.abi, provider);\n                            const totalSupplyRaw = await tokenContract.totalSupply();\n                            const decimals = token.decimals || 18;\n                            const totalSupply = decimals === 0 ? totalSupplyRaw.toString() : ethers__WEBPACK_IMPORTED_MODULE_8__.formatUnits(totalSupplyRaw, decimals);\n                            console.log(`Successfully synced totalSupply for ${token.symbol}: ${totalSupply}`);\n                            // Update the database with the fetched totalSupply\n                            await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.token.update({\n                                where: {\n                                    id: token.id\n                                },\n                                data: {\n                                    totalSupply\n                                }\n                            });\n                            return {\n                                ...token,\n                                totalSupply\n                            };\n                        } catch (error) {\n                            console.warn(`Could not fetch totalSupply for token ${token.address}: ${error.message}`);\n                            return token;\n                        }\n                    }\n                    return token;\n                }));\n                // Also fetch modular tokens from blockchain\n                try {\n                    const network = 'amoy';\n                    const rpcUrl = RPC_URLS[network];\n                    const provider = new ethers__WEBPACK_IMPORTED_MODULE_9__.JsonRpcProvider(rpcUrl);\n                    const modularTokens = await fetchModularTokens(provider);\n                    // Combine database tokens with modular tokens\n                    const allTokens = [\n                        ...tokensWithUpdatedSupply,\n                        ...modularTokens\n                    ];\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(allTokens);\n                } catch (error) {\n                    console.warn('Could not fetch modular tokens:', error);\n                    // Return just database tokens if modular fetch fails\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(tokensWithUpdatedSupply);\n                }\n            }\n            // If 'both', we'll merge with blockchain data below\n            if (source === 'both') {\n                // For now, just return database tokens\n                // TODO: Implement blockchain sync if needed\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(dbTokens);\n            }\n        }\n        // Blockchain-only fetch (legacy behavior)\n        const network = 'amoy'; // Default to amoy network\n        const rpcUrl = RPC_URLS[network];\n        // Create provider\n        const provider = new ethers__WEBPACK_IMPORTED_MODULE_9__.JsonRpcProvider(rpcUrl);\n        // Create factory contract instance\n        const factory = new ethers__WEBPACK_IMPORTED_MODULE_7__.Contract(FACTORY_ADDRESS, _contracts_SecurityTokenFactory_json__WEBPACK_IMPORTED_MODULE_2__.abi, provider);\n        console.log(`Fetching tokens from factory: ${FACTORY_ADDRESS}`);\n        // Get token count\n        const tokenCount = await factory.getTokenCount();\n        console.log(`Factory reports ${tokenCount} deployed tokens`);\n        if (tokenCount === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json([]);\n        }\n        // Get all token addresses\n        const tokenAddresses = await factory.getAllDeployedTokens();\n        console.log(\"Retrieved token addresses:\", tokenAddresses);\n        // Load details for each token\n        const tokens = [];\n        for (const address of tokenAddresses){\n            try {\n                const tokenContract = new ethers__WEBPACK_IMPORTED_MODULE_7__.Contract(address, _contracts_SecurityToken_json__WEBPACK_IMPORTED_MODULE_3__.abi, provider);\n                // Get basic token info\n                const [name, symbol, totalSupply, decimals, owner] = await Promise.all([\n                    tokenContract.name(),\n                    tokenContract.symbol(),\n                    tokenContract.totalSupply(),\n                    tokenContract.decimals(),\n                    tokenContract.owner()\n                ]);\n                // Get additional token details\n                let tokenType = 'UNKNOWN';\n                let securityType = 'UNKNOWN';\n                let createdAt = new Date().toISOString();\n                try {\n                    // Try to get token metadata if available\n                    const tokenDetails = await tokenContract.tokenDetails();\n                    if (tokenDetails) {\n                        // Parse token details if it's a JSON string\n                        try {\n                            const parsed = JSON.parse(tokenDetails);\n                            tokenType = parsed.tokenType || 'UNKNOWN';\n                            securityType = parsed.securityType || 'UNKNOWN';\n                        } catch  {\n                            // If not JSON, use as is\n                            tokenType = 'EQUITY'; // Default\n                            securityType = 'REGULATION_D'; // Default\n                        }\n                    }\n                } catch (error) {\n                    console.warn(`Could not fetch token details for ${address}:`, error);\n                }\n                // Try to get creation timestamp from events\n                try {\n                    const filter = factory.filters.TokenDeployed(null, address);\n                    const events = await factory.queryFilter(filter, 0, 'latest');\n                    if (events.length > 0) {\n                        const block = await provider.getBlock(events[0].blockNumber);\n                        if (block) {\n                            createdAt = new Date(block.timestamp * 1000).toISOString();\n                        }\n                    }\n                } catch (error) {\n                    console.warn(`Could not fetch creation time for ${address}:`, error);\n                }\n                tokens.push({\n                    address,\n                    name,\n                    symbol,\n                    decimals: Number(decimals),\n                    totalSupply: totalSupply.toString(),\n                    owner,\n                    securityType,\n                    tokenType,\n                    createdAt\n                });\n            } catch (error) {\n                console.warn(`Failed to load token details for ${address}:`, error);\n                // Add minimal token info even if details fail\n                tokens.push({\n                    address,\n                    name: 'Unknown Token',\n                    symbol: 'UNKNOWN',\n                    decimals: 0,\n                    totalSupply: '0',\n                    owner: ethers__WEBPACK_IMPORTED_MODULE_6__.ZeroAddress,\n                    securityType: 'UNKNOWN',\n                    tokenType: 'UNKNOWN',\n                    createdAt: new Date().toISOString()\n                });\n            }\n        }\n        console.log(`Successfully loaded ${tokens.length} tokens`);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(tokens);\n    } catch (error) {\n        console.error('Error fetching tokens:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to fetch tokens from factory'\n        }, {\n            status: 500\n        });\n    }\n}\n// POST /api/tokens - Create a new token record in database\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        // Validate required fields\n        const requiredFields = [\n            'address',\n            'name',\n            'symbol'\n        ];\n        for (const field of requiredFields){\n            if (!body[field]) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: `Missing required field: ${field}`\n                }, {\n                    status: 400\n                });\n            }\n        }\n        // Check if token already exists\n        const existingToken = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.token.findUnique({\n            where: {\n                address: body.address\n            }\n        });\n        if (existingToken) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Token with this address already exists'\n            }, {\n                status: 409\n            });\n        }\n        // Create new token record\n        const newToken = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.token.create({\n            data: {\n                address: body.address,\n                transactionHash: body.transactionHash || null,\n                blockNumber: body.blockNumber || null,\n                network: body.network || 'amoy',\n                name: body.name,\n                symbol: body.symbol,\n                decimals: body.decimals !== undefined ? parseInt(body.decimals) : 18,\n                maxSupply: body.maxSupply || '1000000',\n                totalSupply: body.totalSupply || '0',\n                tokenType: body.tokenType || 'equity',\n                tokenPrice: body.tokenPrice || '10 USD',\n                currency: body.currency || 'USD',\n                bonusTiers: body.bonusTiers || null,\n                tokenImageUrl: body.tokenImageUrl || null,\n                whitelistAddress: body.whitelistAddress || null,\n                adminAddress: body.adminAddress || null,\n                hasKYC: body.hasKYC || false,\n                isActive: body.isActive !== undefined ? body.isActive : true,\n                selectedClaims: Array.isArray(body.selectedClaims) ? body.selectedClaims.join(',') : body.selectedClaims || null,\n                deployedBy: body.deployedBy || null,\n                deploymentNotes: body.deploymentNotes || null\n            }\n        });\n        console.log(`Created new token record: ${newToken.symbol} (${newToken.address})`);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(newToken, {\n            status: 201\n        });\n    } catch (error) {\n        console.error('Error creating token:', error);\n        // Handle Prisma unique constraint errors\n        if (error.code === 'P2002') {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Token with this address or symbol already exists'\n            }, {\n                status: 409\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to create token record'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS90b2tlbnMvcm91dGUudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7O0FBQXdEO0FBQ3hCO0FBQ2E7QUFDc0M7QUFDZDtBQUNZO0FBQ0o7QUFFN0Usa0NBQWtDO0FBQ2xDLE1BQU1PLFdBQVc7SUFDZkMsTUFBTUMsUUFBUUMsR0FBRyxDQUFDQyxZQUFZLElBQUk7SUFDbENDLFNBQVNILFFBQVFDLEdBQUcsQ0FBQ0csZUFBZSxJQUFJO0lBQ3hDQyxTQUFTTCxRQUFRQyxHQUFHLENBQUNDLFlBQVksSUFBSTtBQUN2QztBQUVBLDZCQUE2QjtBQUM3QixNQUFNSSxrQkFBa0JOLFFBQVFDLEdBQUcsQ0FBQ0ssZUFBZSxJQUFJO0FBQ3ZELE1BQU1DLDBCQUEwQlAsUUFBUUMsR0FBRyxDQUFDTyxrQ0FBa0MsSUFBSTtBQUVsRixnRkFBZ0Y7QUFDaEYsU0FBU0Msc0JBQXNCQyxPQUFlO0lBQzVDLDhCQUE4QjtJQUM5QixJQUFJLENBQUNBLFdBQVdBLFlBQVlsQiwrQ0FBa0IsRUFBRTtRQUM5QyxPQUFPO0lBQ1Q7SUFFQSwrRUFBK0U7SUFDL0UsTUFBTW9CLGVBQWU7UUFDbkI7UUFDQTtRQUNBLFVBQVUsWUFBWTtLQUN2QjtJQUVELE9BQU8sQ0FBQ0EsYUFBYUMsSUFBSSxDQUFDQyxDQUFBQSxVQUFXQSxRQUFRQyxJQUFJLENBQUNMO0FBQ3BEO0FBRUEsdUVBQXVFO0FBQ3ZFLGVBQWVNLG1CQUFtQkMsUUFBZ0M7SUFDaEUsSUFBSTtRQUNGQyxRQUFRQyxHQUFHLENBQUMsQ0FBQyxzQ0FBc0MsRUFBRVoseUJBQXlCO1FBRTlFLE1BQU1hLGlCQUFpQixJQUFJNUIsNENBQWUsQ0FBQ2UseUJBQXlCWCxnRUFBc0JBLEVBQUVxQjtRQUU1RixrQkFBa0I7UUFDbEIsTUFBTUssYUFBYSxNQUFNRixlQUFlRyxzQkFBc0I7UUFDOURMLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLHdCQUF3QixFQUFFRyxXQUFXLGdCQUFnQixDQUFDO1FBRW5FLElBQUlBLGVBQWUsR0FBRztZQUNwQixPQUFPLEVBQUU7UUFDWDtRQUVBLHdCQUF3QjtRQUN4QixNQUFNRSxlQUFlLE1BQU1KLGVBQWVLLGVBQWU7UUFDekRQLFFBQVFDLEdBQUcsQ0FBQyxzQ0FBc0NLO1FBRWxELE1BQU1FLFNBQVMsRUFBRTtRQUVqQixLQUFLLE1BQU1oQixXQUFXYyxhQUFjO1lBQ2xDLElBQUk7Z0JBQ0YsOEJBQThCO2dCQUM5QixNQUFNRyxZQUFZLE1BQU1QLGVBQWVRLFlBQVksQ0FBQ2xCO2dCQUVwRCxpREFBaUQ7Z0JBQ2pELE1BQU1tQixnQkFBZ0IsSUFBSXJDLDRDQUFlLENBQUNrQixTQUFTYiw4REFBb0JBLEVBQUVvQjtnQkFFekUsTUFBTSxDQUFDYSxhQUFhQyxRQUFRLEdBQUcsTUFBTUMsUUFBUUMsR0FBRyxDQUFDO29CQUMvQ0osY0FBY0MsV0FBVztvQkFDekJELGNBQWNFLE9BQU8sR0FBR0csS0FBSyxDQUFDLElBQU07aUJBQ3JDO2dCQUVELDRCQUE0QjtnQkFDNUIsSUFBSUMsV0FBVztnQkFDZixJQUFJO29CQUNGQSxXQUFXLE1BQU1OLGNBQWNPLFdBQVc7Z0JBQzVDLEVBQUUsT0FBT0MsR0FBRztnQkFDVixrQ0FBa0M7Z0JBQ3BDO2dCQUVBWCxPQUFPWSxJQUFJLENBQUM7b0JBQ1ZDLElBQUksQ0FBQyxRQUFRLEVBQUU3QixTQUFTO29CQUN4QkE7b0JBQ0E4QixNQUFNYixVQUFVYSxJQUFJO29CQUNwQkMsUUFBUWQsVUFBVWMsTUFBTTtvQkFDeEJDLFVBQVVDLE9BQU9oQixVQUFVZSxRQUFRO29CQUNuQ0UsV0FBV3BELCtDQUFrQixDQUFDbUMsVUFBVWlCLFNBQVMsRUFBRWpCLFVBQVVlLFFBQVE7b0JBQ3JFWixhQUFhdEMsK0NBQWtCLENBQUNzQyxhQUFhSCxVQUFVZSxRQUFRO29CQUMvREksV0FBVztvQkFDWEMsWUFBWVosVUFBVVksY0FBYztvQkFDcENDLFVBQVU7b0JBQ1ZDLFNBQVM7b0JBQ1RDLFFBQVE7b0JBQ1JDLFVBQVV4QixVQUFVd0IsUUFBUTtvQkFDNUJDLGNBQWN6QixVQUFVMEIsS0FBSztvQkFDN0JDLGtCQUFrQjtvQkFDbEJDLGlCQUFpQjtvQkFDakJDLGFBQWE7b0JBQ2JDLFdBQVcsSUFBSUMsS0FBS2YsT0FBT2hCLFVBQVVnQyxjQUFjLElBQUksTUFBTUMsV0FBVztvQkFDeEVDLFdBQVcsSUFBSUgsT0FBT0UsV0FBVztvQkFDakM3QixTQUFTQTtvQkFDVCtCLFdBQVc7Z0JBQ2I7WUFFRixFQUFFLE9BQU9DLE9BQU87Z0JBQ2Q3QyxRQUFROEMsSUFBSSxDQUFDLENBQUMseUNBQXlDLEVBQUV0RCxRQUFRLENBQUMsQ0FBQyxFQUFFcUQ7WUFDdkU7UUFDRjtRQUVBN0MsUUFBUUMsR0FBRyxDQUFDLENBQUMsb0JBQW9CLEVBQUVPLE9BQU91QyxNQUFNLENBQUMsZUFBZSxDQUFDO1FBQ2pFLE9BQU92QztJQUVULEVBQUUsT0FBT3FDLE9BQU87UUFDZDdDLFFBQVE2QyxLQUFLLENBQUMsa0NBQWtDQTtRQUNoRCxPQUFPLEVBQUU7SUFDWDtBQUNGO0FBRUEscUZBQXFGO0FBQzlFLGVBQWVHLElBQUlDLE9BQW9CO0lBQzVDLElBQUk7UUFDRixNQUFNLEVBQUVDLFlBQVksRUFBRSxHQUFHLElBQUlDLElBQUlGLFFBQVFHLEdBQUc7UUFDNUMsTUFBTUMsU0FBU0gsYUFBYUksR0FBRyxDQUFDLGFBQWEsWUFBWSxzQ0FBc0M7UUFFL0YsSUFBSUQsV0FBVyxjQUFjQSxXQUFXLFFBQVE7WUFDOUMsMkJBQTJCO1lBQzNCLE1BQU1FLFdBQVcsTUFBTWhGLCtDQUFNQSxDQUFDaUYsS0FBSyxDQUFDQyxRQUFRLENBQUM7Z0JBQzNDQyxTQUFTO29CQUFFbkIsV0FBVztnQkFBTztnQkFDN0JvQixTQUFTO29CQUNQQyxjQUFjO3dCQUNaQyxNQUFNO3dCQUNOSCxTQUFTOzRCQUFFbkIsV0FBVzt3QkFBTztvQkFDL0I7Z0JBQ0Y7WUFDRjtZQUVBLElBQUljLFdBQVcsWUFBWTtnQkFDekIsaUVBQWlFO2dCQUNqRSxNQUFNUywwQkFBMEIsTUFBTWhELFFBQVFDLEdBQUcsQ0FDL0N3QyxTQUFTUSxHQUFHLENBQUMsT0FBT1A7b0JBQ2xCLGdEQUFnRDtvQkFDaEQsc0NBQXNDO29CQUN0QyxzREFBc0Q7b0JBQ3RELGdDQUFnQztvQkFDaEMsTUFBTVEsMkJBQ0osQ0FBQ1IsTUFBTTVDLFdBQVcsS0FBSyxPQUFPLENBQUM0QyxNQUFNNUMsV0FBVyxLQUNoRHJCLHNCQUFzQmlFLE1BQU1oRSxPQUFPLEtBQ2xDZ0UsQ0FBQUEsTUFBTXpCLE9BQU8sS0FBSyxVQUFVeUIsTUFBTXpCLE9BQU8sS0FBSyxTQUFRO29CQUd6RCxJQUFJaUMsMEJBQTBCO3dCQUM1QixJQUFJOzRCQUNGLE1BQU1qQyxVQUFVeUIsTUFBTXpCLE9BQU8sSUFBSTs0QkFDakMsTUFBTWtDLFNBQVNyRixRQUFRLENBQUNtRCxRQUFRLElBQUluRCxTQUFTQyxJQUFJOzRCQUNqRCxNQUFNa0IsV0FBVyxJQUFJekIsbURBQXNCLENBQUMyRjs0QkFFNUMsOENBQThDOzRCQUM5QyxNQUFNRSxPQUFPLE1BQU1wRSxTQUFTcUUsT0FBTyxDQUFDWixNQUFNaEUsT0FBTzs0QkFDakQsSUFBSTJFLFNBQVMsTUFBTTtnQ0FDakJuRSxRQUFRQyxHQUFHLENBQUMsQ0FBQyw2QkFBNkIsRUFBRXVELE1BQU1oRSxPQUFPLENBQUMsdUJBQXVCLENBQUM7Z0NBQ2xGLE9BQU9nRTs0QkFDVDs0QkFFQSxNQUFNN0MsZ0JBQWdCLElBQUlyQyw0Q0FBZSxDQUFDa0YsTUFBTWhFLE9BQU8sRUFBRWYsOERBQW9CLEVBQUVzQjs0QkFFL0UsTUFBTXVFLGlCQUFpQixNQUFNM0QsY0FBY0MsV0FBVzs0QkFDdEQsTUFBTVksV0FBV2dDLE1BQU1oQyxRQUFRLElBQUk7NEJBQ25DLE1BQU1aLGNBQWNZLGFBQWEsSUFDN0I4QyxlQUFlQyxRQUFRLEtBQ3ZCakcsK0NBQWtCLENBQUNnRyxnQkFBZ0I5Qzs0QkFFdkN4QixRQUFRQyxHQUFHLENBQUMsQ0FBQyxvQ0FBb0MsRUFBRXVELE1BQU1qQyxNQUFNLENBQUMsRUFBRSxFQUFFWCxhQUFhOzRCQUVqRixtREFBbUQ7NEJBQ25ELE1BQU1yQywrQ0FBTUEsQ0FBQ2lGLEtBQUssQ0FBQ2dCLE1BQU0sQ0FBQztnQ0FDeEJDLE9BQU87b0NBQUVwRCxJQUFJbUMsTUFBTW5DLEVBQUU7Z0NBQUM7Z0NBQ3RCcUQsTUFBTTtvQ0FBRTlEO2dDQUFZOzRCQUN0Qjs0QkFFQSxPQUFPO2dDQUFFLEdBQUc0QyxLQUFLO2dDQUFFNUM7NEJBQVk7d0JBQ2pDLEVBQUUsT0FBT2lDLE9BQU87NEJBQ2Q3QyxRQUFROEMsSUFBSSxDQUFDLENBQUMsc0NBQXNDLEVBQUVVLE1BQU1oRSxPQUFPLENBQUMsRUFBRSxFQUFFcUQsTUFBTThCLE9BQU8sRUFBRTs0QkFDdkYsT0FBT25CO3dCQUNUO29CQUNGO29CQUNBLE9BQU9BO2dCQUNUO2dCQUdGLDRDQUE0QztnQkFDNUMsSUFBSTtvQkFDRixNQUFNekIsVUFBVTtvQkFDaEIsTUFBTWtDLFNBQVNyRixRQUFRLENBQUNtRCxRQUFRO29CQUNoQyxNQUFNaEMsV0FBVyxJQUFJekIsbURBQXNCLENBQUMyRjtvQkFDNUMsTUFBTVcsZ0JBQWdCLE1BQU05RSxtQkFBbUJDO29CQUUvQyw4Q0FBOEM7b0JBQzlDLE1BQU04RSxZQUFZOzJCQUFJZjsyQkFBNEJjO3FCQUFjO29CQUVoRSxPQUFPdkcscURBQVlBLENBQUN5RyxJQUFJLENBQUNEO2dCQUMzQixFQUFFLE9BQU9oQyxPQUFPO29CQUNkN0MsUUFBUThDLElBQUksQ0FBQyxtQ0FBbUNEO29CQUNoRCxxREFBcUQ7b0JBQ3JELE9BQU94RSxxREFBWUEsQ0FBQ3lHLElBQUksQ0FBQ2hCO2dCQUMzQjtZQUNGO1lBRUEsb0RBQW9EO1lBQ3BELElBQUlULFdBQVcsUUFBUTtnQkFDckIsdUNBQXVDO2dCQUN2Qyw0Q0FBNEM7Z0JBQzVDLE9BQU9oRixxREFBWUEsQ0FBQ3lHLElBQUksQ0FBQ3ZCO1lBQzNCO1FBQ0Y7UUFFQSwwQ0FBMEM7UUFDMUMsTUFBTXhCLFVBQVUsUUFBUSwwQkFBMEI7UUFDbEQsTUFBTWtDLFNBQVNyRixRQUFRLENBQUNtRCxRQUFRO1FBRWhDLGtCQUFrQjtRQUNsQixNQUFNaEMsV0FBVyxJQUFJekIsbURBQXNCLENBQUMyRjtRQUU1QyxtQ0FBbUM7UUFDbkMsTUFBTWMsVUFBVSxJQUFJekcsNENBQWUsQ0FBQ2MsaUJBQWlCWixxRUFBMkIsRUFBRXVCO1FBRWxGQyxRQUFRQyxHQUFHLENBQUMsQ0FBQyw4QkFBOEIsRUFBRWIsaUJBQWlCO1FBRTlELGtCQUFrQjtRQUNsQixNQUFNZ0IsYUFBYSxNQUFNMkUsUUFBUUMsYUFBYTtRQUM5Q2hGLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLGdCQUFnQixFQUFFRyxXQUFXLGdCQUFnQixDQUFDO1FBRTNELElBQUlBLGVBQWUsR0FBRztZQUNwQixPQUFPL0IscURBQVlBLENBQUN5RyxJQUFJLENBQUMsRUFBRTtRQUM3QjtRQUVBLDBCQUEwQjtRQUMxQixNQUFNRyxpQkFBaUIsTUFBTUYsUUFBUUcsb0JBQW9CO1FBQ3pEbEYsUUFBUUMsR0FBRyxDQUFDLDhCQUE4QmdGO1FBRTFDLDhCQUE4QjtRQUM5QixNQUFNekUsU0FBUyxFQUFFO1FBRWpCLEtBQUssTUFBTWhCLFdBQVd5RixlQUFnQjtZQUNwQyxJQUFJO2dCQUNGLE1BQU10RSxnQkFBZ0IsSUFBSXJDLDRDQUFlLENBQUNrQixTQUFTZiw4REFBb0IsRUFBRXNCO2dCQUV6RSx1QkFBdUI7Z0JBQ3ZCLE1BQU0sQ0FBQ3VCLE1BQU1DLFFBQVFYLGFBQWFZLFVBQVUyRCxNQUFNLEdBQUcsTUFBTXJFLFFBQVFDLEdBQUcsQ0FBQztvQkFDckVKLGNBQWNXLElBQUk7b0JBQ2xCWCxjQUFjWSxNQUFNO29CQUNwQlosY0FBY0MsV0FBVztvQkFDekJELGNBQWNhLFFBQVE7b0JBQ3RCYixjQUFjd0UsS0FBSztpQkFDcEI7Z0JBRUQsK0JBQStCO2dCQUMvQixJQUFJdkQsWUFBWTtnQkFDaEIsSUFBSXdELGVBQWU7Z0JBQ25CLElBQUk3QyxZQUFZLElBQUlDLE9BQU9FLFdBQVc7Z0JBRXRDLElBQUk7b0JBQ0YseUNBQXlDO29CQUN6QyxNQUFNMkMsZUFBZSxNQUFNMUUsY0FBYzBFLFlBQVk7b0JBQ3JELElBQUlBLGNBQWM7d0JBQ2hCLDRDQUE0Qzt3QkFDNUMsSUFBSTs0QkFDRixNQUFNQyxTQUFTQyxLQUFLQyxLQUFLLENBQUNIOzRCQUMxQnpELFlBQVkwRCxPQUFPMUQsU0FBUyxJQUFJOzRCQUNoQ3dELGVBQWVFLE9BQU9GLFlBQVksSUFBSTt3QkFDeEMsRUFBRSxPQUFNOzRCQUNOLHlCQUF5Qjs0QkFDekJ4RCxZQUFZLFVBQVUsVUFBVTs0QkFDaEN3RCxlQUFlLGdCQUFnQixVQUFVO3dCQUMzQztvQkFDRjtnQkFDRixFQUFFLE9BQU92QyxPQUFPO29CQUNkN0MsUUFBUThDLElBQUksQ0FBQyxDQUFDLGtDQUFrQyxFQUFFdEQsUUFBUSxDQUFDLENBQUMsRUFBRXFEO2dCQUNoRTtnQkFFQSw0Q0FBNEM7Z0JBQzVDLElBQUk7b0JBQ0YsTUFBTTRDLFNBQVNWLFFBQVFXLE9BQU8sQ0FBQ0MsYUFBYSxDQUFDLE1BQU1uRztvQkFDbkQsTUFBTW9HLFNBQVMsTUFBTWIsUUFBUWMsV0FBVyxDQUFDSixRQUFRLEdBQUc7b0JBQ3BELElBQUlHLE9BQU83QyxNQUFNLEdBQUcsR0FBRzt3QkFDckIsTUFBTStDLFFBQVEsTUFBTS9GLFNBQVNnRyxRQUFRLENBQUNILE1BQU0sQ0FBQyxFQUFFLENBQUN0RCxXQUFXO3dCQUMzRCxJQUFJd0QsT0FBTzs0QkFDVHZELFlBQVksSUFBSUMsS0FBS3NELE1BQU1FLFNBQVMsR0FBRyxNQUFNdEQsV0FBVzt3QkFDMUQ7b0JBQ0Y7Z0JBQ0YsRUFBRSxPQUFPRyxPQUFPO29CQUNkN0MsUUFBUThDLElBQUksQ0FBQyxDQUFDLGtDQUFrQyxFQUFFdEQsUUFBUSxDQUFDLENBQUMsRUFBRXFEO2dCQUNoRTtnQkFFQXJDLE9BQU9ZLElBQUksQ0FBQztvQkFDVjVCO29CQUNBOEI7b0JBQ0FDO29CQUNBQyxVQUFVQyxPQUFPRDtvQkFDakJaLGFBQWFBLFlBQVkyRCxRQUFRO29CQUNqQ1k7b0JBQ0FDO29CQUNBeEQ7b0JBQ0FXO2dCQUNGO1lBRUYsRUFBRSxPQUFPTSxPQUFPO2dCQUNkN0MsUUFBUThDLElBQUksQ0FBQyxDQUFDLGlDQUFpQyxFQUFFdEQsUUFBUSxDQUFDLENBQUMsRUFBRXFEO2dCQUM3RCw4Q0FBOEM7Z0JBQzlDckMsT0FBT1ksSUFBSSxDQUFDO29CQUNWNUI7b0JBQ0E4QixNQUFNO29CQUNOQyxRQUFRO29CQUNSQyxVQUFVO29CQUNWWixhQUFhO29CQUNidUUsT0FBTzdHLCtDQUFrQjtvQkFDekI4RyxjQUFjO29CQUNkeEQsV0FBVztvQkFDWFcsV0FBVyxJQUFJQyxPQUFPRSxXQUFXO2dCQUNuQztZQUNGO1FBQ0Y7UUFFQTFDLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLG9CQUFvQixFQUFFTyxPQUFPdUMsTUFBTSxDQUFDLE9BQU8sQ0FBQztRQUN6RCxPQUFPMUUscURBQVlBLENBQUN5RyxJQUFJLENBQUN0RTtJQUUzQixFQUFFLE9BQU9xQyxPQUFPO1FBQ2Q3QyxRQUFRNkMsS0FBSyxDQUFDLDBCQUEwQkE7UUFDeEMsT0FBT3hFLHFEQUFZQSxDQUFDeUcsSUFBSSxDQUN0QjtZQUFFakMsT0FBTztRQUFzQyxHQUMvQztZQUFFb0QsUUFBUTtRQUFJO0lBRWxCO0FBQ0Y7QUFFQSwyREFBMkQ7QUFDcEQsZUFBZUMsS0FBS2pELE9BQW9CO0lBQzdDLElBQUk7UUFDRixNQUFNa0QsT0FBTyxNQUFNbEQsUUFBUTZCLElBQUk7UUFFL0IsMkJBQTJCO1FBQzNCLE1BQU1zQixpQkFBaUI7WUFDckI7WUFBVztZQUFRO1NBQ3BCO1FBRUQsS0FBSyxNQUFNQyxTQUFTRCxlQUFnQjtZQUNsQyxJQUFJLENBQUNELElBQUksQ0FBQ0UsTUFBTSxFQUFFO2dCQUNoQixPQUFPaEkscURBQVlBLENBQUN5RyxJQUFJLENBQ3RCO29CQUFFakMsT0FBTyxDQUFDLHdCQUF3QixFQUFFd0QsT0FBTztnQkFBQyxHQUM1QztvQkFBRUosUUFBUTtnQkFBSTtZQUVsQjtRQUNGO1FBRUEsZ0NBQWdDO1FBQ2hDLE1BQU1LLGdCQUFnQixNQUFNL0gsK0NBQU1BLENBQUNpRixLQUFLLENBQUMrQyxVQUFVLENBQUM7WUFDbEQ5QixPQUFPO2dCQUFFakYsU0FBUzJHLEtBQUszRyxPQUFPO1lBQUM7UUFDakM7UUFFQSxJQUFJOEcsZUFBZTtZQUNqQixPQUFPakkscURBQVlBLENBQUN5RyxJQUFJLENBQ3RCO2dCQUFFakMsT0FBTztZQUF5QyxHQUNsRDtnQkFBRW9ELFFBQVE7WUFBSTtRQUVsQjtRQUVBLDBCQUEwQjtRQUMxQixNQUFNTyxXQUFXLE1BQU1qSSwrQ0FBTUEsQ0FBQ2lGLEtBQUssQ0FBQ2lELE1BQU0sQ0FBQztZQUN6Qy9CLE1BQU07Z0JBQ0psRixTQUFTMkcsS0FBSzNHLE9BQU87Z0JBQ3JCNkMsaUJBQWlCOEQsS0FBSzlELGVBQWUsSUFBSTtnQkFDekNDLGFBQWE2RCxLQUFLN0QsV0FBVyxJQUFJO2dCQUNqQ1AsU0FBU29FLEtBQUtwRSxPQUFPLElBQUk7Z0JBQ3pCVCxNQUFNNkUsS0FBSzdFLElBQUk7Z0JBQ2ZDLFFBQVE0RSxLQUFLNUUsTUFBTTtnQkFDbkJDLFVBQVUyRSxLQUFLM0UsUUFBUSxLQUFLa0YsWUFBWUMsU0FBU1IsS0FBSzNFLFFBQVEsSUFBSTtnQkFDbEVFLFdBQVd5RSxLQUFLekUsU0FBUyxJQUFJO2dCQUM3QmQsYUFBYXVGLEtBQUt2RixXQUFXLElBQUk7Z0JBQ2pDZ0IsV0FBV3VFLEtBQUt2RSxTQUFTLElBQUk7Z0JBQzdCQyxZQUFZc0UsS0FBS3RFLFVBQVUsSUFBSTtnQkFDL0JDLFVBQVVxRSxLQUFLckUsUUFBUSxJQUFJO2dCQUMzQjhFLFlBQVlULEtBQUtTLFVBQVUsSUFBSTtnQkFDL0JDLGVBQWVWLEtBQUtVLGFBQWEsSUFBSTtnQkFDckN6RSxrQkFBa0IrRCxLQUFLL0QsZ0JBQWdCLElBQUk7Z0JBQzNDRixjQUFjaUUsS0FBS2pFLFlBQVksSUFBSTtnQkFDbkNGLFFBQVFtRSxLQUFLbkUsTUFBTSxJQUFJO2dCQUN2QkMsVUFBVWtFLEtBQUtsRSxRQUFRLEtBQUt5RSxZQUFZUCxLQUFLbEUsUUFBUSxHQUFHO2dCQUN4RDZFLGdCQUFnQkMsTUFBTUMsT0FBTyxDQUFDYixLQUFLVyxjQUFjLElBQUlYLEtBQUtXLGNBQWMsQ0FBQ0csSUFBSSxDQUFDLE9BQU9kLEtBQUtXLGNBQWMsSUFBSTtnQkFDNUdJLFlBQVlmLEtBQUtlLFVBQVUsSUFBSTtnQkFDL0JDLGlCQUFpQmhCLEtBQUtnQixlQUFlLElBQUk7WUFDM0M7UUFDRjtRQUVBbkgsUUFBUUMsR0FBRyxDQUFDLENBQUMsMEJBQTBCLEVBQUV1RyxTQUFTakYsTUFBTSxDQUFDLEVBQUUsRUFBRWlGLFNBQVNoSCxPQUFPLENBQUMsQ0FBQyxDQUFDO1FBQ2hGLE9BQU9uQixxREFBWUEsQ0FBQ3lHLElBQUksQ0FBQzBCLFVBQVU7WUFBRVAsUUFBUTtRQUFJO0lBRW5ELEVBQUUsT0FBT3BELE9BQU87UUFDZDdDLFFBQVE2QyxLQUFLLENBQUMseUJBQXlCQTtRQUV2Qyx5Q0FBeUM7UUFDekMsSUFBSUEsTUFBTXNCLElBQUksS0FBSyxTQUFTO1lBQzFCLE9BQU85RixxREFBWUEsQ0FBQ3lHLElBQUksQ0FDdEI7Z0JBQUVqQyxPQUFPO1lBQW1ELEdBQzVEO2dCQUFFb0QsUUFBUTtZQUFJO1FBRWxCO1FBRUEsT0FBTzVILHFEQUFZQSxDQUFDeUcsSUFBSSxDQUN0QjtZQUFFakMsT0FBTztRQUFnQyxHQUN6QztZQUFFb0QsUUFBUTtRQUFJO0lBRWxCO0FBQ0YiLCJzb3VyY2VzIjpbIkQ6XFxnaXRodWJcXHRva2VuZGV2LW5ld3Jvb1xcYWRtaW4tcGFuZWxcXHNyY1xcYXBwXFxhcGlcXHRva2Vuc1xccm91dGUudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgTmV4dFJlcXVlc3QsIE5leHRSZXNwb25zZSB9IGZyb20gJ25leHQvc2VydmVyJztcbmltcG9ydCB7IGV0aGVycyB9IGZyb20gJ2V0aGVycyc7XG5pbXBvcnQgeyBwcmlzbWEgfSBmcm9tICcuLi8uLi8uLi9saWIvcHJpc21hJztcbmltcG9ydCBTZWN1cml0eVRva2VuRmFjdG9yeUFCSSBmcm9tICcuLi8uLi8uLi9jb250cmFjdHMvU2VjdXJpdHlUb2tlbkZhY3RvcnkuanNvbic7XG5pbXBvcnQgU2VjdXJpdHlUb2tlbkFCSSBmcm9tICcuLi8uLi8uLi9jb250cmFjdHMvU2VjdXJpdHlUb2tlbi5qc29uJztcbmltcG9ydCBNb2R1bGFyVG9rZW5GYWN0b3J5QUJJIGZyb20gJy4uLy4uLy4uL2NvbnRyYWN0cy9Nb2R1bGFyVG9rZW5GYWN0b3J5Lmpzb24nO1xuaW1wb3J0IFNlY3VyaXR5VG9rZW5Db3JlQUJJIGZyb20gJy4uLy4uLy4uL2NvbnRyYWN0cy9TZWN1cml0eVRva2VuQ29yZS5qc29uJztcblxuLy8gUlBDIFVSTHMgZm9yIGRpZmZlcmVudCBuZXR3b3Jrc1xuY29uc3QgUlBDX1VSTFMgPSB7XG4gIGFtb3k6IHByb2Nlc3MuZW52LkFNT1lfUlBDX1VSTCB8fCAnaHR0cHM6Ly9ycGMtYW1veS5wb2x5Z29uLnRlY2hub2xvZ3kvJyxcbiAgcG9seWdvbjogcHJvY2Vzcy5lbnYuUE9MWUdPTl9SUENfVVJMIHx8ICdodHRwczovL3BvbHlnb24tcnBjLmNvbScsXG4gIHVua25vd246IHByb2Nlc3MuZW52LkFNT1lfUlBDX1VSTCB8fCAnaHR0cHM6Ly9ycGMtYW1veS5wb2x5Z29uLnRlY2hub2xvZ3kvJyxcbn07XG5cbi8vIEZhY3RvcnkgY29udHJhY3QgYWRkcmVzc2VzXG5jb25zdCBGQUNUT1JZX0FERFJFU1MgPSBwcm9jZXNzLmVudi5GQUNUT1JZX0FERFJFU1MgfHwgJzB4MjZmNDY3NmQyMzNDZjg3NmVlNTRCODRCYTIyN2E5MzdBNDZhOTdDZSc7XG5jb25zdCBNT0RVTEFSX0ZBQ1RPUllfQUREUkVTUyA9IHByb2Nlc3MuZW52LkFNT1lfTU9EVUxBUl9UT0tFTl9GQUNUT1JZX0FERFJFU1MgfHwgJzB4YTJDRDMzNjcyQTM4QzY2NzIyZWIwNmNBMWFCOTczODg5QkI4NWIyNCc7XG5cbi8vIEhlbHBlciBmdW5jdGlvbiB0byBkZXRlcm1pbmUgaWYgYW4gYWRkcmVzcyBsb29rcyBsaWtlIGEgcmVhbCBjb250cmFjdCBhZGRyZXNzXG5mdW5jdGlvbiBpc1JlYWxDb250cmFjdEFkZHJlc3MoYWRkcmVzczogc3RyaW5nKTogYm9vbGVhbiB7XG4gIC8vIFNraXAgb2J2aW91cyB0ZXN0IGFkZHJlc3Nlc1xuICBpZiAoIWFkZHJlc3MgfHwgYWRkcmVzcyA9PT0gZXRoZXJzLlplcm9BZGRyZXNzKSB7XG4gICAgcmV0dXJuIGZhbHNlO1xuICB9XG5cbiAgLy8gU2tpcCBhZGRyZXNzZXMgdGhhdCBhcmUgY2xlYXJseSB0ZXN0IHBhdHRlcm5zIChhbGwgemVyb3Mgd2l0aCBzbWFsbCBudW1iZXJzKVxuICBjb25zdCB0ZXN0UGF0dGVybnMgPSBbXG4gICAgL14weDArWzAtOWEtZl17MSwyfSQvaSwgIC8vIDB4MDAwLi4uMDAxOCwgMHgwMDAuLi4wMDYsIGV0Yy5cbiAgICAvXjB4MTIzNDU2Nzg5MDEyMzQ1Njc4OTAxMjM0NTY3ODkwMTIzNDU2Nzg5MCQvaSwgLy8gT3VyIHRlc3QgYWRkcmVzc1xuICAgIC9eMHgwKyQvaSAvLyBBbGwgemVyb3NcbiAgXTtcblxuICByZXR1cm4gIXRlc3RQYXR0ZXJucy5zb21lKHBhdHRlcm4gPT4gcGF0dGVybi50ZXN0KGFkZHJlc3MpKTtcbn1cblxuLy8gSGVscGVyIGZ1bmN0aW9uIHRvIGZldGNoIG1vZHVsYXIgdG9rZW5zIGZyb20gdGhlIE1vZHVsYXJUb2tlbkZhY3RvcnlcbmFzeW5jIGZ1bmN0aW9uIGZldGNoTW9kdWxhclRva2Vucyhwcm92aWRlcjogZXRoZXJzLkpzb25ScGNQcm92aWRlcikge1xuICB0cnkge1xuICAgIGNvbnNvbGUubG9nKGBGZXRjaGluZyBtb2R1bGFyIHRva2VucyBmcm9tIGZhY3Rvcnk6ICR7TU9EVUxBUl9GQUNUT1JZX0FERFJFU1N9YCk7XG5cbiAgICBjb25zdCBtb2R1bGFyRmFjdG9yeSA9IG5ldyBldGhlcnMuQ29udHJhY3QoTU9EVUxBUl9GQUNUT1JZX0FERFJFU1MsIE1vZHVsYXJUb2tlbkZhY3RvcnlBQkksIHByb3ZpZGVyKTtcblxuICAgIC8vIEdldCB0b2tlbiBjb3VudFxuICAgIGNvbnN0IHRva2VuQ291bnQgPSBhd2FpdCBtb2R1bGFyRmFjdG9yeS5nZXREZXBsb3llZFRva2Vuc0NvdW50KCk7XG4gICAgY29uc29sZS5sb2coYE1vZHVsYXIgZmFjdG9yeSByZXBvcnRzICR7dG9rZW5Db3VudH0gZGVwbG95ZWQgdG9rZW5zYCk7XG5cbiAgICBpZiAodG9rZW5Db3VudCA9PT0gMCkge1xuICAgICAgcmV0dXJuIFtdO1xuICAgIH1cblxuICAgIC8vIEdldCBhbGwgYWN0aXZlIHRva2Vuc1xuICAgIGNvbnN0IGFjdGl2ZVRva2VucyA9IGF3YWl0IG1vZHVsYXJGYWN0b3J5LmdldEFjdGl2ZVRva2VucygpO1xuICAgIGNvbnNvbGUubG9nKFwiUmV0cmlldmVkIG1vZHVsYXIgdG9rZW4gYWRkcmVzc2VzOlwiLCBhY3RpdmVUb2tlbnMpO1xuXG4gICAgY29uc3QgdG9rZW5zID0gW107XG5cbiAgICBmb3IgKGNvbnN0IGFkZHJlc3Mgb2YgYWN0aXZlVG9rZW5zKSB7XG4gICAgICB0cnkge1xuICAgICAgICAvLyBHZXQgdG9rZW4gaW5mbyBmcm9tIGZhY3RvcnlcbiAgICAgICAgY29uc3QgdG9rZW5JbmZvID0gYXdhaXQgbW9kdWxhckZhY3RvcnkuZ2V0VG9rZW5JbmZvKGFkZHJlc3MpO1xuXG4gICAgICAgIC8vIEdldCBhZGRpdGlvbmFsIGRldGFpbHMgZnJvbSB0aGUgdG9rZW4gY29udHJhY3RcbiAgICAgICAgY29uc3QgdG9rZW5Db250cmFjdCA9IG5ldyBldGhlcnMuQ29udHJhY3QoYWRkcmVzcywgU2VjdXJpdHlUb2tlbkNvcmVBQkksIHByb3ZpZGVyKTtcblxuICAgICAgICBjb25zdCBbdG90YWxTdXBwbHksIHZlcnNpb25dID0gYXdhaXQgUHJvbWlzZS5hbGwoW1xuICAgICAgICAgIHRva2VuQ29udHJhY3QudG90YWxTdXBwbHkoKSxcbiAgICAgICAgICB0b2tlbkNvbnRyYWN0LnZlcnNpb24oKS5jYXRjaCgoKSA9PiBcIjEuMC4wXCIpXG4gICAgICAgIF0pO1xuXG4gICAgICAgIC8vIEdldCBtZXRhZGF0YSBpZiBhdmFpbGFibGVcbiAgICAgICAgbGV0IG1ldGFkYXRhID0gbnVsbDtcbiAgICAgICAgdHJ5IHtcbiAgICAgICAgICBtZXRhZGF0YSA9IGF3YWl0IHRva2VuQ29udHJhY3QuZ2V0TWV0YWRhdGEoKTtcbiAgICAgICAgfSBjYXRjaCAoZSkge1xuICAgICAgICAgIC8vIE1ldGFkYXRhIG1pZ2h0IG5vdCBiZSBhdmFpbGFibGVcbiAgICAgICAgfVxuXG4gICAgICAgIHRva2Vucy5wdXNoKHtcbiAgICAgICAgICBpZDogYG1vZHVsYXItJHthZGRyZXNzfWAsXG4gICAgICAgICAgYWRkcmVzcyxcbiAgICAgICAgICBuYW1lOiB0b2tlbkluZm8ubmFtZSxcbiAgICAgICAgICBzeW1ib2w6IHRva2VuSW5mby5zeW1ib2wsXG4gICAgICAgICAgZGVjaW1hbHM6IE51bWJlcih0b2tlbkluZm8uZGVjaW1hbHMpLFxuICAgICAgICAgIG1heFN1cHBseTogZXRoZXJzLmZvcm1hdFVuaXRzKHRva2VuSW5mby5tYXhTdXBwbHksIHRva2VuSW5mby5kZWNpbWFscyksXG4gICAgICAgICAgdG90YWxTdXBwbHk6IGV0aGVycy5mb3JtYXRVbml0cyh0b3RhbFN1cHBseSwgdG9rZW5JbmZvLmRlY2ltYWxzKSxcbiAgICAgICAgICB0b2tlblR5cGU6ICdNb2R1bGFyIFNlY3VyaXR5IFRva2VuJyxcbiAgICAgICAgICB0b2tlblByaWNlOiBtZXRhZGF0YT8udG9rZW5QcmljZSB8fCAnTi9BJyxcbiAgICAgICAgICBjdXJyZW5jeTogJ1VTRCcsIC8vIERlZmF1bHQsIGNvdWxkIGJlIHBhcnNlZCBmcm9tIHRva2VuUHJpY2VcbiAgICAgICAgICBuZXR3b3JrOiAnYW1veScsXG4gICAgICAgICAgaGFzS1lDOiB0cnVlLCAvLyBNb2R1bGFyIHRva2VucyBzdXBwb3J0IEtZQ1xuICAgICAgICAgIGlzQWN0aXZlOiB0b2tlbkluZm8uaXNBY3RpdmUsXG4gICAgICAgICAgYWRtaW5BZGRyZXNzOiB0b2tlbkluZm8uYWRtaW4sXG4gICAgICAgICAgd2hpdGVsaXN0QWRkcmVzczogbnVsbCwgLy8gV291bGQgbmVlZCB0byBnZXQgZnJvbSBtb2R1bGVzXG4gICAgICAgICAgdHJhbnNhY3Rpb25IYXNoOiBudWxsLCAvLyBDb3VsZCBnZXQgZnJvbSBkZXBsb3ltZW50IGV2ZW50c1xuICAgICAgICAgIGJsb2NrTnVtYmVyOiBudWxsLFxuICAgICAgICAgIGNyZWF0ZWRBdDogbmV3IERhdGUoTnVtYmVyKHRva2VuSW5mby5kZXBsb3ltZW50VGltZSkgKiAxMDAwKS50b0lTT1N0cmluZygpLFxuICAgICAgICAgIHVwZGF0ZWRBdDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLFxuICAgICAgICAgIHZlcnNpb246IHZlcnNpb24sXG4gICAgICAgICAgaXNNb2R1bGFyOiB0cnVlXG4gICAgICAgIH0pO1xuXG4gICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICBjb25zb2xlLndhcm4oYEZhaWxlZCB0byBsb2FkIG1vZHVsYXIgdG9rZW4gZGV0YWlscyBmb3IgJHthZGRyZXNzfTpgLCBlcnJvcik7XG4gICAgICB9XG4gICAgfVxuXG4gICAgY29uc29sZS5sb2coYFN1Y2Nlc3NmdWxseSBsb2FkZWQgJHt0b2tlbnMubGVuZ3RofSBtb2R1bGFyIHRva2Vuc2ApO1xuICAgIHJldHVybiB0b2tlbnM7XG5cbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCdFcnJvciBmZXRjaGluZyBtb2R1bGFyIHRva2VuczonLCBlcnJvcik7XG4gICAgcmV0dXJuIFtdO1xuICB9XG59XG5cbi8vIEdFVCAvYXBpL3Rva2VucyAtIEdldCBhbGwgdG9rZW5zIGZyb20gZGF0YWJhc2UgYW5kIG9wdGlvbmFsbHkgc3luYyB3aXRoIGJsb2NrY2hhaW5cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBHRVQocmVxdWVzdDogTmV4dFJlcXVlc3QpIHtcbiAgdHJ5IHtcbiAgICBjb25zdCB7IHNlYXJjaFBhcmFtcyB9ID0gbmV3IFVSTChyZXF1ZXN0LnVybCk7XG4gICAgY29uc3Qgc291cmNlID0gc2VhcmNoUGFyYW1zLmdldCgnc291cmNlJykgfHwgJ2RhdGFiYXNlJzsgLy8gJ2RhdGFiYXNlJywgJ2Jsb2NrY2hhaW4nLCBvciAnYm90aCdcblxuICAgIGlmIChzb3VyY2UgPT09ICdkYXRhYmFzZScgfHwgc291cmNlID09PSAnYm90aCcpIHtcbiAgICAgIC8vIEdldCB0b2tlbnMgZnJvbSBkYXRhYmFzZVxuICAgICAgY29uc3QgZGJUb2tlbnMgPSBhd2FpdCBwcmlzbWEudG9rZW4uZmluZE1hbnkoe1xuICAgICAgICBvcmRlckJ5OiB7IGNyZWF0ZWRBdDogJ2Rlc2MnIH0sXG4gICAgICAgIGluY2x1ZGU6IHtcbiAgICAgICAgICB0cmFuc2FjdGlvbnM6IHtcbiAgICAgICAgICAgIHRha2U6IDUsXG4gICAgICAgICAgICBvcmRlckJ5OiB7IGNyZWF0ZWRBdDogJ2Rlc2MnIH1cbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIH0pO1xuXG4gICAgICBpZiAoc291cmNlID09PSAnZGF0YWJhc2UnKSB7XG4gICAgICAgIC8vIFN5bmMgdG90YWxTdXBwbHkgZnJvbSBibG9ja2NoYWluIGZvciBkYXRhYmFzZSB0b2tlbnMgaWYgbmVlZGVkXG4gICAgICAgIGNvbnN0IHRva2Vuc1dpdGhVcGRhdGVkU3VwcGx5ID0gYXdhaXQgUHJvbWlzZS5hbGwoXG4gICAgICAgICAgZGJUb2tlbnMubWFwKGFzeW5jICh0b2tlbikgPT4ge1xuICAgICAgICAgICAgLy8gT25seSBhdHRlbXB0IGJsb2NrY2hhaW4gc3luYyBmb3IgdG9rZW5zIHRoYXQ6XG4gICAgICAgICAgICAvLyAxLiBIYXZlIHRvdGFsU3VwcGx5IG9mIDAgb3Igbm90IHNldFxuICAgICAgICAgICAgLy8gMi4gSGF2ZSBhIHJlYWwtbG9va2luZyBhZGRyZXNzIChub3QgdGVzdCBhZGRyZXNzZXMpXG4gICAgICAgICAgICAvLyAzLiBBcmUgb24gYSBzdXBwb3J0ZWQgbmV0d29ya1xuICAgICAgICAgICAgY29uc3Qgc2hvdWxkU3luY0Zyb21CbG9ja2NoYWluID0gKFxuICAgICAgICAgICAgICAodG9rZW4udG90YWxTdXBwbHkgPT09ICcwJyB8fCAhdG9rZW4udG90YWxTdXBwbHkpICYmXG4gICAgICAgICAgICAgIGlzUmVhbENvbnRyYWN0QWRkcmVzcyh0b2tlbi5hZGRyZXNzKSAmJlxuICAgICAgICAgICAgICAodG9rZW4ubmV0d29yayA9PT0gJ2Ftb3knIHx8IHRva2VuLm5ldHdvcmsgPT09ICdwb2x5Z29uJylcbiAgICAgICAgICAgICk7XG5cbiAgICAgICAgICAgIGlmIChzaG91bGRTeW5jRnJvbUJsb2NrY2hhaW4pIHtcbiAgICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgICBjb25zdCBuZXR3b3JrID0gdG9rZW4ubmV0d29yayB8fCAnYW1veSc7XG4gICAgICAgICAgICAgICAgY29uc3QgcnBjVXJsID0gUlBDX1VSTFNbbmV0d29ya10gfHwgUlBDX1VSTFMuYW1veTtcbiAgICAgICAgICAgICAgICBjb25zdCBwcm92aWRlciA9IG5ldyBldGhlcnMuSnNvblJwY1Byb3ZpZGVyKHJwY1VybCk7XG5cbiAgICAgICAgICAgICAgICAvLyBGaXJzdCBjaGVjayBpZiB0aGVyZSdzIGNvZGUgYXQgdGhpcyBhZGRyZXNzXG4gICAgICAgICAgICAgICAgY29uc3QgY29kZSA9IGF3YWl0IHByb3ZpZGVyLmdldENvZGUodG9rZW4uYWRkcmVzcyk7XG4gICAgICAgICAgICAgICAgaWYgKGNvZGUgPT09ICcweCcpIHtcbiAgICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKGBTa2lwcGluZyBibG9ja2NoYWluIHN5bmMgZm9yICR7dG9rZW4uYWRkcmVzc30gLSBubyBjb250cmFjdCBkZXBsb3llZGApO1xuICAgICAgICAgICAgICAgICAgcmV0dXJuIHRva2VuO1xuICAgICAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgICAgIGNvbnN0IHRva2VuQ29udHJhY3QgPSBuZXcgZXRoZXJzLkNvbnRyYWN0KHRva2VuLmFkZHJlc3MsIFNlY3VyaXR5VG9rZW5BQkkuYWJpLCBwcm92aWRlcik7XG5cbiAgICAgICAgICAgICAgICBjb25zdCB0b3RhbFN1cHBseVJhdyA9IGF3YWl0IHRva2VuQ29udHJhY3QudG90YWxTdXBwbHkoKTtcbiAgICAgICAgICAgICAgICBjb25zdCBkZWNpbWFscyA9IHRva2VuLmRlY2ltYWxzIHx8IDE4O1xuICAgICAgICAgICAgICAgIGNvbnN0IHRvdGFsU3VwcGx5ID0gZGVjaW1hbHMgPT09IDBcbiAgICAgICAgICAgICAgICAgID8gdG90YWxTdXBwbHlSYXcudG9TdHJpbmcoKVxuICAgICAgICAgICAgICAgICAgOiBldGhlcnMuZm9ybWF0VW5pdHModG90YWxTdXBwbHlSYXcsIGRlY2ltYWxzKTtcblxuICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKGBTdWNjZXNzZnVsbHkgc3luY2VkIHRvdGFsU3VwcGx5IGZvciAke3Rva2VuLnN5bWJvbH06ICR7dG90YWxTdXBwbHl9YCk7XG5cbiAgICAgICAgICAgICAgICAvLyBVcGRhdGUgdGhlIGRhdGFiYXNlIHdpdGggdGhlIGZldGNoZWQgdG90YWxTdXBwbHlcbiAgICAgICAgICAgICAgICBhd2FpdCBwcmlzbWEudG9rZW4udXBkYXRlKHtcbiAgICAgICAgICAgICAgICAgIHdoZXJlOiB7IGlkOiB0b2tlbi5pZCB9LFxuICAgICAgICAgICAgICAgICAgZGF0YTogeyB0b3RhbFN1cHBseSB9XG4gICAgICAgICAgICAgICAgfSk7XG5cbiAgICAgICAgICAgICAgICByZXR1cm4geyAuLi50b2tlbiwgdG90YWxTdXBwbHkgfTtcbiAgICAgICAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgICAgICAgICBjb25zb2xlLndhcm4oYENvdWxkIG5vdCBmZXRjaCB0b3RhbFN1cHBseSBmb3IgdG9rZW4gJHt0b2tlbi5hZGRyZXNzfTogJHtlcnJvci5tZXNzYWdlfWApO1xuICAgICAgICAgICAgICAgIHJldHVybiB0b2tlbjtcbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgcmV0dXJuIHRva2VuO1xuICAgICAgICAgIH0pXG4gICAgICAgICk7XG5cbiAgICAgICAgLy8gQWxzbyBmZXRjaCBtb2R1bGFyIHRva2VucyBmcm9tIGJsb2NrY2hhaW5cbiAgICAgICAgdHJ5IHtcbiAgICAgICAgICBjb25zdCBuZXR3b3JrID0gJ2Ftb3knO1xuICAgICAgICAgIGNvbnN0IHJwY1VybCA9IFJQQ19VUkxTW25ldHdvcmtdO1xuICAgICAgICAgIGNvbnN0IHByb3ZpZGVyID0gbmV3IGV0aGVycy5Kc29uUnBjUHJvdmlkZXIocnBjVXJsKTtcbiAgICAgICAgICBjb25zdCBtb2R1bGFyVG9rZW5zID0gYXdhaXQgZmV0Y2hNb2R1bGFyVG9rZW5zKHByb3ZpZGVyKTtcblxuICAgICAgICAgIC8vIENvbWJpbmUgZGF0YWJhc2UgdG9rZW5zIHdpdGggbW9kdWxhciB0b2tlbnNcbiAgICAgICAgICBjb25zdCBhbGxUb2tlbnMgPSBbLi4udG9rZW5zV2l0aFVwZGF0ZWRTdXBwbHksIC4uLm1vZHVsYXJUb2tlbnNdO1xuXG4gICAgICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKGFsbFRva2Vucyk7XG4gICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgY29uc29sZS53YXJuKCdDb3VsZCBub3QgZmV0Y2ggbW9kdWxhciB0b2tlbnM6JywgZXJyb3IpO1xuICAgICAgICAgIC8vIFJldHVybiBqdXN0IGRhdGFiYXNlIHRva2VucyBpZiBtb2R1bGFyIGZldGNoIGZhaWxzXG4gICAgICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHRva2Vuc1dpdGhVcGRhdGVkU3VwcGx5KTtcbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICAvLyBJZiAnYm90aCcsIHdlJ2xsIG1lcmdlIHdpdGggYmxvY2tjaGFpbiBkYXRhIGJlbG93XG4gICAgICBpZiAoc291cmNlID09PSAnYm90aCcpIHtcbiAgICAgICAgLy8gRm9yIG5vdywganVzdCByZXR1cm4gZGF0YWJhc2UgdG9rZW5zXG4gICAgICAgIC8vIFRPRE86IEltcGxlbWVudCBibG9ja2NoYWluIHN5bmMgaWYgbmVlZGVkXG4gICAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihkYlRva2Vucyk7XG4gICAgICB9XG4gICAgfVxuXG4gICAgLy8gQmxvY2tjaGFpbi1vbmx5IGZldGNoIChsZWdhY3kgYmVoYXZpb3IpXG4gICAgY29uc3QgbmV0d29yayA9ICdhbW95JzsgLy8gRGVmYXVsdCB0byBhbW95IG5ldHdvcmtcbiAgICBjb25zdCBycGNVcmwgPSBSUENfVVJMU1tuZXR3b3JrXTtcblxuICAgIC8vIENyZWF0ZSBwcm92aWRlclxuICAgIGNvbnN0IHByb3ZpZGVyID0gbmV3IGV0aGVycy5Kc29uUnBjUHJvdmlkZXIocnBjVXJsKTtcblxuICAgIC8vIENyZWF0ZSBmYWN0b3J5IGNvbnRyYWN0IGluc3RhbmNlXG4gICAgY29uc3QgZmFjdG9yeSA9IG5ldyBldGhlcnMuQ29udHJhY3QoRkFDVE9SWV9BRERSRVNTLCBTZWN1cml0eVRva2VuRmFjdG9yeUFCSS5hYmksIHByb3ZpZGVyKTtcblxuICAgIGNvbnNvbGUubG9nKGBGZXRjaGluZyB0b2tlbnMgZnJvbSBmYWN0b3J5OiAke0ZBQ1RPUllfQUREUkVTU31gKTtcblxuICAgIC8vIEdldCB0b2tlbiBjb3VudFxuICAgIGNvbnN0IHRva2VuQ291bnQgPSBhd2FpdCBmYWN0b3J5LmdldFRva2VuQ291bnQoKTtcbiAgICBjb25zb2xlLmxvZyhgRmFjdG9yeSByZXBvcnRzICR7dG9rZW5Db3VudH0gZGVwbG95ZWQgdG9rZW5zYCk7XG5cbiAgICBpZiAodG9rZW5Db3VudCA9PT0gMCkge1xuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFtdKTtcbiAgICB9XG5cbiAgICAvLyBHZXQgYWxsIHRva2VuIGFkZHJlc3Nlc1xuICAgIGNvbnN0IHRva2VuQWRkcmVzc2VzID0gYXdhaXQgZmFjdG9yeS5nZXRBbGxEZXBsb3llZFRva2VucygpO1xuICAgIGNvbnNvbGUubG9nKFwiUmV0cmlldmVkIHRva2VuIGFkZHJlc3NlczpcIiwgdG9rZW5BZGRyZXNzZXMpO1xuXG4gICAgLy8gTG9hZCBkZXRhaWxzIGZvciBlYWNoIHRva2VuXG4gICAgY29uc3QgdG9rZW5zID0gW107XG5cbiAgICBmb3IgKGNvbnN0IGFkZHJlc3Mgb2YgdG9rZW5BZGRyZXNzZXMpIHtcbiAgICAgIHRyeSB7XG4gICAgICAgIGNvbnN0IHRva2VuQ29udHJhY3QgPSBuZXcgZXRoZXJzLkNvbnRyYWN0KGFkZHJlc3MsIFNlY3VyaXR5VG9rZW5BQkkuYWJpLCBwcm92aWRlcik7XG5cbiAgICAgICAgLy8gR2V0IGJhc2ljIHRva2VuIGluZm9cbiAgICAgICAgY29uc3QgW25hbWUsIHN5bWJvbCwgdG90YWxTdXBwbHksIGRlY2ltYWxzLCBvd25lcl0gPSBhd2FpdCBQcm9taXNlLmFsbChbXG4gICAgICAgICAgdG9rZW5Db250cmFjdC5uYW1lKCksXG4gICAgICAgICAgdG9rZW5Db250cmFjdC5zeW1ib2woKSxcbiAgICAgICAgICB0b2tlbkNvbnRyYWN0LnRvdGFsU3VwcGx5KCksXG4gICAgICAgICAgdG9rZW5Db250cmFjdC5kZWNpbWFscygpLFxuICAgICAgICAgIHRva2VuQ29udHJhY3Qub3duZXIoKVxuICAgICAgICBdKTtcblxuICAgICAgICAvLyBHZXQgYWRkaXRpb25hbCB0b2tlbiBkZXRhaWxzXG4gICAgICAgIGxldCB0b2tlblR5cGUgPSAnVU5LTk9XTic7XG4gICAgICAgIGxldCBzZWN1cml0eVR5cGUgPSAnVU5LTk9XTic7XG4gICAgICAgIGxldCBjcmVhdGVkQXQgPSBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCk7XG5cbiAgICAgICAgdHJ5IHtcbiAgICAgICAgICAvLyBUcnkgdG8gZ2V0IHRva2VuIG1ldGFkYXRhIGlmIGF2YWlsYWJsZVxuICAgICAgICAgIGNvbnN0IHRva2VuRGV0YWlscyA9IGF3YWl0IHRva2VuQ29udHJhY3QudG9rZW5EZXRhaWxzKCk7XG4gICAgICAgICAgaWYgKHRva2VuRGV0YWlscykge1xuICAgICAgICAgICAgLy8gUGFyc2UgdG9rZW4gZGV0YWlscyBpZiBpdCdzIGEgSlNPTiBzdHJpbmdcbiAgICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICAgIGNvbnN0IHBhcnNlZCA9IEpTT04ucGFyc2UodG9rZW5EZXRhaWxzKTtcbiAgICAgICAgICAgICAgdG9rZW5UeXBlID0gcGFyc2VkLnRva2VuVHlwZSB8fCAnVU5LTk9XTic7XG4gICAgICAgICAgICAgIHNlY3VyaXR5VHlwZSA9IHBhcnNlZC5zZWN1cml0eVR5cGUgfHwgJ1VOS05PV04nO1xuICAgICAgICAgICAgfSBjYXRjaCB7XG4gICAgICAgICAgICAgIC8vIElmIG5vdCBKU09OLCB1c2UgYXMgaXNcbiAgICAgICAgICAgICAgdG9rZW5UeXBlID0gJ0VRVUlUWSc7IC8vIERlZmF1bHRcbiAgICAgICAgICAgICAgc2VjdXJpdHlUeXBlID0gJ1JFR1VMQVRJT05fRCc7IC8vIERlZmF1bHRcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9XG4gICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgY29uc29sZS53YXJuKGBDb3VsZCBub3QgZmV0Y2ggdG9rZW4gZGV0YWlscyBmb3IgJHthZGRyZXNzfTpgLCBlcnJvcik7XG4gICAgICAgIH1cblxuICAgICAgICAvLyBUcnkgdG8gZ2V0IGNyZWF0aW9uIHRpbWVzdGFtcCBmcm9tIGV2ZW50c1xuICAgICAgICB0cnkge1xuICAgICAgICAgIGNvbnN0IGZpbHRlciA9IGZhY3RvcnkuZmlsdGVycy5Ub2tlbkRlcGxveWVkKG51bGwsIGFkZHJlc3MpO1xuICAgICAgICAgIGNvbnN0IGV2ZW50cyA9IGF3YWl0IGZhY3RvcnkucXVlcnlGaWx0ZXIoZmlsdGVyLCAwLCAnbGF0ZXN0Jyk7XG4gICAgICAgICAgaWYgKGV2ZW50cy5sZW5ndGggPiAwKSB7XG4gICAgICAgICAgICBjb25zdCBibG9jayA9IGF3YWl0IHByb3ZpZGVyLmdldEJsb2NrKGV2ZW50c1swXS5ibG9ja051bWJlcik7XG4gICAgICAgICAgICBpZiAoYmxvY2spIHtcbiAgICAgICAgICAgICAgY3JlYXRlZEF0ID0gbmV3IERhdGUoYmxvY2sudGltZXN0YW1wICogMTAwMCkudG9JU09TdHJpbmcoKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9XG4gICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgY29uc29sZS53YXJuKGBDb3VsZCBub3QgZmV0Y2ggY3JlYXRpb24gdGltZSBmb3IgJHthZGRyZXNzfTpgLCBlcnJvcik7XG4gICAgICAgIH1cblxuICAgICAgICB0b2tlbnMucHVzaCh7XG4gICAgICAgICAgYWRkcmVzcyxcbiAgICAgICAgICBuYW1lLFxuICAgICAgICAgIHN5bWJvbCxcbiAgICAgICAgICBkZWNpbWFsczogTnVtYmVyKGRlY2ltYWxzKSxcbiAgICAgICAgICB0b3RhbFN1cHBseTogdG90YWxTdXBwbHkudG9TdHJpbmcoKSxcbiAgICAgICAgICBvd25lcixcbiAgICAgICAgICBzZWN1cml0eVR5cGUsXG4gICAgICAgICAgdG9rZW5UeXBlLFxuICAgICAgICAgIGNyZWF0ZWRBdFxuICAgICAgICB9KTtcblxuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgY29uc29sZS53YXJuKGBGYWlsZWQgdG8gbG9hZCB0b2tlbiBkZXRhaWxzIGZvciAke2FkZHJlc3N9OmAsIGVycm9yKTtcbiAgICAgICAgLy8gQWRkIG1pbmltYWwgdG9rZW4gaW5mbyBldmVuIGlmIGRldGFpbHMgZmFpbFxuICAgICAgICB0b2tlbnMucHVzaCh7XG4gICAgICAgICAgYWRkcmVzcyxcbiAgICAgICAgICBuYW1lOiAnVW5rbm93biBUb2tlbicsXG4gICAgICAgICAgc3ltYm9sOiAnVU5LTk9XTicsXG4gICAgICAgICAgZGVjaW1hbHM6IDAsXG4gICAgICAgICAgdG90YWxTdXBwbHk6ICcwJyxcbiAgICAgICAgICBvd25lcjogZXRoZXJzLlplcm9BZGRyZXNzLFxuICAgICAgICAgIHNlY3VyaXR5VHlwZTogJ1VOS05PV04nLFxuICAgICAgICAgIHRva2VuVHlwZTogJ1VOS05PV04nLFxuICAgICAgICAgIGNyZWF0ZWRBdDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpXG4gICAgICAgIH0pO1xuICAgICAgfVxuICAgIH1cblxuICAgIGNvbnNvbGUubG9nKGBTdWNjZXNzZnVsbHkgbG9hZGVkICR7dG9rZW5zLmxlbmd0aH0gdG9rZW5zYCk7XG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHRva2Vucyk7XG5cbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCdFcnJvciBmZXRjaGluZyB0b2tlbnM6JywgZXJyb3IpO1xuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgIHsgZXJyb3I6ICdGYWlsZWQgdG8gZmV0Y2ggdG9rZW5zIGZyb20gZmFjdG9yeScgfSxcbiAgICAgIHsgc3RhdHVzOiA1MDAgfVxuICAgICk7XG4gIH1cbn1cblxuLy8gUE9TVCAvYXBpL3Rva2VucyAtIENyZWF0ZSBhIG5ldyB0b2tlbiByZWNvcmQgaW4gZGF0YWJhc2VcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBQT1NUKHJlcXVlc3Q6IE5leHRSZXF1ZXN0KSB7XG4gIHRyeSB7XG4gICAgY29uc3QgYm9keSA9IGF3YWl0IHJlcXVlc3QuanNvbigpO1xuXG4gICAgLy8gVmFsaWRhdGUgcmVxdWlyZWQgZmllbGRzXG4gICAgY29uc3QgcmVxdWlyZWRGaWVsZHMgPSBbXG4gICAgICAnYWRkcmVzcycsICduYW1lJywgJ3N5bWJvbCdcbiAgICBdO1xuXG4gICAgZm9yIChjb25zdCBmaWVsZCBvZiByZXF1aXJlZEZpZWxkcykge1xuICAgICAgaWYgKCFib2R5W2ZpZWxkXSkge1xuICAgICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXG4gICAgICAgICAgeyBlcnJvcjogYE1pc3NpbmcgcmVxdWlyZWQgZmllbGQ6ICR7ZmllbGR9YCB9LFxuICAgICAgICAgIHsgc3RhdHVzOiA0MDAgfVxuICAgICAgICApO1xuICAgICAgfVxuICAgIH1cblxuICAgIC8vIENoZWNrIGlmIHRva2VuIGFscmVhZHkgZXhpc3RzXG4gICAgY29uc3QgZXhpc3RpbmdUb2tlbiA9IGF3YWl0IHByaXNtYS50b2tlbi5maW5kVW5pcXVlKHtcbiAgICAgIHdoZXJlOiB7IGFkZHJlc3M6IGJvZHkuYWRkcmVzcyB9XG4gICAgfSk7XG5cbiAgICBpZiAoZXhpc3RpbmdUb2tlbikge1xuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgICB7IGVycm9yOiAnVG9rZW4gd2l0aCB0aGlzIGFkZHJlc3MgYWxyZWFkeSBleGlzdHMnIH0sXG4gICAgICAgIHsgc3RhdHVzOiA0MDkgfVxuICAgICAgKTtcbiAgICB9XG5cbiAgICAvLyBDcmVhdGUgbmV3IHRva2VuIHJlY29yZFxuICAgIGNvbnN0IG5ld1Rva2VuID0gYXdhaXQgcHJpc21hLnRva2VuLmNyZWF0ZSh7XG4gICAgICBkYXRhOiB7XG4gICAgICAgIGFkZHJlc3M6IGJvZHkuYWRkcmVzcyxcbiAgICAgICAgdHJhbnNhY3Rpb25IYXNoOiBib2R5LnRyYW5zYWN0aW9uSGFzaCB8fCBudWxsLFxuICAgICAgICBibG9ja051bWJlcjogYm9keS5ibG9ja051bWJlciB8fCBudWxsLFxuICAgICAgICBuZXR3b3JrOiBib2R5Lm5ldHdvcmsgfHwgJ2Ftb3knLFxuICAgICAgICBuYW1lOiBib2R5Lm5hbWUsXG4gICAgICAgIHN5bWJvbDogYm9keS5zeW1ib2wsXG4gICAgICAgIGRlY2ltYWxzOiBib2R5LmRlY2ltYWxzICE9PSB1bmRlZmluZWQgPyBwYXJzZUludChib2R5LmRlY2ltYWxzKSA6IDE4LFxuICAgICAgICBtYXhTdXBwbHk6IGJvZHkubWF4U3VwcGx5IHx8ICcxMDAwMDAwJyxcbiAgICAgICAgdG90YWxTdXBwbHk6IGJvZHkudG90YWxTdXBwbHkgfHwgJzAnLCAvLyBJbml0aWFsaXplIHdpdGggMCBvciBwcm92aWRlZCB2YWx1ZVxuICAgICAgICB0b2tlblR5cGU6IGJvZHkudG9rZW5UeXBlIHx8ICdlcXVpdHknLFxuICAgICAgICB0b2tlblByaWNlOiBib2R5LnRva2VuUHJpY2UgfHwgJzEwIFVTRCcsXG4gICAgICAgIGN1cnJlbmN5OiBib2R5LmN1cnJlbmN5IHx8ICdVU0QnLFxuICAgICAgICBib251c1RpZXJzOiBib2R5LmJvbnVzVGllcnMgfHwgbnVsbCxcbiAgICAgICAgdG9rZW5JbWFnZVVybDogYm9keS50b2tlbkltYWdlVXJsIHx8IG51bGwsXG4gICAgICAgIHdoaXRlbGlzdEFkZHJlc3M6IGJvZHkud2hpdGVsaXN0QWRkcmVzcyB8fCBudWxsLFxuICAgICAgICBhZG1pbkFkZHJlc3M6IGJvZHkuYWRtaW5BZGRyZXNzIHx8IG51bGwsXG4gICAgICAgIGhhc0tZQzogYm9keS5oYXNLWUMgfHwgZmFsc2UsXG4gICAgICAgIGlzQWN0aXZlOiBib2R5LmlzQWN0aXZlICE9PSB1bmRlZmluZWQgPyBib2R5LmlzQWN0aXZlIDogdHJ1ZSxcbiAgICAgICAgc2VsZWN0ZWRDbGFpbXM6IEFycmF5LmlzQXJyYXkoYm9keS5zZWxlY3RlZENsYWltcykgPyBib2R5LnNlbGVjdGVkQ2xhaW1zLmpvaW4oJywnKSA6IGJvZHkuc2VsZWN0ZWRDbGFpbXMgfHwgbnVsbCxcbiAgICAgICAgZGVwbG95ZWRCeTogYm9keS5kZXBsb3llZEJ5IHx8IG51bGwsXG4gICAgICAgIGRlcGxveW1lbnROb3RlczogYm9keS5kZXBsb3ltZW50Tm90ZXMgfHwgbnVsbFxuICAgICAgfVxuICAgIH0pO1xuXG4gICAgY29uc29sZS5sb2coYENyZWF0ZWQgbmV3IHRva2VuIHJlY29yZDogJHtuZXdUb2tlbi5zeW1ib2x9ICgke25ld1Rva2VuLmFkZHJlc3N9KWApO1xuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihuZXdUb2tlbiwgeyBzdGF0dXM6IDIwMSB9KTtcblxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGNyZWF0aW5nIHRva2VuOicsIGVycm9yKTtcblxuICAgIC8vIEhhbmRsZSBQcmlzbWEgdW5pcXVlIGNvbnN0cmFpbnQgZXJyb3JzXG4gICAgaWYgKGVycm9yLmNvZGUgPT09ICdQMjAwMicpIHtcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgICAgeyBlcnJvcjogJ1Rva2VuIHdpdGggdGhpcyBhZGRyZXNzIG9yIHN5bWJvbCBhbHJlYWR5IGV4aXN0cycgfSxcbiAgICAgICAgeyBzdGF0dXM6IDQwOSB9XG4gICAgICApO1xuICAgIH1cblxuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgIHsgZXJyb3I6ICdGYWlsZWQgdG8gY3JlYXRlIHRva2VuIHJlY29yZCcgfSxcbiAgICAgIHsgc3RhdHVzOiA1MDAgfVxuICAgICk7XG4gIH1cbn1cbiJdLCJuYW1lcyI6WyJOZXh0UmVzcG9uc2UiLCJldGhlcnMiLCJwcmlzbWEiLCJTZWN1cml0eVRva2VuRmFjdG9yeUFCSSIsIlNlY3VyaXR5VG9rZW5BQkkiLCJNb2R1bGFyVG9rZW5GYWN0b3J5QUJJIiwiU2VjdXJpdHlUb2tlbkNvcmVBQkkiLCJSUENfVVJMUyIsImFtb3kiLCJwcm9jZXNzIiwiZW52IiwiQU1PWV9SUENfVVJMIiwicG9seWdvbiIsIlBPTFlHT05fUlBDX1VSTCIsInVua25vd24iLCJGQUNUT1JZX0FERFJFU1MiLCJNT0RVTEFSX0ZBQ1RPUllfQUREUkVTUyIsIkFNT1lfTU9EVUxBUl9UT0tFTl9GQUNUT1JZX0FERFJFU1MiLCJpc1JlYWxDb250cmFjdEFkZHJlc3MiLCJhZGRyZXNzIiwiWmVyb0FkZHJlc3MiLCJ0ZXN0UGF0dGVybnMiLCJzb21lIiwicGF0dGVybiIsInRlc3QiLCJmZXRjaE1vZHVsYXJUb2tlbnMiLCJwcm92aWRlciIsImNvbnNvbGUiLCJsb2ciLCJtb2R1bGFyRmFjdG9yeSIsIkNvbnRyYWN0IiwidG9rZW5Db3VudCIsImdldERlcGxveWVkVG9rZW5zQ291bnQiLCJhY3RpdmVUb2tlbnMiLCJnZXRBY3RpdmVUb2tlbnMiLCJ0b2tlbnMiLCJ0b2tlbkluZm8iLCJnZXRUb2tlbkluZm8iLCJ0b2tlbkNvbnRyYWN0IiwidG90YWxTdXBwbHkiLCJ2ZXJzaW9uIiwiUHJvbWlzZSIsImFsbCIsImNhdGNoIiwibWV0YWRhdGEiLCJnZXRNZXRhZGF0YSIsImUiLCJwdXNoIiwiaWQiLCJuYW1lIiwic3ltYm9sIiwiZGVjaW1hbHMiLCJOdW1iZXIiLCJtYXhTdXBwbHkiLCJmb3JtYXRVbml0cyIsInRva2VuVHlwZSIsInRva2VuUHJpY2UiLCJjdXJyZW5jeSIsIm5ldHdvcmsiLCJoYXNLWUMiLCJpc0FjdGl2ZSIsImFkbWluQWRkcmVzcyIsImFkbWluIiwid2hpdGVsaXN0QWRkcmVzcyIsInRyYW5zYWN0aW9uSGFzaCIsImJsb2NrTnVtYmVyIiwiY3JlYXRlZEF0IiwiRGF0ZSIsImRlcGxveW1lbnRUaW1lIiwidG9JU09TdHJpbmciLCJ1cGRhdGVkQXQiLCJpc01vZHVsYXIiLCJlcnJvciIsIndhcm4iLCJsZW5ndGgiLCJHRVQiLCJyZXF1ZXN0Iiwic2VhcmNoUGFyYW1zIiwiVVJMIiwidXJsIiwic291cmNlIiwiZ2V0IiwiZGJUb2tlbnMiLCJ0b2tlbiIsImZpbmRNYW55Iiwib3JkZXJCeSIsImluY2x1ZGUiLCJ0cmFuc2FjdGlvbnMiLCJ0YWtlIiwidG9rZW5zV2l0aFVwZGF0ZWRTdXBwbHkiLCJtYXAiLCJzaG91bGRTeW5jRnJvbUJsb2NrY2hhaW4iLCJycGNVcmwiLCJKc29uUnBjUHJvdmlkZXIiLCJjb2RlIiwiZ2V0Q29kZSIsImFiaSIsInRvdGFsU3VwcGx5UmF3IiwidG9TdHJpbmciLCJ1cGRhdGUiLCJ3aGVyZSIsImRhdGEiLCJtZXNzYWdlIiwibW9kdWxhclRva2VucyIsImFsbFRva2VucyIsImpzb24iLCJmYWN0b3J5IiwiZ2V0VG9rZW5Db3VudCIsInRva2VuQWRkcmVzc2VzIiwiZ2V0QWxsRGVwbG95ZWRUb2tlbnMiLCJvd25lciIsInNlY3VyaXR5VHlwZSIsInRva2VuRGV0YWlscyIsInBhcnNlZCIsIkpTT04iLCJwYXJzZSIsImZpbHRlciIsImZpbHRlcnMiLCJUb2tlbkRlcGxveWVkIiwiZXZlbnRzIiwicXVlcnlGaWx0ZXIiLCJibG9jayIsImdldEJsb2NrIiwidGltZXN0YW1wIiwic3RhdHVzIiwiUE9TVCIsImJvZHkiLCJyZXF1aXJlZEZpZWxkcyIsImZpZWxkIiwiZXhpc3RpbmdUb2tlbiIsImZpbmRVbmlxdWUiLCJuZXdUb2tlbiIsImNyZWF0ZSIsInVuZGVmaW5lZCIsInBhcnNlSW50IiwiYm9udXNUaWVycyIsInRva2VuSW1hZ2VVcmwiLCJzZWxlY3RlZENsYWltcyIsIkFycmF5IiwiaXNBcnJheSIsImpvaW4iLCJkZXBsb3llZEJ5IiwiZGVwbG95bWVudE5vdGVzIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/tokens/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/contracts/ModularTokenFactory.json":
/*!************************************************!*\
  !*** ./src/contracts/ModularTokenFactory.json ***!
  \************************************************/
/***/ ((module) => {

"use strict";
module.exports = /*#__PURE__*/JSON.parse('{"_format":"hh-sol-artifact-1","contractName":"ModularTokenFactory","sourceName":"contracts/ModularTokenFactory.sol","abi":[{"inputs":[{"internalType":"address","name":"_securityTokenImplementation","type":"address"},{"internalType":"address","name":"_upgradeManager","type":"address"},{"internalType":"address","name":"_admin","type":"address"}],"stateMutability":"nonpayable","type":"constructor"},{"inputs":[],"name":"AccessControlBadConfirmation","type":"error"},{"inputs":[{"internalType":"address","name":"account","type":"address"},{"internalType":"bytes32","name":"neededRole","type":"bytes32"}],"name":"AccessControlUnauthorizedAccount","type":"error"},{"inputs":[],"name":"ReentrancyGuardReentrantCall","type":"error"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"bytes32","name":"role","type":"bytes32"},{"indexed":true,"internalType":"bytes32","name":"previousAdminRole","type":"bytes32"},{"indexed":true,"internalType":"bytes32","name":"newAdminRole","type":"bytes32"}],"name":"RoleAdminChanged","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"bytes32","name":"role","type":"bytes32"},{"indexed":true,"internalType":"address","name":"account","type":"address"},{"indexed":true,"internalType":"address","name":"sender","type":"address"}],"name":"RoleGranted","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"bytes32","name":"role","type":"bytes32"},{"indexed":true,"internalType":"address","name":"account","type":"address"},{"indexed":true,"internalType":"address","name":"sender","type":"address"}],"name":"RoleRevoked","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"tokenAddress","type":"address"},{"indexed":true,"internalType":"address","name":"admin","type":"address"}],"name":"TokenDeactivated","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"tokenAddress","type":"address"},{"indexed":true,"internalType":"address","name":"deployer","type":"address"},{"indexed":true,"internalType":"address","name":"admin","type":"address"},{"indexed":false,"internalType":"string","name":"name","type":"string"},{"indexed":false,"internalType":"string","name":"symbol","type":"string"},{"indexed":false,"internalType":"uint8","name":"decimals","type":"uint8"},{"indexed":false,"internalType":"uint256","name":"maxSupply","type":"uint256"}],"name":"TokenDeployed","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"oldManager","type":"address"},{"indexed":true,"internalType":"address","name":"newManager","type":"address"}],"name":"UpgradeManagerUpdated","type":"event"},{"inputs":[],"name":"DEFAULT_ADMIN_ROLE","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"DEPLOYER_ROLE","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"tokenAddress","type":"address"}],"name":"deactivateToken","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"string","name":"name_","type":"string"},{"internalType":"string","name":"symbol_","type":"string"},{"internalType":"uint8","name":"decimals_","type":"uint8"},{"internalType":"uint256","name":"maxSupply_","type":"uint256"},{"internalType":"address","name":"admin_","type":"address"},{"internalType":"string","name":"tokenPrice_","type":"string"},{"internalType":"string","name":"bonusTiers_","type":"string"},{"internalType":"string","name":"tokenDetails_","type":"string"},{"internalType":"string","name":"tokenImageUrl_","type":"string"}],"name":"deployToken","outputs":[{"internalType":"address","name":"tokenAddress","type":"address"}],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"uint256","name":"","type":"uint256"}],"name":"deployedTokens","outputs":[{"internalType":"address","name":"","type":"address"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"","type":"address"},{"internalType":"uint256","name":"","type":"uint256"}],"name":"deployerTokens","outputs":[{"internalType":"address","name":"","type":"address"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"getActiveTokens","outputs":[{"internalType":"address[]","name":"activeTokens","type":"address[]"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"uint256","name":"offset","type":"uint256"},{"internalType":"uint256","name":"limit","type":"uint256"}],"name":"getDeployedTokens","outputs":[{"internalType":"address[]","name":"tokens","type":"address[]"},{"internalType":"bool","name":"hasMore","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"getDeployedTokensCount","outputs":[{"internalType":"uint256","name":"count","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"bytes32","name":"role","type":"bytes32"}],"name":"getRoleAdmin","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"tokenAddress","type":"address"}],"name":"getTokenInfo","outputs":[{"components":[{"internalType":"string","name":"name","type":"string"},{"internalType":"string","name":"symbol","type":"string"},{"internalType":"uint8","name":"decimals","type":"uint8"},{"internalType":"uint256","name":"maxSupply","type":"uint256"},{"internalType":"address","name":"admin","type":"address"},{"internalType":"address","name":"deployer","type":"address"},{"internalType":"uint256","name":"deploymentTime","type":"uint256"},{"internalType":"bool","name":"isActive","type":"bool"}],"internalType":"struct ModularTokenFactory.TokenInfo","name":"info","type":"tuple"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"deployer","type":"address"}],"name":"getTokensByDeployer","outputs":[{"internalType":"address[]","name":"tokens","type":"address[]"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"address","name":"account","type":"address"}],"name":"grantRole","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"address","name":"account","type":"address"}],"name":"hasRole","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"tokenAddress","type":"address"}],"name":"isDeployedToken","outputs":[{"internalType":"bool","name":"isDeployed","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"address","name":"callerConfirmation","type":"address"}],"name":"renounceRole","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"address","name":"account","type":"address"}],"name":"revokeRole","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[],"name":"securityTokenImplementation","outputs":[{"internalType":"address","name":"","type":"address"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"newUpgradeManager","type":"address"}],"name":"setUpgradeManager","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"bytes4","name":"interfaceId","type":"bytes4"}],"name":"supportsInterface","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"","type":"address"}],"name":"tokenInfo","outputs":[{"internalType":"string","name":"name","type":"string"},{"internalType":"string","name":"symbol","type":"string"},{"internalType":"uint8","name":"decimals","type":"uint8"},{"internalType":"uint256","name":"maxSupply","type":"uint256"},{"internalType":"address","name":"admin","type":"address"},{"internalType":"address","name":"deployer","type":"address"},{"internalType":"uint256","name":"deploymentTime","type":"uint256"},{"internalType":"bool","name":"isActive","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"upgradeManager","outputs":[{"internalType":"address","name":"","type":"address"}],"stateMutability":"view","type":"function"}],"bytecode":"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","deployedBytecode":"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","linkReferences":{},"deployedLinkReferences":{}}');

/***/ }),

/***/ "(rsc)/./src/contracts/SecurityToken.json":
/*!******************************************!*\
  !*** ./src/contracts/SecurityToken.json ***!
  \******************************************/
/***/ ((module) => {

"use strict";
module.exports = /*#__PURE__*/JSON.parse('{"abi":[{"inputs":[],"stateMutability":"nonpayable","type":"constructor"},{"inputs":[],"name":"AccessControlBadConfirmation","type":"error"},{"inputs":[{"internalType":"address","name":"account","type":"address"},{"internalType":"bytes32","name":"neededRole","type":"bytes32"}],"name":"AccessControlUnauthorizedAccount","type":"error"},{"inputs":[{"internalType":"address","name":"target","type":"address"}],"name":"AddressEmptyCode","type":"error"},{"inputs":[{"internalType":"address","name":"implementation","type":"address"}],"name":"ERC1967InvalidImplementation","type":"error"},{"inputs":[],"name":"ERC1967NonPayable","type":"error"},{"inputs":[{"internalType":"address","name":"spender","type":"address"},{"internalType":"uint256","name":"allowance","type":"uint256"},{"internalType":"uint256","name":"needed","type":"uint256"}],"name":"ERC20InsufficientAllowance","type":"error"},{"inputs":[{"internalType":"address","name":"sender","type":"address"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"uint256","name":"needed","type":"uint256"}],"name":"ERC20InsufficientBalance","type":"error"},{"inputs":[{"internalType":"address","name":"approver","type":"address"}],"name":"ERC20InvalidApprover","type":"error"},{"inputs":[{"internalType":"address","name":"receiver","type":"address"}],"name":"ERC20InvalidReceiver","type":"error"},{"inputs":[{"internalType":"address","name":"sender","type":"address"}],"name":"ERC20InvalidSender","type":"error"},{"inputs":[{"internalType":"address","name":"spender","type":"address"}],"name":"ERC20InvalidSpender","type":"error"},{"inputs":[],"name":"EnforcedPause","type":"error"},{"inputs":[],"name":"ExpectedPause","type":"error"},{"inputs":[],"name":"FailedCall","type":"error"},{"inputs":[],"name":"InvalidInitialization","type":"error"},{"inputs":[],"name":"NotInitializing","type":"error"},{"inputs":[],"name":"ReentrancyGuardReentrantCall","type":"error"},{"inputs":[],"name":"UUPSUnauthorizedCallContext","type":"error"},{"inputs":[{"internalType":"bytes32","name":"slot","type":"bytes32"}],"name":"UUPSUnsupportedProxiableUUID","type":"error"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"agent","type":"address"}],"name":"AgentAdded","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"agent","type":"address"}],"name":"AgentRemoved","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"account","type":"address"},{"indexed":false,"internalType":"uint256","name":"timestamp","type":"uint256"}],"name":"AgreementAccepted","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"owner","type":"address"},{"indexed":true,"internalType":"address","name":"spender","type":"address"},{"indexed":false,"internalType":"uint256","name":"value","type":"uint256"}],"name":"Approval","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"oldCompliance","type":"address"},{"indexed":true,"internalType":"address","name":"newCompliance","type":"address"}],"name":"ComplianceUpdated","type":"event"},{"anonymous":false,"inputs":[{"indexed":false,"internalType":"bool","name":"enabled","type":"bool"}],"name":"ConditionalTransfersUpdated","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"admin","type":"address"}],"name":"EmergencyPaused","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"admin","type":"address"}],"name":"EmergencyUnpaused","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"from","type":"address"},{"indexed":true,"internalType":"address","name":"to","type":"address"},{"indexed":false,"internalType":"uint256","name":"value","type":"uint256"}],"name":"ForcedTransfer","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"bytes4","name":"functionSelector","type":"bytes4"},{"indexed":true,"internalType":"address","name":"admin","type":"address"}],"name":"FunctionPaused","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"bytes4","name":"functionSelector","type":"bytes4"},{"indexed":true,"internalType":"address","name":"admin","type":"address"}],"name":"FunctionUnpaused","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"oldRegistry","type":"address"},{"indexed":true,"internalType":"address","name":"newRegistry","type":"address"}],"name":"IdentityRegistryUpdated","type":"event"},{"anonymous":false,"inputs":[{"indexed":false,"internalType":"uint64","name":"version","type":"uint64"}],"name":"Initialized","type":"event"},{"anonymous":false,"inputs":[{"indexed":false,"internalType":"uint256","name":"oldMaxSupply","type":"uint256"},{"indexed":false,"internalType":"uint256","name":"newMaxSupply","type":"uint256"}],"name":"MaxSupplyUpdated","type":"event"},{"anonymous":false,"inputs":[{"indexed":false,"internalType":"address","name":"account","type":"address"}],"name":"Paused","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"bytes32","name":"role","type":"bytes32"},{"indexed":true,"internalType":"bytes32","name":"previousAdminRole","type":"bytes32"},{"indexed":true,"internalType":"bytes32","name":"newAdminRole","type":"bytes32"}],"name":"RoleAdminChanged","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"bytes32","name":"role","type":"bytes32"},{"indexed":true,"internalType":"address","name":"account","type":"address"},{"indexed":true,"internalType":"address","name":"sender","type":"address"}],"name":"RoleGranted","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"bytes32","name":"role","type":"bytes32"},{"indexed":true,"internalType":"address","name":"account","type":"address"},{"indexed":true,"internalType":"address","name":"sender","type":"address"}],"name":"RoleRevoked","type":"event"},{"anonymous":false,"inputs":[{"indexed":false,"internalType":"string","name":"tokenImageUrl","type":"string"}],"name":"TokenImageUpdated","type":"event"},{"anonymous":false,"inputs":[{"indexed":false,"internalType":"string","name":"tokenPrice","type":"string"},{"indexed":false,"internalType":"string","name":"bonusTiers","type":"string"},{"indexed":false,"internalType":"string","name":"tokenDetails","type":"string"}],"name":"TokenMetadataUpdated","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"from","type":"address"},{"indexed":true,"internalType":"address","name":"to","type":"address"},{"indexed":false,"internalType":"uint256","name":"value","type":"uint256"}],"name":"Transfer","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"bytes32","name":"transferId","type":"bytes32"},{"indexed":true,"internalType":"address","name":"from","type":"address"},{"indexed":true,"internalType":"address","name":"to","type":"address"},{"indexed":false,"internalType":"uint256","name":"amount","type":"uint256"}],"name":"TransferApproved","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"from","type":"address"},{"indexed":true,"internalType":"address","name":"to","type":"address"},{"indexed":false,"internalType":"uint256","name":"transferAmount","type":"uint256"},{"indexed":false,"internalType":"uint256","name":"feeAmount","type":"uint256"}],"name":"TransferFeeCollected","type":"event"},{"anonymous":false,"inputs":[{"indexed":false,"internalType":"bool","name":"enabled","type":"bool"},{"indexed":false,"internalType":"uint256","name":"feePercentage","type":"uint256"},{"indexed":false,"internalType":"address","name":"feeCollector","type":"address"}],"name":"TransferFeesUpdated","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"account","type":"address"},{"indexed":false,"internalType":"bool","name":"whitelisted","type":"bool"}],"name":"TransferWhitelistAddressUpdated","type":"event"},{"anonymous":false,"inputs":[{"indexed":false,"internalType":"bool","name":"enabled","type":"bool"}],"name":"TransferWhitelistUpdated","type":"event"},{"anonymous":false,"inputs":[{"indexed":false,"internalType":"address","name":"account","type":"address"}],"name":"Unpaused","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"implementation","type":"address"}],"name":"Upgraded","type":"event"},{"inputs":[],"name":"AGENT_ROLE","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"DEFAULT_ADMIN_ROLE","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"TRANSFER_MANAGER_ROLE","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"UPGRADE_INTERFACE_VERSION","outputs":[{"internalType":"string","name":"","type":"string"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"acceptAgreement","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"agent","type":"address"}],"name":"addAgent","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"addToWhitelist","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"owner","type":"address"},{"internalType":"address","name":"spender","type":"address"}],"name":"allowance","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"spender","type":"address"},{"internalType":"uint256","name":"value","type":"uint256"}],"name":"approve","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"from","type":"address"},{"internalType":"address","name":"to","type":"address"},{"internalType":"uint256","name":"amount","type":"uint256"},{"internalType":"uint256","name":"nonce","type":"uint256"}],"name":"approveTransfer","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"balanceOf","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address[]","name":"accounts","type":"address[]"}],"name":"batchAddToWhitelist","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[],"name":"bonusTiers","outputs":[{"internalType":"string","name":"","type":"string"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"uint256","name":"value","type":"uint256"}],"name":"burn","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"},{"internalType":"uint256","name":"value","type":"uint256"}],"name":"burnFrom","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"from","type":"address"},{"internalType":"address","name":"to","type":"address"},{"internalType":"uint256","name":"amount","type":"uint256"}],"name":"canTransfer","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"compliance","outputs":[{"internalType":"address","name":"","type":"address"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"conditionalTransfersEnabled","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"decimals","outputs":[{"internalType":"uint8","name":"","type":"uint8"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"emergencyPause","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[],"name":"emergencyUnpause","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"to","type":"address"},{"internalType":"uint256","name":"amount","type":"uint256"},{"internalType":"uint256","name":"nonce","type":"uint256"}],"name":"executeApprovedTransfer","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"from","type":"address"},{"internalType":"address","name":"to","type":"address"},{"internalType":"uint256","name":"amount","type":"uint256"}],"name":"forcedTransfer","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"freezeAddress","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"uint256","name":"index","type":"uint256"}],"name":"getAgentAt","outputs":[{"internalType":"address","name":"","type":"address"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"getAgentCount","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"getAgreementAcceptanceTimestamp","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"getAllAgents","outputs":[{"internalType":"address[]","name":"","type":"address[]"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"bytes32","name":"role","type":"bytes32"}],"name":"getRoleAdmin","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"getTransferFeeConfig","outputs":[{"internalType":"uint256","name":"feePercentage","type":"uint256"},{"internalType":"address","name":"feeCollector","type":"address"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"getTransferNonce","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"address","name":"account","type":"address"}],"name":"grantRole","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"hasAcceptedAgreement","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"address","name":"account","type":"address"}],"name":"hasRole","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"identityRegistry","outputs":[{"internalType":"address","name":"","type":"address"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"string","name":"name_","type":"string"},{"internalType":"string","name":"symbol_","type":"string"},{"internalType":"uint8","name":"decimals_","type":"uint8"},{"internalType":"uint256","name":"maxSupply_","type":"uint256"},{"internalType":"address","name":"identityRegistry_","type":"address"},{"internalType":"address","name":"compliance_","type":"address"},{"internalType":"address","name":"admin_","type":"address"},{"internalType":"string","name":"tokenPrice_","type":"string"},{"internalType":"string","name":"bonusTiers_","type":"string"},{"internalType":"string","name":"tokenDetails_","type":"string"},{"internalType":"string","name":"tokenImageUrl_","type":"string"}],"name":"initialize","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"investorCountry","outputs":[{"internalType":"uint16","name":"","type":"uint16"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"isAgent","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"isEmergencyPaused","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"bytes4","name":"functionSelector","type":"bytes4"}],"name":"isFunctionPaused","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"isTransferWhitelisted","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"isVerified","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"isWhitelisted","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"maxSupply","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"to","type":"address"},{"internalType":"uint256","name":"amount","type":"uint256"}],"name":"mint","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[],"name":"name","outputs":[{"internalType":"string","name":"","type":"string"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"pause","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"bytes4","name":"functionSelector","type":"bytes4"}],"name":"pauseFunction","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[],"name":"paused","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"proxiableUUID","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"agent","type":"address"}],"name":"removeAgent","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"removeFromWhitelist","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"address","name":"callerConfirmation","type":"address"}],"name":"renounceRole","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"address","name":"account","type":"address"}],"name":"revokeRole","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"bool","name":"enabled","type":"bool"}],"name":"setConditionalTransfers","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"uint256","name":"feePercentage","type":"uint256"},{"internalType":"address","name":"feeCollector","type":"address"}],"name":"setTransferFees","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"bool","name":"enabled","type":"bool"}],"name":"setTransferWhitelist","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"},{"internalType":"bool","name":"whitelisted","type":"bool"}],"name":"setTransferWhitelistAddress","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"bytes4","name":"interfaceId","type":"bytes4"}],"name":"supportsInterface","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"symbol","outputs":[{"internalType":"string","name":"","type":"string"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"tokenDetails","outputs":[{"internalType":"string","name":"","type":"string"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"tokenImageUrl","outputs":[{"internalType":"string","name":"","type":"string"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"tokenPrice","outputs":[{"internalType":"string","name":"","type":"string"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"totalSupply","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"to","type":"address"},{"internalType":"uint256","name":"value","type":"uint256"}],"name":"transfer","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"nonpayable","type":"function"},{"inputs":[],"name":"transferFeesEnabled","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"from","type":"address"},{"internalType":"address","name":"to","type":"address"},{"internalType":"uint256","name":"value","type":"uint256"}],"name":"transferFrom","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"nonpayable","type":"function"},{"inputs":[],"name":"transferWhitelistEnabled","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"unfreezeAddress","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[],"name":"unpause","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"bytes4","name":"functionSelector","type":"bytes4"}],"name":"unpauseFunction","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"newCompliance","type":"address"}],"name":"updateCompliance","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"newIdentityRegistry","type":"address"}],"name":"updateIdentityRegistry","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"uint256","name":"newMaxSupply","type":"uint256"}],"name":"updateMaxSupply","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"string","name":"tokenImageUrl_","type":"string"}],"name":"updateTokenImageUrl","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"string","name":"tokenPrice_","type":"string"},{"internalType":"string","name":"bonusTiers_","type":"string"},{"internalType":"string","name":"tokenDetails_","type":"string"}],"name":"updateTokenMetadata","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"newImplementation","type":"address"},{"internalType":"bytes","name":"data","type":"bytes"}],"name":"upgradeToAndCall","outputs":[],"stateMutability":"payable","type":"function"},{"inputs":[],"name":"version","outputs":[{"internalType":"string","name":"","type":"string"}],"stateMutability":"pure","type":"function"}]}');

/***/ }),

/***/ "(rsc)/./src/contracts/SecurityTokenCore.json":
/*!**********************************************!*\
  !*** ./src/contracts/SecurityTokenCore.json ***!
  \**********************************************/
/***/ ((module) => {

"use strict";
module.exports = /*#__PURE__*/JSON.parse('{"_format":"hh-sol-artifact-1","contractName":"SecurityTokenCore","sourceName":"contracts/SecurityTokenCore.sol","abi":[{"inputs":[],"name":"AccessControlBadConfirmation","type":"error"},{"inputs":[{"internalType":"address","name":"account","type":"address"},{"internalType":"bytes32","name":"neededRole","type":"bytes32"}],"name":"AccessControlUnauthorizedAccount","type":"error"},{"inputs":[{"internalType":"address","name":"target","type":"address"}],"name":"AddressEmptyCode","type":"error"},{"inputs":[{"internalType":"address","name":"implementation","type":"address"}],"name":"ERC1967InvalidImplementation","type":"error"},{"inputs":[],"name":"ERC1967NonPayable","type":"error"},{"inputs":[{"internalType":"address","name":"spender","type":"address"},{"internalType":"uint256","name":"allowance","type":"uint256"},{"internalType":"uint256","name":"needed","type":"uint256"}],"name":"ERC20InsufficientAllowance","type":"error"},{"inputs":[{"internalType":"address","name":"sender","type":"address"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"uint256","name":"needed","type":"uint256"}],"name":"ERC20InsufficientBalance","type":"error"},{"inputs":[{"internalType":"address","name":"approver","type":"address"}],"name":"ERC20InvalidApprover","type":"error"},{"inputs":[{"internalType":"address","name":"receiver","type":"address"}],"name":"ERC20InvalidReceiver","type":"error"},{"inputs":[{"internalType":"address","name":"sender","type":"address"}],"name":"ERC20InvalidSender","type":"error"},{"inputs":[{"internalType":"address","name":"spender","type":"address"}],"name":"ERC20InvalidSpender","type":"error"},{"inputs":[],"name":"EnforcedPause","type":"error"},{"inputs":[],"name":"ExpectedPause","type":"error"},{"inputs":[],"name":"FailedCall","type":"error"},{"inputs":[],"name":"InvalidInitialization","type":"error"},{"inputs":[],"name":"NotInitializing","type":"error"},{"inputs":[],"name":"ReentrancyGuardReentrantCall","type":"error"},{"inputs":[],"name":"UUPSUnauthorizedCallContext","type":"error"},{"inputs":[{"internalType":"bytes32","name":"slot","type":"bytes32"}],"name":"UUPSUnsupportedProxiableUUID","type":"error"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"owner","type":"address"},{"indexed":true,"internalType":"address","name":"spender","type":"address"},{"indexed":false,"internalType":"uint256","name":"value","type":"uint256"}],"name":"Approval","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"from","type":"address"},{"indexed":true,"internalType":"address","name":"to","type":"address"},{"indexed":false,"internalType":"uint256","name":"value","type":"uint256"}],"name":"ForcedTransfer","type":"event"},{"anonymous":false,"inputs":[{"indexed":false,"internalType":"uint64","name":"version","type":"uint64"}],"name":"Initialized","type":"event"},{"anonymous":false,"inputs":[{"indexed":false,"internalType":"uint256","name":"oldMaxSupply","type":"uint256"},{"indexed":false,"internalType":"uint256","name":"newMaxSupply","type":"uint256"}],"name":"MaxSupplyUpdated","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"bytes32","name":"moduleId","type":"bytes32"},{"indexed":true,"internalType":"address","name":"moduleAddress","type":"address"}],"name":"ModuleRegistered","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"bytes32","name":"moduleId","type":"bytes32"}],"name":"ModuleUnregistered","type":"event"},{"anonymous":false,"inputs":[{"indexed":false,"internalType":"address","name":"account","type":"address"}],"name":"Paused","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"bytes32","name":"role","type":"bytes32"},{"indexed":true,"internalType":"bytes32","name":"previousAdminRole","type":"bytes32"},{"indexed":true,"internalType":"bytes32","name":"newAdminRole","type":"bytes32"}],"name":"RoleAdminChanged","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"bytes32","name":"role","type":"bytes32"},{"indexed":true,"internalType":"address","name":"account","type":"address"},{"indexed":true,"internalType":"address","name":"sender","type":"address"}],"name":"RoleGranted","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"bytes32","name":"role","type":"bytes32"},{"indexed":true,"internalType":"address","name":"account","type":"address"},{"indexed":true,"internalType":"address","name":"sender","type":"address"}],"name":"RoleRevoked","type":"event"},{"anonymous":false,"inputs":[{"indexed":false,"internalType":"string","name":"tokenPrice","type":"string"},{"indexed":false,"internalType":"string","name":"bonusTiers","type":"string"},{"indexed":false,"internalType":"string","name":"tokenDetails","type":"string"}],"name":"TokenMetadataUpdated","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"from","type":"address"},{"indexed":true,"internalType":"address","name":"to","type":"address"},{"indexed":false,"internalType":"uint256","name":"value","type":"uint256"}],"name":"Transfer","type":"event"},{"anonymous":false,"inputs":[{"indexed":false,"internalType":"address","name":"account","type":"address"}],"name":"Unpaused","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"implementation","type":"address"}],"name":"Upgraded","type":"event"},{"inputs":[],"name":"AGENT_ROLE","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"DEFAULT_ADMIN_ROLE","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"MODULE_MANAGER_ROLE","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"TRANSFER_MANAGER_ROLE","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"UPGRADE_INTERFACE_VERSION","outputs":[{"internalType":"string","name":"","type":"string"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"acceptAgreement","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"agent","type":"address"}],"name":"addAgent","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"addToWhitelist","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"owner","type":"address"},{"internalType":"address","name":"spender","type":"address"}],"name":"allowance","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"spender","type":"address"},{"internalType":"uint256","name":"value","type":"uint256"}],"name":"approve","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"user","type":"address"}],"name":"approveKYC","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"balanceOf","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"uint256","name":"value","type":"uint256"}],"name":"burn","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"},{"internalType":"uint256","name":"value","type":"uint256"}],"name":"burnFrom","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"from","type":"address"},{"internalType":"address","name":"to","type":"address"},{"internalType":"uint256","name":"amount","type":"uint256"}],"name":"canTransfer","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"uint256[]","name":"requiredClaims","type":"uint256[]"},{"internalType":"bool","name":"kycEnabled","type":"bool"},{"internalType":"bool","name":"claimsEnabled","type":"bool"}],"name":"configureTokenClaims","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[],"name":"decimals","outputs":[{"internalType":"uint8","name":"","type":"uint8"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"from","type":"address"},{"internalType":"address","name":"to","type":"address"},{"internalType":"uint256","name":"amount","type":"uint256"}],"name":"forcedTransfer","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"freezeAddress","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[],"name":"getAllAgents","outputs":[{"internalType":"address[]","name":"","type":"address[]"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"bytes32","name":"moduleId","type":"bytes32"}],"name":"getModule","outputs":[{"internalType":"address","name":"","type":"address"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"bytes32","name":"role","type":"bytes32"}],"name":"getRoleAdmin","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"getTokenMetadata","outputs":[{"internalType":"string","name":"tokenPrice","type":"string"},{"internalType":"string","name":"bonusTiers","type":"string"},{"internalType":"string","name":"tokenDetails","type":"string"},{"internalType":"string","name":"tokenImageUrl","type":"string"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"user","type":"address"}],"name":"getVerificationStatus","outputs":[{"internalType":"bool","name":"kycApproved","type":"bool"},{"internalType":"bool","name":"whitelisted","type":"bool"},{"internalType":"bool","name":"eligible","type":"bool"},{"internalType":"string","name":"method","type":"string"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"address","name":"account","type":"address"}],"name":"grantRole","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"hasAcceptedAgreement","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"address","name":"account","type":"address"}],"name":"hasRole","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"string","name":"name_","type":"string"},{"internalType":"string","name":"symbol_","type":"string"},{"internalType":"uint8","name":"decimals_","type":"uint8"},{"internalType":"uint256","name":"maxSupply_","type":"uint256"},{"internalType":"address","name":"admin_","type":"address"},{"internalType":"string","name":"tokenPrice_","type":"string"},{"internalType":"string","name":"bonusTiers_","type":"string"},{"internalType":"string","name":"tokenDetails_","type":"string"},{"internalType":"string","name":"tokenImageUrl_","type":"string"}],"name":"initialize","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"isAgent","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"moduleAddress","type":"address"}],"name":"isAuthorizedModule","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"isVerified","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"isWhitelisted","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"user","type":"address"},{"internalType":"uint256","name":"claimType","type":"uint256"},{"internalType":"bytes","name":"data","type":"bytes"},{"internalType":"string","name":"uri","type":"string"},{"internalType":"uint256","name":"expiresAt","type":"uint256"}],"name":"issueCustomClaim","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"user","type":"address"},{"internalType":"bytes","name":"data","type":"bytes"}],"name":"issueKYCClaim","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"nonpayable","type":"function"},{"inputs":[],"name":"maxSupply","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"to","type":"address"},{"internalType":"uint256","name":"amount","type":"uint256"}],"name":"mint","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[],"name":"name","outputs":[{"internalType":"string","name":"","type":"string"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"pause","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[],"name":"paused","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"proxiableUUID","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"bytes32","name":"moduleId","type":"bytes32"},{"internalType":"address","name":"moduleAddress","type":"address"}],"name":"registerModule","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"agent","type":"address"}],"name":"removeAgent","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"removeFromWhitelist","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"address","name":"callerConfirmation","type":"address"}],"name":"renounceRole","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"address","name":"account","type":"address"}],"name":"revokeRole","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"bool","name":"inProgress","type":"bool"}],"name":"setForcedTransferFlag","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"bool","name":"inProgress","type":"bool"}],"name":"setModuleTransferFlag","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"bytes4","name":"interfaceId","type":"bytes4"}],"name":"supportsInterface","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"symbol","outputs":[{"internalType":"string","name":"","type":"string"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"totalSupply","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"to","type":"address"},{"internalType":"uint256","name":"value","type":"uint256"}],"name":"transfer","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"from","type":"address"},{"internalType":"address","name":"to","type":"address"},{"internalType":"uint256","name":"value","type":"uint256"}],"name":"transferFrom","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"unfreezeAddress","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[],"name":"unpause","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"bytes32","name":"moduleId","type":"bytes32"}],"name":"unregisterModule","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"string","name":"bonusTiers_","type":"string"}],"name":"updateBonusTiers","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"uint256","name":"newMaxSupply","type":"uint256"}],"name":"updateMaxSupply","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"string","name":"tokenImageUrl_","type":"string"}],"name":"updateTokenImageUrl","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"string","name":"tokenPrice_","type":"string"},{"internalType":"string","name":"bonusTiers_","type":"string"},{"internalType":"string","name":"tokenDetails_","type":"string"}],"name":"updateTokenMetadata","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"string","name":"tokenPrice_","type":"string"}],"name":"updateTokenPrice","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"newImplementation","type":"address"},{"internalType":"bytes","name":"data","type":"bytes"}],"name":"upgradeToAndCall","outputs":[],"stateMutability":"payable","type":"function"},{"inputs":[],"name":"version","outputs":[{"internalType":"string","name":"","type":"string"}],"stateMutability":"pure","type":"function"}],"bytecode":"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","deployedBytecode":"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","linkReferences":{},"deployedLinkReferences":{}}');

/***/ }),

/***/ "(rsc)/./src/contracts/SecurityTokenFactory.json":
/*!*************************************************!*\
  !*** ./src/contracts/SecurityTokenFactory.json ***!
  \*************************************************/
/***/ ((module) => {

"use strict";
module.exports = /*#__PURE__*/JSON.parse('{"abi":[{"inputs":[{"internalType":"address","name":"admin","type":"address"}],"stateMutability":"nonpayable","type":"constructor"},{"inputs":[],"name":"AccessControlBadConfirmation","type":"error"},{"inputs":[{"internalType":"address","name":"account","type":"address"},{"internalType":"bytes32","name":"neededRole","type":"bytes32"}],"name":"AccessControlUnauthorizedAccount","type":"error"},{"inputs":[],"name":"ReentrancyGuardReentrantCall","type":"error"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"oldTokenImplementation","type":"address"},{"indexed":true,"internalType":"address","name":"newTokenImplementation","type":"address"},{"indexed":false,"internalType":"address","name":"oldWhitelistImplementation","type":"address"},{"indexed":false,"internalType":"address","name":"newWhitelistImplementation","type":"address"},{"indexed":false,"internalType":"address","name":"oldWhitelistWithKYCImplementation","type":"address"},{"indexed":false,"internalType":"address","name":"newWhitelistWithKYCImplementation","type":"address"},{"indexed":false,"internalType":"address","name":"oldComplianceImplementation","type":"address"},{"indexed":false,"internalType":"address","name":"newComplianceImplementation","type":"address"}],"name":"ImplementationsUpdated","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"bytes32","name":"role","type":"bytes32"},{"indexed":true,"internalType":"bytes32","name":"previousAdminRole","type":"bytes32"},{"indexed":true,"internalType":"bytes32","name":"newAdminRole","type":"bytes32"}],"name":"RoleAdminChanged","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"bytes32","name":"role","type":"bytes32"},{"indexed":true,"internalType":"address","name":"account","type":"address"},{"indexed":true,"internalType":"address","name":"sender","type":"address"}],"name":"RoleGranted","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"bytes32","name":"role","type":"bytes32"},{"indexed":true,"internalType":"address","name":"account","type":"address"},{"indexed":true,"internalType":"address","name":"sender","type":"address"}],"name":"RoleRevoked","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"tokenAddress","type":"address"},{"indexed":true,"internalType":"address","name":"identityRegistryAddress","type":"address"},{"indexed":true,"internalType":"address","name":"complianceAddress","type":"address"},{"indexed":false,"internalType":"string","name":"name","type":"string"},{"indexed":false,"internalType":"string","name":"symbol","type":"string"},{"indexed":false,"internalType":"uint8","name":"decimals","type":"uint8"},{"indexed":false,"internalType":"uint256","name":"maxSupply","type":"uint256"},{"indexed":false,"internalType":"address","name":"admin","type":"address"},{"indexed":false,"internalType":"bool","name":"hasKYC","type":"bool"},{"indexed":false,"internalType":"string","name":"tokenImageUrl","type":"string"}],"name":"TokenDeployed","type":"event"},{"inputs":[],"name":"DEFAULT_ADMIN_ROLE","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"DEPLOYER_ROLE","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"deployer","type":"address"}],"name":"addDeployer","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[],"name":"complianceImplementation","outputs":[{"internalType":"address","name":"","type":"address"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"string","name":"name","type":"string"},{"internalType":"string","name":"symbol","type":"string"},{"internalType":"uint8","name":"decimals","type":"uint8"},{"internalType":"uint256","name":"maxSupply","type":"uint256"},{"internalType":"address","name":"admin","type":"address"},{"internalType":"string","name":"tokenPrice","type":"string"},{"internalType":"string","name":"bonusTiers","type":"string"},{"internalType":"string","name":"tokenDetails","type":"string"},{"internalType":"string","name":"tokenImageUrl","type":"string"}],"name":"deploySecurityToken","outputs":[{"internalType":"address","name":"tokenAddress","type":"address"},{"internalType":"address","name":"identityRegistryAddress","type":"address"},{"internalType":"address","name":"complianceAddress","type":"address"}],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"string","name":"name","type":"string"},{"internalType":"string","name":"symbol","type":"string"},{"internalType":"uint8","name":"decimals","type":"uint8"},{"internalType":"uint256","name":"maxSupply","type":"uint256"},{"internalType":"address","name":"admin","type":"address"},{"internalType":"string","name":"tokenPrice","type":"string"},{"internalType":"string","name":"bonusTiers","type":"string"},{"internalType":"string","name":"tokenDetails","type":"string"},{"internalType":"string","name":"tokenImageUrl","type":"string"},{"internalType":"bool","name":"withKYC","type":"bool"}],"name":"deploySecurityTokenWithOptions","outputs":[{"internalType":"address","name":"tokenAddress","type":"address"},{"internalType":"address","name":"identityRegistryAddress","type":"address"},{"internalType":"address","name":"complianceAddress","type":"address"}],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"uint256","name":"","type":"uint256"}],"name":"deployedTokens","outputs":[{"internalType":"address","name":"","type":"address"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"getAllDeployedTokens","outputs":[{"internalType":"address[]","name":"","type":"address[]"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"uint256","name":"index","type":"uint256"}],"name":"getDeployedToken","outputs":[{"internalType":"address","name":"","type":"address"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"bytes32","name":"role","type":"bytes32"}],"name":"getRoleAdmin","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"string","name":"symbol","type":"string"}],"name":"getTokenAddressBySymbol","outputs":[{"internalType":"address","name":"","type":"address"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"getTokenCount","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"address","name":"account","type":"address"}],"name":"grantRole","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"address","name":"account","type":"address"}],"name":"hasRole","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"deployer","type":"address"}],"name":"removeDeployer","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"address","name":"callerConfirmation","type":"address"}],"name":"renounceRole","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"address","name":"account","type":"address"}],"name":"revokeRole","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[],"name":"securityTokenImplementation","outputs":[{"internalType":"address","name":"","type":"address"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"bytes4","name":"interfaceId","type":"bytes4"}],"name":"supportsInterface","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"newTokenImplementation","type":"address"},{"internalType":"address","name":"newWhitelistImplementation","type":"address"},{"internalType":"address","name":"newWhitelistWithKYCImplementation","type":"address"},{"internalType":"address","name":"newComplianceImplementation","type":"address"}],"name":"updateImplementations","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[],"name":"whitelistImplementation","outputs":[{"internalType":"address","name":"","type":"address"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"whitelistWithKYCImplementation","outputs":[{"internalType":"address","name":"","type":"address"}],"stateMutability":"view","type":"function"}]}');

/***/ }),

/***/ "(rsc)/./src/lib/prisma.ts":
/*!***************************!*\
  !*** ./src/lib/prisma.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient({\n    log:  true ? [\n        'query',\n        'error',\n        'warn'\n    ] : 0\n});\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3ByaXNtYS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBOEM7QUFFOUMsTUFBTUMsa0JBQWtCQztBQUlqQixNQUFNQyxTQUFTRixnQkFBZ0JFLE1BQU0sSUFBSSxJQUFJSCx3REFBWUEsQ0FBQztJQUMvREksS0FBS0MsS0FBc0MsR0FBRztRQUFDO1FBQVM7UUFBUztLQUFPLEdBQUcsQ0FBUztBQUN0RixHQUFHO0FBRUgsSUFBSUEsSUFBcUMsRUFBRUosZ0JBQWdCRSxNQUFNLEdBQUdBIiwic291cmNlcyI6WyJEOlxcZ2l0aHViXFx0b2tlbmRldi1uZXdyb29cXGFkbWluLXBhbmVsXFxzcmNcXGxpYlxccHJpc21hLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFByaXNtYUNsaWVudCB9IGZyb20gJ0BwcmlzbWEvY2xpZW50JztcclxuXHJcbmNvbnN0IGdsb2JhbEZvclByaXNtYSA9IGdsb2JhbFRoaXMgYXMgdW5rbm93biBhcyB7XHJcbiAgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWQ7XHJcbn07XHJcblxyXG5leHBvcnQgY29uc3QgcHJpc21hID0gZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA/PyBuZXcgUHJpc21hQ2xpZW50KHtcclxuICBsb2c6IHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAnZGV2ZWxvcG1lbnQnID8gWydxdWVyeScsICdlcnJvcicsICd3YXJuJ10gOiBbJ2Vycm9yJ10sXHJcbn0pO1xyXG5cclxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WICE9PSAncHJvZHVjdGlvbicpIGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPSBwcmlzbWE7XHJcbiJdLCJuYW1lcyI6WyJQcmlzbWFDbGllbnQiLCJnbG9iYWxGb3JQcmlzbWEiLCJnbG9iYWxUaGlzIiwicHJpc21hIiwibG9nIiwicHJvY2VzcyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/prisma.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/ethers","vendor-chunks/@noble","vendor-chunks/@adraffy"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftokens%2Froute&page=%2Fapi%2Ftokens%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftokens%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();