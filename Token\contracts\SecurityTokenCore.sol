// SPDX-License-Identifier: MIT
pragma solidity ^0.8.22;

import "@openzeppelin/contracts-upgradeable/token/ERC20/ERC20Upgradeable.sol";
import "@openzeppelin/contracts-upgradeable/token/ERC20/extensions/ERC20BurnableUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/utils/PausableUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/access/AccessControlUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol";
import "./interfaces/IModularToken.sol";

/**
 * @title SecurityTokenCore
 * @dev Core ERC20 token with modular architecture for ERC-3643 compliance
 * This is the lightweight core that delegates complex operations to specialized modules
 */
contract SecurityTokenCore is
    Initializable,
    ERC20Upgradeable,
    ERC20BurnableUpgradeable,
    PausableUpgradeable,
    AccessControlUpgradeable,
    UUPSUpgradeable,
    ReentrancyGuardUpgradeable
{
    // ============================================================================
    // CONSTANTS & ROLES
    // ============================================================================
    
    bytes32 public constant AGENT_ROLE = keccak256("AGENT_ROLE");
    bytes32 public constant TRANSFER_MANAGER_ROLE = keccak256("TRANSFER_MANAGER_ROLE");
    bytes32 public constant MODULE_MANAGER_ROLE = keccak256("MODULE_MANAGER_ROLE");
    
    string private constant _TOKEN_VERSION = "4.0.0"; // Modular architecture version

    // ============================================================================
    // STATE VARIABLES
    // ============================================================================
    
    // Core token properties
    uint256 private _maxSupply;
    uint8 private _decimals;
    
    // Token metadata
    string private _tokenPrice;
    string private _bonusTiers;
    string private _tokenDetails;
    string private _tokenImageUrl;
    
    // Module registry
    mapping(bytes32 => address) private _modules;
    mapping(address => bool) private _authorizedModules;
    
    // Transfer state flags (for module coordination)
    bool private _forcedTransferInProgress;
    bool private _moduleTransferInProgress;
    
    // ============================================================================
    // EVENTS
    // ============================================================================
    
    event ModuleRegistered(bytes32 indexed moduleId, address indexed moduleAddress);
    event ModuleUnregistered(bytes32 indexed moduleId);
    event TokenMetadataUpdated(string tokenPrice, string bonusTiers, string tokenDetails);
    event MaxSupplyUpdated(uint256 oldMaxSupply, uint256 newMaxSupply);
    event ForcedTransfer(address indexed from, address indexed to, uint256 value);

    // ============================================================================
    // MODIFIERS
    // ============================================================================
    
    modifier onlyAuthorizedModule() {
        require(_authorizedModules[msg.sender], "SecurityTokenCore: caller is not an authorized module");
        _;
    }
    
    modifier onlyAgentOrModule() {
        require(
            hasRole(AGENT_ROLE, msg.sender) || _authorizedModules[msg.sender],
            "SecurityTokenCore: caller is not an agent or authorized module"
        );
        _;
    }

    // ============================================================================
    // INITIALIZATION
    // ============================================================================
    
    /**
     * @dev Initialize the core token with basic parameters
     * @param name_ The name of the token
     * @param symbol_ The symbol of the token
     * @param decimals_ The number of decimals for the token (0-18)
     * @param maxSupply_ The maximum supply of the token
     * @param admin_ The address to be granted DEFAULT_ADMIN_ROLE
     * @param tokenPrice_ Optional token price metadata
     * @param bonusTiers_ Optional bonus tiers metadata
     * @param tokenDetails_ Optional additional token details
     * @param tokenImageUrl_ Optional token image/logo URL
     */
    function initialize(
        string memory name_,
        string memory symbol_,
        uint8 decimals_,
        uint256 maxSupply_,
        address admin_,
        string memory tokenPrice_,
        string memory bonusTiers_,
        string memory tokenDetails_,
        string memory tokenImageUrl_
    ) public initializer {
        __ERC20_init(name_, symbol_);
        __ERC20Burnable_init();
        __Pausable_init();
        __AccessControl_init();
        __UUPSUpgradeable_init();
        __ReentrancyGuard_init();

        require(admin_ != address(0), "SecurityTokenCore: admin cannot be zero address");
        require(maxSupply_ > 0, "SecurityTokenCore: max supply must be positive");
        require(decimals_ <= 18, "SecurityTokenCore: decimals must be 18 or less");

        _maxSupply = maxSupply_;
        _decimals = decimals_;
        _tokenPrice = tokenPrice_;
        _bonusTiers = bonusTiers_;
        _tokenDetails = tokenDetails_;
        _tokenImageUrl = tokenImageUrl_;

        _setupRoles(admin_);

        emit TokenMetadataUpdated(tokenPrice_, bonusTiers_, tokenDetails_);
        emit MaxSupplyUpdated(0, maxSupply_);
    }

    /**
     * @dev Setup initial roles for the admin
     * @param admin_ The address to grant roles to
     */
    function _setupRoles(address admin_) private {
        _grantRole(DEFAULT_ADMIN_ROLE, admin_);
        _grantRole(AGENT_ROLE, admin_);
        _grantRole(TRANSFER_MANAGER_ROLE, admin_);
        _grantRole(MODULE_MANAGER_ROLE, admin_);
    }

    // ============================================================================
    // MODULE MANAGEMENT
    // ============================================================================
    
    /**
     * @dev Register a module with the token
     * @param moduleId The identifier for the module
     * @param moduleAddress The address of the module contract
     */
    function registerModule(bytes32 moduleId, address moduleAddress) 
        external 
        onlyRole(MODULE_MANAGER_ROLE) 
    {
        require(moduleAddress != address(0), "SecurityTokenCore: module address cannot be zero");
        require(_modules[moduleId] == address(0), "SecurityTokenCore: module already registered");
        
        _modules[moduleId] = moduleAddress;
        _authorizedModules[moduleAddress] = true;
        
        emit ModuleRegistered(moduleId, moduleAddress);
    }
    
    /**
     * @dev Unregister a module from the token
     * @param moduleId The identifier for the module
     */
    function unregisterModule(bytes32 moduleId) external onlyRole(MODULE_MANAGER_ROLE) {
        address moduleAddress = _modules[moduleId];
        require(moduleAddress != address(0), "SecurityTokenCore: module not registered");
        
        delete _modules[moduleId];
        delete _authorizedModules[moduleAddress];
        
        emit ModuleUnregistered(moduleId);
    }
    
    /**
     * @dev Get the address of a registered module
     * @param moduleId The identifier for the module
     * @return address The address of the module contract
     */
    function getModule(bytes32 moduleId) external view returns (address) {
        return _modules[moduleId];
    }
    
    /**
     * @dev Check if an address is an authorized module
     * @param moduleAddress The address to check
     * @return bool True if the address is an authorized module
     */
    function isAuthorizedModule(address moduleAddress) external view returns (bool) {
        return _authorizedModules[moduleAddress];
    }

    // ============================================================================
    // CORE TOKEN FUNCTIONS
    // ============================================================================
    
    /**
     * @dev Returns the number of decimals used to get its user representation
     */
    function decimals() public view override returns (uint8) {
        return _decimals;
    }
    
    /**
     * @dev Returns the maximum supply of tokens
     */
    function maxSupply() external view returns (uint256) {
        return _maxSupply;
    }
    
    /**
     * @dev Returns the token version
     */
    function version() external pure returns (string memory) {
        return _TOKEN_VERSION;
    }
    
    /**
     * @dev Returns token metadata
     */
    function getTokenMetadata() external view returns (
        string memory tokenPrice,
        string memory bonusTiers,
        string memory tokenDetails,
        string memory tokenImageUrl
    ) {
        return (_tokenPrice, _bonusTiers, _tokenDetails, _tokenImageUrl);
    }

    // ============================================================================
    // MINTING FUNCTIONS
    // ============================================================================
    
    /**
     * @dev Mints new tokens to a specified address
     * @param to The address to receive the minted tokens
     * @param amount The amount of tokens to mint
     */
    function mint(address to, uint256 amount) external onlyAgentOrModule nonReentrant {
        require(to != address(0), "SecurityTokenCore: mint to the zero address");
        require(amount > 0, "SecurityTokenCore: mint amount must be positive");
        require(totalSupply() + amount <= _maxSupply, "SecurityTokenCore: exceeds max supply");

        // Check KYC Claims module first, then fall back to IdentityManager
        if (!_checkKYCClaimsModule(to)) {
            // Fall back to IdentityManager module
            _requireModuleCheck(ModuleIds.IDENTITY_MANAGER,
                abi.encodeWithSelector(IIdentityManager.isVerified.selector, to),
                "SecurityTokenCore: recipient not verified"
            );

            _requireModuleCheck(ModuleIds.IDENTITY_MANAGER,
                abi.encodeWithSelector(IIdentityManager.isWhitelisted.selector, to),
                "SecurityTokenCore: recipient not whitelisted"
            );
        }

        _mint(to, amount);
    }

    // ============================================================================
    // ADMIN FUNCTIONS
    // ============================================================================

    /**
     * @dev Update token metadata
     * @param tokenPrice_ New token price metadata
     * @param bonusTiers_ New bonus tiers metadata
     * @param tokenDetails_ New token details
     */
    function updateTokenMetadata(
        string memory tokenPrice_,
        string memory bonusTiers_,
        string memory tokenDetails_
    ) external onlyRole(DEFAULT_ADMIN_ROLE) {
        _tokenPrice = tokenPrice_;
        _bonusTiers = bonusTiers_;
        _tokenDetails = tokenDetails_;

        emit TokenMetadataUpdated(tokenPrice_, bonusTiers_, tokenDetails_);
    }

    /**
     * @dev Update token image URL
     * @param tokenImageUrl_ New token image URL
     */
    function updateTokenImageUrl(string memory tokenImageUrl_) external onlyRole(DEFAULT_ADMIN_ROLE) {
        _tokenImageUrl = tokenImageUrl_;
        // Note: Could add TokenImageUpdated event if needed
    }

    /**
     * @dev Update max supply
     * @param newMaxSupply New maximum supply
     */
    function updateMaxSupply(uint256 newMaxSupply) external onlyRole(DEFAULT_ADMIN_ROLE) {
        require(newMaxSupply > 0, "SecurityTokenCore: max supply must be positive");
        require(newMaxSupply >= totalSupply(), "SecurityTokenCore: new max supply below current total supply");

        emit MaxSupplyUpdated(_maxSupply, newMaxSupply);
        _maxSupply = newMaxSupply;
    }

    /**
     * @dev Update only token price
     * @param tokenPrice_ New token price metadata
     */
    function updateTokenPrice(string memory tokenPrice_) external onlyRole(DEFAULT_ADMIN_ROLE) {
        _tokenPrice = tokenPrice_;
        emit TokenMetadataUpdated(tokenPrice_, _bonusTiers, _tokenDetails);
    }

    /**
     * @dev Update only bonus tiers
     * @param bonusTiers_ New bonus tiers metadata
     */
    function updateBonusTiers(string memory bonusTiers_) external onlyRole(DEFAULT_ADMIN_ROLE) {
        _bonusTiers = bonusTiers_;
        emit TokenMetadataUpdated(_tokenPrice, bonusTiers_, _tokenDetails);
    }

    // ============================================================================
    // PAUSE FUNCTIONS
    // ============================================================================

    /**
     * @dev Pauses all token transfers
     */
    function pause() external onlyRole(DEFAULT_ADMIN_ROLE) {
        _pause();
    }

    /**
     * @dev Unpauses all token transfers
     */
    function unpause() external onlyRole(DEFAULT_ADMIN_ROLE) {
        _unpause();
    }

    // ============================================================================
    // TRANSFER FUNCTIONS
    // ============================================================================

    /**
     * @dev Check if a transfer is valid according to all compliance modules
     * @param from Source address
     * @param to Destination address
     * @param amount Amount of tokens to transfer
     * @return bool True if the transfer is valid
     */
    function canTransfer(address from, address to, uint256 amount) external view returns (bool) {
        if (paused()) {
            return false;
        }

        if (amount == 0 || from == address(0) || to == address(0)) {
            return false;
        }

        if (balanceOf(from) < amount) {
            return false;
        }

        // Check with IdentityManager
        if (!_moduleCheck(ModuleIds.IDENTITY_MANAGER,
            abi.encodeWithSelector(IIdentityManager.canTransfer.selector, from, to))) {
            return false;
        }

        // Check with ComplianceEngine
        if (!_moduleCheck(ModuleIds.COMPLIANCE_ENGINE,
            abi.encodeWithSelector(IComplianceEngine.canTransfer.selector, from, to, amount))) {
            return false;
        }

        return true;
    }

    /**
     * @dev Force transfer tokens (delegated to TransferController)
     * @param from The address to transfer tokens from
     * @param to The address to transfer tokens to
     * @param amount The amount of tokens to transfer
     * @return bool True if the transfer was successful
     */
    function forcedTransfer(address from, address to, uint256 amount)
        external
        onlyRole(TRANSFER_MANAGER_ROLE)
        returns (bool)
    {
        address transferController = _modules[ModuleIds.TRANSFER_CONTROLLER];
        require(transferController != address(0), "SecurityTokenCore: TransferController not registered");

        // Delegate to TransferController module
        (bool success, bytes memory result) = transferController.call(
            abi.encodeWithSelector(ITransferController.forcedTransfer.selector, from, to, amount)
        );

        require(success, "SecurityTokenCore: forced transfer failed");
        return abi.decode(result, (bool));
    }

    // ============================================================================
    // TRANSFER HOOKS
    // ============================================================================

    /**
     * @dev Hook that is called before any transfer of tokens
     * This includes minting and burning
     */
    function _update(address from, address to, uint256 amount) internal override whenNotPaused {
        // Skip compliance checks for minting and burning
        if (from != address(0) && to != address(0)) {
            // Only perform checks if not in a forced transfer or module transfer
            if (!_forcedTransferInProgress && !_moduleTransferInProgress) {
                // Delegate compliance checks to modules
                _requireModuleCheck(ModuleIds.IDENTITY_MANAGER,
                    abi.encodeWithSelector(IIdentityManager.canTransfer.selector, from, to),
                    "SecurityTokenCore: identity check failed"
                );

                _requireModuleCheck(ModuleIds.COMPLIANCE_ENGINE,
                    abi.encodeWithSelector(IComplianceEngine.canTransfer.selector, from, to, amount),
                    "SecurityTokenCore: compliance check failed"
                );
            }
        } else if (to != address(0)) {
            // For minting, check recipient with KYC Claims module first
            if (!_checkKYCClaimsModule(to)) {
                // Fall back to IdentityManager module
                _requireModuleCheck(ModuleIds.IDENTITY_MANAGER,
                    abi.encodeWithSelector(IIdentityManager.isVerified.selector, to),
                    "SecurityTokenCore: recipient not verified"
                );

                _requireModuleCheck(ModuleIds.IDENTITY_MANAGER,
                    abi.encodeWithSelector(IIdentityManager.isWhitelisted.selector, to),
                    "SecurityTokenCore: recipient not whitelisted"
                );
            }
        }

        super._update(from, to, amount);

        // Notify modules of completed transfer
        if (from != address(0) && to != address(0)) {
            _notifyModuleTransfer(ModuleIds.COMPLIANCE_ENGINE, from, to, amount);
        } else if (from != address(0)) {
            // Notify of burn
            _notifyModuleBurn(ModuleIds.COMPLIANCE_ENGINE, from, amount);
        }
    }

    /**
     * @dev Set forced transfer flag (called by TransferController)
     */
    function setForcedTransferFlag(bool inProgress) external onlyAuthorizedModule {
        _forcedTransferInProgress = inProgress;
    }

    /**
     * @dev Set module transfer flag (for internal module operations)
     */
    function setModuleTransferFlag(bool inProgress) external onlyAuthorizedModule {
        _moduleTransferInProgress = inProgress;
    }

    // ============================================================================
    // INTERNAL FUNCTIONS
    // ============================================================================

    /**
     * @dev Helper function to make calls to modules and validate responses
     */
    function _requireModuleCheck(bytes32 moduleId, bytes memory callData, string memory errorMessage) private view {
        address moduleAddress = _modules[moduleId];
        if (moduleAddress != address(0)) {
            (bool success, bytes memory result) = moduleAddress.staticcall(callData);
            require(success && abi.decode(result, (bool)), errorMessage);
        }
    }

    /**
     * @dev Helper function to make calls to modules and return boolean result
     */
    function _moduleCheck(bytes32 moduleId, bytes memory callData) private view returns (bool) {
        address moduleAddress = _modules[moduleId];
        if (moduleAddress == address(0)) {
            return true; // If module not registered, allow by default
        }

        (bool success, bytes memory result) = moduleAddress.staticcall(callData);
        return success && abi.decode(result, (bool));
    }

    /**
     * @dev Helper function to check KYC Claims module eligibility
     */
    function _checkKYCClaimsModule(address user) private view returns (bool) {
        address kycModule = _modules[ModuleIds.KYC_CLAIMS_MODULE];
        if (kycModule == address(0)) {
            return false; // Module not registered, fall back to other checks
        }

        try IKYCClaimsModule(kycModule).isEligible(address(this), user) returns (bool eligible) {
            return eligible;
        } catch {
            return false; // If module call fails, fall back to other checks
        }
    }

    /**
     * @dev Notify module of transfer completion
     */
    function _notifyModuleTransfer(bytes32 moduleId, address from, address to, uint256 amount) private {
        address moduleAddress = _modules[moduleId];
        if (moduleAddress != address(0)) {
            // Ignore return value for notification calls
            (bool success, ) = moduleAddress.call(
                abi.encodeWithSelector(IComplianceEngine.transferred.selector, from, to, amount)
            );
            // Don't revert on notification failures to avoid blocking transfers
            if (!success) {
                // Could emit an event here for monitoring
            }
        }
    }

    /**
     * @dev Notify module of burn completion
     */
    function _notifyModuleBurn(bytes32 moduleId, address from, uint256 amount) private {
        address moduleAddress = _modules[moduleId];
        if (moduleAddress != address(0)) {
            // Ignore return value for notification calls
            (bool success, ) = moduleAddress.call(
                abi.encodeWithSelector(IComplianceEngine.destroyed.selector, from, amount)
            );
            // Don't revert on notification failures to avoid blocking burns
            if (!success) {
                // Could emit an event here for monitoring
            }
        }
    }

    // ============================================================================
    // DELEGATION FUNCTIONS (for backward compatibility)
    // ============================================================================

    /**
     * @dev Delegate whitelist functions to IdentityManager
     */
    function addToWhitelist(address account) external {
        _delegateToModule(ModuleIds.IDENTITY_MANAGER,
            abi.encodeWithSelector(IIdentityManager.addToWhitelist.selector, account));
    }

    function removeFromWhitelist(address account) external {
        _delegateToModule(ModuleIds.IDENTITY_MANAGER,
            abi.encodeWithSelector(IIdentityManager.removeFromWhitelist.selector, account));
    }

    function isWhitelisted(address account) external view returns (bool) {
        return _moduleCheck(ModuleIds.IDENTITY_MANAGER,
            abi.encodeWithSelector(IIdentityManager.isWhitelisted.selector, account));
    }

    function isVerified(address account) external view returns (bool) {
        return _moduleCheck(ModuleIds.IDENTITY_MANAGER,
            abi.encodeWithSelector(IIdentityManager.isVerified.selector, account));
    }

    function freezeAddress(address account) external {
        _delegateToModule(ModuleIds.IDENTITY_MANAGER,
            abi.encodeWithSelector(IIdentityManager.freezeAddress.selector, account));
    }

    function unfreezeAddress(address account) external {
        _delegateToModule(ModuleIds.IDENTITY_MANAGER,
            abi.encodeWithSelector(IIdentityManager.unfreezeAddress.selector, account));
    }

    /**
     * @dev Delegate agent functions to AgentManager
     */
    function addAgent(address agent) external {
        _delegateToModule(ModuleIds.AGENT_MANAGER,
            abi.encodeWithSelector(IAgentManager.addAgent.selector, agent));
    }

    function removeAgent(address agent) external {
        _delegateToModule(ModuleIds.AGENT_MANAGER,
            abi.encodeWithSelector(IAgentManager.removeAgent.selector, agent));
    }

    function isAgent(address account) external view returns (bool) {
        return _moduleCheck(ModuleIds.AGENT_MANAGER,
            abi.encodeWithSelector(IAgentManager.isAgent.selector, account));
    }

    function getAllAgents() external view returns (address[] memory) {
        address moduleAddress = _modules[ModuleIds.AGENT_MANAGER];
        if (moduleAddress == address(0)) {
            return new address[](0);
        }

        (bool success, bytes memory result) = moduleAddress.staticcall(
            abi.encodeWithSelector(IAgentManager.getAllAgents.selector)
        );

        if (success) {
            return abi.decode(result, (address[]));
        }
        return new address[](0);
    }

    /**
     * @dev Delegate agreement functions to ComplianceEngine
     */
    function acceptAgreement() external {
        _delegateToModule(ModuleIds.COMPLIANCE_ENGINE,
            abi.encodeWithSelector(IComplianceEngine.acceptAgreement.selector));
    }

    function hasAcceptedAgreement(address account) external view returns (bool) {
        return _moduleCheck(ModuleIds.COMPLIANCE_ENGINE,
            abi.encodeWithSelector(IComplianceEngine.hasAcceptedAgreement.selector, account));
    }

    /**
     * @dev Delegate KYC Claims functions to KYCClaimsModule
     */
    function approveKYC(address user) external {
        _delegateToModule(ModuleIds.KYC_CLAIMS_MODULE,
            abi.encodeWithSelector(IKYCClaimsModule.approveKYC.selector, address(this), user));
    }

    function issueKYCClaim(address user, bytes calldata data) external returns (bytes32) {
        address moduleAddress = _modules[ModuleIds.KYC_CLAIMS_MODULE];
        require(moduleAddress != address(0), "SecurityTokenCore: KYC Claims module not registered");

        (bool success, bytes memory result) = moduleAddress.call(
            abi.encodeWithSelector(IKYCClaimsModule.issueKYCClaim.selector, user, data)
        );
        require(success, "SecurityTokenCore: KYC claim issuance failed");
        return abi.decode(result, (bytes32));
    }

    function issueCustomClaim(
        address user,
        uint256 claimType,
        bytes calldata data,
        string calldata uri,
        uint256 expiresAt
    ) external returns (bytes32) {
        address moduleAddress = _modules[ModuleIds.KYC_CLAIMS_MODULE];
        require(moduleAddress != address(0), "SecurityTokenCore: KYC Claims module not registered");

        (bool success, bytes memory result) = moduleAddress.call(
            abi.encodeWithSelector(IKYCClaimsModule.issueCustomClaim.selector, user, claimType, data, uri, expiresAt)
        );
        require(success, "SecurityTokenCore: custom claim issuance failed");
        return abi.decode(result, (bytes32));
    }

    function configureTokenClaims(
        uint256[] calldata requiredClaims,
        bool kycEnabled,
        bool claimsEnabled
    ) external {
        _delegateToModule(ModuleIds.KYC_CLAIMS_MODULE,
            abi.encodeWithSelector(IKYCClaimsModule.configureTokenClaims.selector, address(this), requiredClaims, kycEnabled, claimsEnabled));
    }

    function getVerificationStatus(address user) external view returns (
        bool kycApproved,
        bool whitelisted,
        bool eligible,
        string memory method
    ) {
        address moduleAddress = _modules[ModuleIds.KYC_CLAIMS_MODULE];
        if (moduleAddress == address(0)) {
            // Fall back to IdentityManager
            bool verified = _moduleCheck(ModuleIds.IDENTITY_MANAGER,
                abi.encodeWithSelector(IIdentityManager.isVerified.selector, user));
            bool whitelistedIM = _moduleCheck(ModuleIds.IDENTITY_MANAGER,
                abi.encodeWithSelector(IIdentityManager.isWhitelisted.selector, user));
            return (verified, whitelistedIM, verified && whitelistedIM, "IDENTITY_MANAGER");
        }

        (bool success, bytes memory result) = moduleAddress.staticcall(
            abi.encodeWithSelector(IKYCClaimsModule.getVerificationStatus.selector, address(this), user)
        );

        if (success) {
            return abi.decode(result, (bool, bool, bool, string));
        }
        return (false, false, false, "ERROR");
    }

    /**
     * @dev Helper function to delegate calls to modules
     */
    function _delegateToModule(bytes32 moduleId, bytes memory callData) private {
        address moduleAddress = _modules[moduleId];
        require(moduleAddress != address(0), "SecurityTokenCore: module not registered");

        (bool success, ) = moduleAddress.call(callData);
        require(success, "SecurityTokenCore: module call failed");
    }

    /**
     * @dev Function to authorize an upgrade
     * @param newImplementation The address of the new implementation
     */
    function _authorizeUpgrade(address newImplementation)
        internal
        override
        onlyRole(DEFAULT_ADMIN_ROLE)
    {}
}
