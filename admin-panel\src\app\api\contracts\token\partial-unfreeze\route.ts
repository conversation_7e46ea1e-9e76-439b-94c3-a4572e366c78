import { ethers } from 'ethers';
import { NextRequest, NextResponse } from 'next/server';
import SecurityTokenCoreArtifact from '../../../../../contracts/SecurityTokenCore.json';

// Extract ABI from artifact
const SecurityTokenABI = SecurityTokenCoreArtifact.abi;

// Load private key from environment variable
const PRIVATE_KEY = process.env.CONTRACT_ADMIN_PRIVATE_KEY;
const RPC_URLS = {
  amoy: process.env.AMOY_RPC_URL || 'https://rpc-amoy.polygon.technology/',
  polygon: process.env.POLYGON_RPC_URL || 'https://polygon-rpc.com',
  unknown: process.env.AMOY_RPC_URL || 'https://rpc-amoy.polygon.technology/', // Default to Amoy
};

// Network chain IDs
const CHAIN_IDS = {
  amoy: 80002,
  polygon: 137,
  unknown: 80002, // Default to Amoy
};

// Vault address - this should be a controlled wallet where frozen tokens are stored
// The ideal approach would be to have a dedicated escrow contract for this purpose
// For now, we'll use a hardcoded address that should be managed by the admin
const VAULT_ADDRESSES = {
  amoy: process.env.AMOY_VAULT_ADDRESS || '******************************************', // Default to admin address
  polygon: process.env.POLYGON_VAULT_ADDRESS || '******************************************',
  unknown: process.env.AMOY_VAULT_ADDRESS || '******************************************'
};

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { 
      tokenAddress, 
      toAddress,
      amount,
      network = 'amoy' 
    } = body;
    
    if (!tokenAddress || !toAddress || !amount) {
      return NextResponse.json(
        { error: 'Token address, to address, and amount are required' },
        { status: 400 }
      );
    }
    
    if (!PRIVATE_KEY) {
      return NextResponse.json(
        {
          error: 'CONTRACT_ADMIN_PRIVATE_KEY environment variable not set',
          details: 'For security reasons, the API requires a secure method to sign transactions.',
          clientSideInstructions: true,
          message: 'The server is not configured with admin credentials.'
        },
        { status: 422 }
      );
    }
    
    // Validate addresses
    if (!ethers.isAddress(tokenAddress) || !ethers.isAddress(toAddress)) {
      return NextResponse.json(
        { error: 'Invalid address format provided' },
        { status: 400 }
      );
    }
    
    // Validate amount
    let parsedAmount;
    try {
      parsedAmount = ethers.parseUnits(amount.toString(), 18); // Assuming 18 decimals
    } catch (error) {
      return NextResponse.json(
        { error: 'Invalid amount format' },
        { status: 400 }
      );
    }
    
    // Get RPC URL for the specified network, defaulting to Amoy
    const actualNetwork = network === 'unknown' ? 'amoy' : network;
    const rpcUrl = RPC_URLS[actualNetwork as keyof typeof RPC_URLS] || RPC_URLS.amoy;
    const chainId = CHAIN_IDS[actualNetwork as keyof typeof CHAIN_IDS] || CHAIN_IDS.amoy;
    
    // Get vault address for the network
    const vaultAddress = VAULT_ADDRESSES[actualNetwork as keyof typeof VAULT_ADDRESSES] || VAULT_ADDRESSES.amoy;
    
    console.log(`Using network: ${actualNetwork}, RPC URL: ${rpcUrl}, Chain ID: ${chainId}`);
    console.log(`Vault address: ${vaultAddress}`);
    
    // Connect to the network
    const provider = new ethers.JsonRpcProvider(rpcUrl, {
      chainId,
      name: actualNetwork
    });
    
    // Ensure the network is connected
    const network_details = await provider.getNetwork();
    console.log(`Connected to network: ${network_details.name} (Chain ID: ${network_details.chainId})`);
    
    // Create wallet and connect to provider
    const wallet = new ethers.Wallet(PRIVATE_KEY, provider);
    console.log(`Wallet address: ${wallet.address}`);
    
    // Connect to the token contract
    const tokenContract = new ethers.Contract(
      tokenAddress,
      SecurityTokenABI,
      wallet
    );
    
    // Check current balance of the vault
    const vaultBalance = await tokenContract.balanceOf(vaultAddress);
    console.log(`Current vault balance: ${ethers.formatUnits(vaultBalance, 18)}`);
    
    if (vaultBalance < parsedAmount) {
      return NextResponse.json({
        success: false,
        error: `Insufficient vault balance. Vault has ${ethers.formatUnits(vaultBalance, 18)} tokens but trying to unfreeze ${amount} tokens.`
      }, { status: 400 });
    }
    
    // Execute the force transfer using adminTransfer function
    console.log(`Unfreezing ${amount} tokens from vault ${vaultAddress} to ${toAddress}...`);
    
    try {
      // Using the forcedTransfer function to move tokens from the vault to the user
      const tx = await tokenContract.forcedTransfer(vaultAddress, toAddress, parsedAmount);
      console.log(`Transaction hash: ${tx.hash}`);

      // Wait for the transaction to be mined
      const receipt = await tx.wait();
      
      return NextResponse.json({
        success: true,
        action: 'partialUnfreeze',
        txHash: tx.hash,
        blockNumber: receipt.blockNumber,
        from: vaultAddress,
        to: toAddress,
        amount: amount
      });
      
    } catch (txError: any) {
      console.error('Transaction error:', txError);
      
      // Check if the issue might be a permissions problem
      try {
        const DEFAULT_ADMIN_ROLE = await tokenContract.DEFAULT_ADMIN_ROLE();
        const hasAdminRole = await tokenContract.hasRole(DEFAULT_ADMIN_ROLE, wallet.address);
        
        if (!hasAdminRole) {
          return NextResponse.json({
            success: false,
            error: "The connected wallet doesn't have the DEFAULT_ADMIN_ROLE required for unfreezing tokens",
            details: `Please grant DEFAULT_ADMIN_ROLE to ${wallet.address} on the token contract`
          }, { status: 403 });
        }
      } catch (roleCheckError) {
        console.error('Role check error:', roleCheckError);
      }
      
      throw txError; // Re-throw to be caught by the outer catch
    }
    
  } catch (error: any) {
    console.error('Error unfreezing tokens:', error);
    return NextResponse.json(
      { error: error.message || 'An unknown error occurred' },
      { status: 500 }
    );
  }
} 