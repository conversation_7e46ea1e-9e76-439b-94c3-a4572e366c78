import { NextRequest, NextResponse } from 'next/server';
import { ethers } from 'ethers';

const SECURITY_TOKEN_CORE_ABI = [
  "function updateTokenPrice(string memory tokenPrice_) external",
  "function updateTokenMetadata(string memory tokenPrice_, string memory bonusTiers_, string memory tokenDetails_) external",
  "function getTokenMetadata() external view returns (string memory tokenPrice, string memory bonusTiers, string memory tokenDetails, string memory tokenImageUrl)",
  "function hasRole(bytes32 role, address account) external view returns (bool)"
];

export async function POST(request: NextRequest) {
  try {
    const { tokenAddress, newPrice } = await request.json();

    if (!tokenAddress || !newPrice) {
      return NextResponse.json(
        { error: 'Token address and new price are required' },
        { status: 400 }
      );
    }

    // Validate addresses
    if (!ethers.isAddress(tokenAddress)) {
      return NextResponse.json(
        { error: 'Invalid token address format' },
        { status: 400 }
      );
    }

    // Get environment variables
    const rpcUrl = process.env.AMOY_RPC_URL;
    const privateKey = process.env.CONTRACT_ADMIN_PRIVATE_KEY;

    if (!rpcUrl || !privateKey) {
      return NextResponse.json(
        { error: 'Server configuration error' },
        { status: 500 }
      );
    }

    // Setup provider and signer
    const provider = new ethers.JsonRpcProvider(rpcUrl);
    const signer = new ethers.Wallet(privateKey, provider);

    // Get token contract
    const tokenContract = new ethers.Contract(tokenAddress, SECURITY_TOKEN_CORE_ABI, signer);

    // Check if the API signer has admin role
    const DEFAULT_ADMIN_ROLE = '0x0000000000000000000000000000000000000000000000000000000000000000';
    const signerAddress = await signer.getAddress();
    const hasAdminRole = await tokenContract.hasRole(DEFAULT_ADMIN_ROLE, signerAddress);

    console.log('API Signer address:', signerAddress);
    console.log('Has DEFAULT_ADMIN_ROLE:', hasAdminRole);

    if (!hasAdminRole) {
      return NextResponse.json(
        {
          error: `API signer (${signerAddress}) does not have DEFAULT_ADMIN_ROLE on token ${tokenAddress}. Contact the token admin to grant this role.`,
          signerAddress,
          tokenAddress,
          hasAdminRole: false
        },
        { status: 403 }
      );
    }

    // Get current metadata for comparison
    const currentMetadata = await tokenContract.getTokenMetadata();
    console.log('Current price:', currentMetadata[0]);
    console.log('Current bonus tiers:', currentMetadata[1]);
    console.log('Current details:', currentMetadata[2]);
    console.log('New price:', newPrice);

    // Update token price using updateTokenMetadata for compatibility
    const tx = await tokenContract.updateTokenMetadata(
      newPrice,
      currentMetadata[1], // preserve bonus tiers
      currentMetadata[2]  // preserve details
    );
    await tx.wait();

    // Verify update
    const updatedMetadata = await tokenContract.getTokenMetadata();

    console.log('Price updated successfully:', tx.hash);

    return NextResponse.json({
      success: true,
      message: 'Token price updated successfully',
      txHash: tx.hash,
      oldPrice: currentMetadata[0],
      newPrice: updatedMetadata[0],
      tokenAddress
    });

  } catch (error: any) {
    console.error('Error updating token price:', error);
    return NextResponse.json(
      { error: `Failed to update token price: ${error.message}` },
      { status: 500 }
    );
  }
}
