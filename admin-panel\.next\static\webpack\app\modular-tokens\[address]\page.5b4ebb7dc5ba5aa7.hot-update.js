"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/modular-tokens/[address]/page",{

/***/ "(app-pages-browser)/./src/hooks/useModularToken.ts":
/*!**************************************!*\
  !*** ./src/hooks/useModularToken.ts ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useModularToken: () => (/* binding */ useModularToken)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/providers/provider-browser.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/contract/contract.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/utils/units.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/crypto/keccak.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/utils/utf8.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/constants/addresses.js\");\n/* harmony import */ var _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/contracts/SecurityTokenCore.json */ \"(app-pages-browser)/./src/contracts/SecurityTokenCore.json\");\n/* harmony import */ var _contracts_UpgradeManager_json__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contracts/UpgradeManager.json */ \"(app-pages-browser)/./src/contracts/UpgradeManager.json\");\n/* __next_internal_client_entry_do_not_use__ useModularToken auto */ \n\n// Import ABIs - extract the abi property from the JSON artifacts\n\n\n// Extract ABIs from artifacts\nconst SecurityTokenCoreABI = _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_1__.abi;\nconst UpgradeManagerABI = _contracts_UpgradeManager_json__WEBPACK_IMPORTED_MODULE_2__.abi;\n// Contract addresses from environment\nconst SECURITY_TOKEN_CORE_ADDRESS = \"******************************************\";\nconst UPGRADE_MANAGER_ADDRESS = \"0xb7a53dC340C2E5e823002B174629e2a44B8ed815\";\nfunction useModularToken(tokenAddress) {\n    const [provider, setProvider] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [signer, setSigner] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [tokenInfo, setTokenInfo] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [contractAddresses, setContractAddresses] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [userRoles, setUserRoles] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [upgradeInfo, setUpgradeInfo] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [pendingUpgrades, setPendingUpgrades] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [upgradeHistory, setUpgradeHistory] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    // Initialize provider and signer\n    const initializeProvider = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[initializeProvider]\": async ()=>{\n            try {\n                if ( true && window.ethereum) {\n                    const provider = new ethers__WEBPACK_IMPORTED_MODULE_3__.BrowserProvider(window.ethereum);\n                    await provider.send('eth_requestAccounts', []);\n                    const signer = await provider.getSigner();\n                    setProvider(provider);\n                    setSigner(signer);\n                    return {\n                        provider,\n                        signer\n                    };\n                } else {\n                    throw new Error('MetaMask not found');\n                }\n            } catch (error) {\n                console.error('Error initializing provider:', error);\n                setError('Failed to connect to wallet');\n                return null;\n            }\n        }\n    }[\"useModularToken.useCallback[initializeProvider]\"], []);\n    // Load token information\n    const loadTokenInfo = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[loadTokenInfo]\": async ()=>{\n            const contractAddress = tokenAddress || SECURITY_TOKEN_CORE_ADDRESS;\n            if (!provider || !contractAddress) return;\n            try {\n                setLoading(true);\n                const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(contractAddress, SecurityTokenCoreABI, provider);\n                const [name, symbol, version, totalSupply, maxSupply, decimals, paused, metadata] = await Promise.all([\n                    contract.name(),\n                    contract.symbol(),\n                    contract.version(),\n                    contract.totalSupply(),\n                    contract.maxSupply(),\n                    contract.decimals(),\n                    contract.paused(),\n                    contract.getTokenMetadata()\n                ]);\n                console.log('Token contract data:');\n                console.log('- Name:', name);\n                console.log('- Symbol:', symbol);\n                console.log('- Decimals (raw):', decimals);\n                console.log('- Decimals (number):', Number(decimals));\n                console.log('- Total Supply (raw):', totalSupply.toString());\n                console.log('- Max Supply (raw):', maxSupply.toString());\n                console.log('- Metadata (raw):', metadata);\n                console.log('- Token Price:', metadata[0]);\n                console.log('- Bonus Tiers:', metadata[1]);\n                console.log('- Token Details:', metadata[2]);\n                console.log('- Token Image URL:', metadata[3]);\n                const decimalsNumber = Number(decimals);\n                // Format with proper blockchain decimal precision\n                let formattedTotalSupply;\n                let formattedMaxSupply;\n                if (decimalsNumber === 0) {\n                    // For 0 decimals, show as whole numbers\n                    formattedTotalSupply = totalSupply.toString();\n                    formattedMaxSupply = maxSupply.toString();\n                } else {\n                    // For tokens with decimals, use ethers.formatUnits and preserve decimal precision\n                    const totalSupplyFormatted = ethers__WEBPACK_IMPORTED_MODULE_5__.formatUnits(totalSupply, decimalsNumber);\n                    const maxSupplyFormatted = ethers__WEBPACK_IMPORTED_MODULE_5__.formatUnits(maxSupply, decimalsNumber);\n                    // For blockchain tokens, show with appropriate decimal places\n                    // Remove excessive trailing zeros but keep meaningful precision\n                    formattedTotalSupply = parseFloat(totalSupplyFormatted).toFixed(Math.min(decimalsNumber, 6));\n                    formattedMaxSupply = parseFloat(maxSupplyFormatted).toFixed(Math.min(decimalsNumber, 6));\n                    // Remove trailing zeros after decimal point\n                    formattedTotalSupply = formattedTotalSupply.replace(/\\.?0+$/, '');\n                    formattedMaxSupply = formattedMaxSupply.replace(/\\.?0+$/, '');\n                    // Ensure at least .0 for tokens with decimals\n                    if (!formattedTotalSupply.includes('.')) formattedTotalSupply += '.0';\n                    if (!formattedMaxSupply.includes('.')) formattedMaxSupply += '.0';\n                }\n                console.log('- Total Supply (formatted):', formattedTotalSupply);\n                console.log('- Max Supply (formatted):', formattedMaxSupply);\n                setTokenInfo({\n                    name,\n                    symbol,\n                    version,\n                    totalSupply: formattedTotalSupply,\n                    maxSupply: formattedMaxSupply,\n                    decimals: decimalsNumber,\n                    paused,\n                    metadata: {\n                        tokenPrice: metadata[0],\n                        bonusTiers: metadata[1],\n                        tokenDetails: metadata[2],\n                        tokenImageUrl: metadata[3]\n                    }\n                });\n            } catch (error) {\n                console.error('Error loading token info:', error);\n                setError('Failed to load token information');\n            } finally{\n                setLoading(false);\n            }\n        }\n    }[\"useModularToken.useCallback[loadTokenInfo]\"], [\n        provider,\n        tokenAddress\n    ]);\n    // Load contract addresses\n    const loadContractAddresses = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[loadContractAddresses]\": async ()=>{\n            const contractAddress = tokenAddress || SECURITY_TOKEN_CORE_ADDRESS;\n            if (!provider || !contractAddress) return;\n            try {\n                const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(contractAddress, SecurityTokenCoreABI, provider);\n                // Define module IDs (these are keccak256 hashes of the module names)\n                const moduleIds = {\n                    'Identity Manager': ethers__WEBPACK_IMPORTED_MODULE_6__.keccak256(ethers__WEBPACK_IMPORTED_MODULE_7__.toUtf8Bytes(\"IDENTITY_MANAGER\")),\n                    'Compliance Engine': ethers__WEBPACK_IMPORTED_MODULE_6__.keccak256(ethers__WEBPACK_IMPORTED_MODULE_7__.toUtf8Bytes(\"COMPLIANCE_ENGINE\")),\n                    'Transfer Controller': ethers__WEBPACK_IMPORTED_MODULE_6__.keccak256(ethers__WEBPACK_IMPORTED_MODULE_7__.toUtf8Bytes(\"TRANSFER_CONTROLLER\")),\n                    'Agent Manager': ethers__WEBPACK_IMPORTED_MODULE_6__.keccak256(ethers__WEBPACK_IMPORTED_MODULE_7__.toUtf8Bytes(\"AGENT_MANAGER\")),\n                    'Emergency Manager': ethers__WEBPACK_IMPORTED_MODULE_6__.keccak256(ethers__WEBPACK_IMPORTED_MODULE_7__.toUtf8Bytes(\"EMERGENCY_MANAGER\")),\n                    'KYC Claims Module': ethers__WEBPACK_IMPORTED_MODULE_6__.keccak256(ethers__WEBPACK_IMPORTED_MODULE_7__.toUtf8Bytes(\"KYC_CLAIMS_MODULE\"))\n                };\n                console.log('Fetching module addresses...');\n                // Fetch all module addresses\n                const addresses = {\n                    'Token Contract': contractAddress\n                };\n                for (const [moduleName, moduleId] of Object.entries(moduleIds)){\n                    try {\n                        const moduleAddress = await contract.getModule(moduleId);\n                        if (moduleAddress && moduleAddress !== ethers__WEBPACK_IMPORTED_MODULE_8__.ZeroAddress) {\n                            addresses[moduleName] = moduleAddress;\n                            console.log(\"\".concat(moduleName, \": \").concat(moduleAddress));\n                        } else {\n                            console.log(\"\".concat(moduleName, \": Not registered\"));\n                        }\n                    } catch (error) {\n                        console.warn(\"Error fetching \".concat(moduleName, \" address:\"), error);\n                    }\n                }\n                setContractAddresses(addresses);\n            } catch (error) {\n                console.error('Error loading contract addresses:', error);\n            }\n        }\n    }[\"useModularToken.useCallback[loadContractAddresses]\"], [\n        signer,\n        tokenAddress\n    ]);\n    // Force transfer tokens\n    const forceTransfer = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[forceTransfer]\": async (fromAddress, toAddress, amount)=>{\n            const contractAddress = tokenAddress || SECURITY_TOKEN_CORE_ADDRESS;\n            if (!signer || !contractAddress) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(contractAddress, SecurityTokenCoreABI, signer);\n            try {\n                // Check if user has required role\n                const TRANSFER_MANAGER_ROLE = ethers__WEBPACK_IMPORTED_MODULE_6__.keccak256(ethers__WEBPACK_IMPORTED_MODULE_7__.toUtf8Bytes(\"TRANSFER_MANAGER_ROLE\"));\n                const DEFAULT_ADMIN_ROLE = '0x0000000000000000000000000000000000000000000000000000000000000000';\n                const userAddress = await signer.getAddress();\n                const hasTransferManagerRole = await contract.hasRole(TRANSFER_MANAGER_ROLE, userAddress);\n                const hasAdminRole = await contract.hasRole(DEFAULT_ADMIN_ROLE, userAddress);\n                if (!hasTransferManagerRole && !hasAdminRole) {\n                    throw new Error(\"Wallet \".concat(userAddress, \" does not have TRANSFER_MANAGER_ROLE or DEFAULT_ADMIN_ROLE required for force transfers\"));\n                }\n                console.log('Force transferring tokens:', {\n                    fromAddress,\n                    toAddress,\n                    amount\n                });\n                console.log('User address:', userAddress);\n                console.log('Has TRANSFER_MANAGER_ROLE:', hasTransferManagerRole);\n                console.log('Has DEFAULT_ADMIN_ROLE:', hasAdminRole);\n                // Parse amount based on token decimals\n                const decimals = await contract.decimals();\n                const parsedAmount = ethers__WEBPACK_IMPORTED_MODULE_5__.parseUnits(amount, decimals);\n                console.log('Parsed amount:', parsedAmount.toString());\n                // Estimate gas first\n                let gasEstimate;\n                try {\n                    gasEstimate = await contract.forcedTransfer.estimateGas(fromAddress, toAddress, parsedAmount);\n                    console.log('Gas estimate:', gasEstimate.toString());\n                } catch (gasError) {\n                    console.error('Gas estimation failed:', gasError);\n                    throw new Error(\"Gas estimation failed: \".concat(gasError.message, \". This usually means the transaction would revert.\"));\n                }\n                // Execute the force transfer\n                const gasLimit = gasEstimate + gasEstimate / 10n; // Add 10% buffer\n                const tx = await contract.forcedTransfer(fromAddress, toAddress, parsedAmount, {\n                    gasLimit: gasLimit,\n                    gasPrice: ethers__WEBPACK_IMPORTED_MODULE_5__.parseUnits('30', 'gwei')\n                });\n                console.log('Force transfer transaction sent:', tx.hash);\n                return tx.wait();\n            } catch (error) {\n                console.error('Force transfer error:', error);\n                if (error.code === 'ACTION_REJECTED') {\n                    throw new Error('Transaction was rejected by user');\n                } else if (error.reason) {\n                    throw new Error(\"Contract error: \".concat(error.reason));\n                } else if (error.message.includes('Gas estimation failed')) {\n                    throw error; // Re-throw gas estimation errors as-is\n                } else {\n                    throw new Error(\"Failed to force transfer: \".concat(error.message));\n                }\n            }\n        }\n    }[\"useModularToken.useCallback[forceTransfer]\"], [\n        provider,\n        tokenAddress\n    ]);\n    // Load user roles\n    const loadUserRoles = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[loadUserRoles]\": async ()=>{\n            const contractAddress = tokenAddress || SECURITY_TOKEN_CORE_ADDRESS;\n            if (!provider || !signer || !contractAddress) return;\n            try {\n                const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(contractAddress, SecurityTokenCoreABI, provider);\n                const userAddress = await signer.getAddress();\n                // Define role hashes\n                const DEFAULT_ADMIN_ROLE = '0x0000000000000000000000000000000000000000000000000000000000000000';\n                const AGENT_ROLE = ethers__WEBPACK_IMPORTED_MODULE_6__.keccak256(ethers__WEBPACK_IMPORTED_MODULE_7__.toUtf8Bytes(\"AGENT_ROLE\"));\n                const TRANSFER_MANAGER_ROLE = ethers__WEBPACK_IMPORTED_MODULE_6__.keccak256(ethers__WEBPACK_IMPORTED_MODULE_7__.toUtf8Bytes(\"TRANSFER_MANAGER_ROLE\"));\n                const MODULE_MANAGER_ROLE = ethers__WEBPACK_IMPORTED_MODULE_6__.keccak256(ethers__WEBPACK_IMPORTED_MODULE_7__.toUtf8Bytes(\"MODULE_MANAGER_ROLE\"));\n                console.log('Checking roles for user:', userAddress);\n                // Check all roles\n                const roles = {};\n                try {\n                    roles['DEFAULT_ADMIN_ROLE'] = await contract.hasRole(DEFAULT_ADMIN_ROLE, userAddress);\n                    console.log('DEFAULT_ADMIN_ROLE:', roles['DEFAULT_ADMIN_ROLE']);\n                } catch (error) {\n                    console.warn('Error checking DEFAULT_ADMIN_ROLE:', error);\n                    roles['DEFAULT_ADMIN_ROLE'] = false;\n                }\n                try {\n                    roles['AGENT_ROLE'] = await contract.hasRole(AGENT_ROLE, userAddress);\n                    console.log('AGENT_ROLE:', roles['AGENT_ROLE']);\n                } catch (error) {\n                    console.warn('Error checking AGENT_ROLE:', error);\n                    roles['AGENT_ROLE'] = false;\n                }\n                try {\n                    roles['TRANSFER_MANAGER_ROLE'] = await contract.hasRole(TRANSFER_MANAGER_ROLE, userAddress);\n                    console.log('TRANSFER_MANAGER_ROLE:', roles['TRANSFER_MANAGER_ROLE']);\n                } catch (error) {\n                    console.warn('Error checking TRANSFER_MANAGER_ROLE:', error);\n                    roles['TRANSFER_MANAGER_ROLE'] = false;\n                }\n                try {\n                    roles['MODULE_MANAGER_ROLE'] = await contract.hasRole(MODULE_MANAGER_ROLE, userAddress);\n                    console.log('MODULE_MANAGER_ROLE:', roles['MODULE_MANAGER_ROLE']);\n                } catch (error) {\n                    console.warn('Error checking MODULE_MANAGER_ROLE:', error);\n                    roles['MODULE_MANAGER_ROLE'] = false;\n                }\n                setUserRoles(roles);\n            } catch (error) {\n                console.error('Error loading user roles:', error);\n            }\n        }\n    }[\"useModularToken.useCallback[loadUserRoles]\"], [\n        provider,\n        signer,\n        tokenAddress\n    ]);\n    // Load upgrade information\n    const loadUpgradeInfo = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[loadUpgradeInfo]\": async ()=>{\n            if (!provider || !UPGRADE_MANAGER_ADDRESS) return;\n            try {\n                setLoading(true);\n                const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(UPGRADE_MANAGER_ADDRESS, UpgradeManagerABI, provider);\n                const [emergencyModeActive, registeredModules, upgradeDelay, emergencyModeDuration, pendingUpgradeIds] = await Promise.all([\n                    contract.isEmergencyModeActive(),\n                    contract.getRegisteredModules(),\n                    contract.UPGRADE_DELAY(),\n                    contract.EMERGENCY_MODE_DURATION(),\n                    contract.getPendingUpgradeIds()\n                ]);\n                setUpgradeInfo({\n                    emergencyModeActive,\n                    registeredModules,\n                    upgradeDelay: Number(upgradeDelay),\n                    emergencyModeDuration: Number(emergencyModeDuration)\n                });\n                // Load pending upgrades\n                const pendingUpgradesData = await Promise.all(pendingUpgradeIds.map({\n                    \"useModularToken.useCallback[loadUpgradeInfo]\": async (id)=>{\n                        const upgrade = await contract.pendingUpgrades(id);\n                        return {\n                            upgradeId: id,\n                            moduleId: upgrade.moduleId,\n                            proxy: upgrade.proxy,\n                            newImplementation: upgrade.newImplementation,\n                            executeTime: Number(upgrade.executeTime),\n                            executed: upgrade.executed,\n                            cancelled: upgrade.cancelled,\n                            description: upgrade.description\n                        };\n                    }\n                }[\"useModularToken.useCallback[loadUpgradeInfo]\"]));\n                setPendingUpgrades(pendingUpgradesData);\n                // Load upgrade history for SecurityTokenCore\n                const SECURITY_TOKEN_CORE_ID = ethers__WEBPACK_IMPORTED_MODULE_6__.keccak256(ethers__WEBPACK_IMPORTED_MODULE_7__.toUtf8Bytes(\"SECURITY_TOKEN_CORE\"));\n                const history = await contract.getUpgradeHistory(SECURITY_TOKEN_CORE_ID);\n                setUpgradeHistory(history.map({\n                    \"useModularToken.useCallback[loadUpgradeInfo]\": (record)=>({\n                            oldImplementation: record.oldImplementation,\n                            newImplementation: record.newImplementation,\n                            timestamp: Number(record.timestamp),\n                            executor: record.executor,\n                            version: record.version,\n                            isEmergency: record.isEmergency,\n                            description: record.description\n                        })\n                }[\"useModularToken.useCallback[loadUpgradeInfo]\"]));\n            } catch (error) {\n                console.error('Error loading upgrade info:', error);\n                setError('Failed to load upgrade information');\n            } finally{\n                setLoading(false);\n            }\n        }\n    }[\"useModularToken.useCallback[loadUpgradeInfo]\"], [\n        provider\n    ]);\n    // Mint tokens\n    const mintTokens = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[mintTokens]\": async (address, amount)=>{\n            const contractAddress = tokenAddress || SECURITY_TOKEN_CORE_ADDRESS;\n            if (!signer || !contractAddress) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(contractAddress, SecurityTokenCoreABI, signer);\n            try {\n                const decimals = (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.decimals) || 0;\n                const amountWei = ethers__WEBPACK_IMPORTED_MODULE_5__.parseUnits(amount, decimals);\n                const tx = await contract.mint(address, amountWei, {\n                    gasLimit: 400000\n                });\n                return tx.wait();\n            } catch (error) {\n                console.error('Mint tokens error:', error);\n                if (error.code === 'ACTION_REJECTED') {\n                    throw new Error('Transaction was rejected by user');\n                } else if (error.reason) {\n                    throw new Error(\"Contract error: \".concat(error.reason));\n                } else {\n                    throw new Error(\"Failed to mint tokens: \".concat(error.message));\n                }\n            }\n        }\n    }[\"useModularToken.useCallback[mintTokens]\"], [\n        signer,\n        tokenInfo,\n        tokenAddress\n    ]);\n    // Toggle pause state with multiple retry strategies\n    const togglePause = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[togglePause]\": async ()=>{\n            const contractAddress = tokenAddress || SECURITY_TOKEN_CORE_ADDRESS;\n            if (!signer || !contractAddress) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(contractAddress, SecurityTokenCoreABI, signer);\n            const isPausing = !(tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.paused);\n            const functionName = isPausing ? 'pause' : 'unpause';\n            // Try multiple strategies to handle Amoy testnet issues\n            const strategies = [\n                {\n                    \"useModularToken.useCallback[togglePause]\": // Strategy 1: Standard call with high gas\n                    async ()=>{\n                        const tx = isPausing ? await contract.pause({\n                            gasLimit: 500000,\n                            gasPrice: ethers__WEBPACK_IMPORTED_MODULE_5__.parseUnits('30', 'gwei')\n                        }) : await contract.unpause({\n                            gasLimit: 500000,\n                            gasPrice: ethers__WEBPACK_IMPORTED_MODULE_5__.parseUnits('30', 'gwei')\n                        });\n                        return tx.wait();\n                    }\n                }[\"useModularToken.useCallback[togglePause]\"],\n                {\n                    \"useModularToken.useCallback[togglePause]\": // Strategy 2: Raw transaction approach (like the working script)\n                    async ()=>{\n                        const functionSelector = isPausing ? '0x8456cb59' : '0x3f4ba83a';\n                        const nonce = await signer.getNonce();\n                        const tx = await signer.sendTransaction({\n                            to: contractAddress,\n                            data: functionSelector,\n                            gasLimit: 500000,\n                            gasPrice: ethers__WEBPACK_IMPORTED_MODULE_5__.parseUnits('30', 'gwei'),\n                            nonce: nonce\n                        });\n                        return tx.wait();\n                    }\n                }[\"useModularToken.useCallback[togglePause]\"]\n            ];\n            for(let i = 0; i < strategies.length; i++){\n                try {\n                    console.log(\"Attempting \".concat(functionName, \" strategy \").concat(i + 1, \"...\"));\n                    const result = await strategies[i]();\n                    console.log(\"\".concat(functionName, \" successful with strategy \").concat(i + 1));\n                    return result;\n                } catch (error) {\n                    console.error(\"Strategy \".concat(i + 1, \" failed:\"), error);\n                    if (error.code === 'ACTION_REJECTED') {\n                        throw new Error('Transaction was rejected by user');\n                    }\n                    // If this is the last strategy, throw a comprehensive error\n                    if (i === strategies.length - 1) {\n                        var _error_error;\n                        if (error.code === 'UNKNOWN_ERROR' && ((_error_error = error.error) === null || _error_error === void 0 ? void 0 : _error_error.code) === -32603) {\n                            throw new Error(\"Amoy testnet RPC error: The network is experiencing issues. Please try again in a few minutes, or use a different RPC endpoint.\");\n                        } else if (error.reason) {\n                            throw new Error(\"Contract error: \".concat(error.reason));\n                        } else {\n                            throw new Error(\"Failed to \".concat(functionName, \" token after trying multiple methods: \").concat(error.message));\n                        }\n                    }\n                    // Wait a bit before trying the next strategy\n                    await new Promise({\n                        \"useModularToken.useCallback[togglePause]\": (resolve)=>setTimeout(resolve, 1000)\n                    }[\"useModularToken.useCallback[togglePause]\"]);\n                }\n            }\n        }\n    }[\"useModularToken.useCallback[togglePause]\"], [\n        signer,\n        tokenInfo,\n        tokenAddress\n    ]);\n    // Schedule upgrade\n    const scheduleUpgrade = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[scheduleUpgrade]\": async (implementationAddress, description)=>{\n            if (!signer || !UPGRADE_MANAGER_ADDRESS) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(UPGRADE_MANAGER_ADDRESS, UpgradeManagerABI, signer);\n            const SECURITY_TOKEN_CORE_ID = ethers__WEBPACK_IMPORTED_MODULE_6__.keccak256(ethers__WEBPACK_IMPORTED_MODULE_7__.toUtf8Bytes(\"SECURITY_TOKEN_CORE\"));\n            const tx = await contract.scheduleUpgrade(SECURITY_TOKEN_CORE_ID, implementationAddress, description);\n            return tx.wait();\n        }\n    }[\"useModularToken.useCallback[scheduleUpgrade]\"], [\n        signer\n    ]);\n    // Execute upgrade\n    const executeUpgrade = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[executeUpgrade]\": async (upgradeId)=>{\n            if (!signer || !UPGRADE_MANAGER_ADDRESS) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(UPGRADE_MANAGER_ADDRESS, UpgradeManagerABI, signer);\n            const tx = await contract.executeUpgrade(upgradeId);\n            return tx.wait();\n        }\n    }[\"useModularToken.useCallback[executeUpgrade]\"], [\n        signer\n    ]);\n    // Emergency upgrade\n    const emergencyUpgrade = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[emergencyUpgrade]\": async (implementationAddress, description)=>{\n            if (!signer || !UPGRADE_MANAGER_ADDRESS) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(UPGRADE_MANAGER_ADDRESS, UpgradeManagerABI, signer);\n            const SECURITY_TOKEN_CORE_ID = ethers__WEBPACK_IMPORTED_MODULE_6__.keccak256(ethers__WEBPACK_IMPORTED_MODULE_7__.toUtf8Bytes(\"SECURITY_TOKEN_CORE\"));\n            const tx = await contract.emergencyUpgrade(SECURITY_TOKEN_CORE_ID, implementationAddress, description);\n            return tx.wait();\n        }\n    }[\"useModularToken.useCallback[emergencyUpgrade]\"], [\n        signer\n    ]);\n    // Toggle emergency mode\n    const toggleEmergencyMode = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[toggleEmergencyMode]\": async ()=>{\n            if (!signer || !UPGRADE_MANAGER_ADDRESS) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(UPGRADE_MANAGER_ADDRESS, UpgradeManagerABI, signer);\n            const tx = (upgradeInfo === null || upgradeInfo === void 0 ? void 0 : upgradeInfo.emergencyModeActive) ? await contract.deactivateEmergencyMode() : await contract.activateEmergencyMode();\n            return tx.wait();\n        }\n    }[\"useModularToken.useCallback[toggleEmergencyMode]\"], [\n        signer,\n        upgradeInfo\n    ]);\n    // Cancel upgrade\n    const cancelUpgrade = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[cancelUpgrade]\": async (upgradeId)=>{\n            if (!signer || !UPGRADE_MANAGER_ADDRESS) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(UPGRADE_MANAGER_ADDRESS, UpgradeManagerABI, signer);\n            const tx = await contract.cancelUpgrade(upgradeId);\n            return tx.wait();\n        }\n    }[\"useModularToken.useCallback[cancelUpgrade]\"], [\n        signer\n    ]);\n    // Update token price (now using direct updateTokenPrice function)\n    const updateTokenPrice = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[updateTokenPrice]\": async (newPrice)=>{\n            const contractAddress = tokenAddress || SECURITY_TOKEN_CORE_ADDRESS;\n            if (!signer || !contractAddress) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(contractAddress, SecurityTokenCoreABI, signer);\n            try {\n                // First check if user has admin role\n                const DEFAULT_ADMIN_ROLE = '0x0000000000000000000000000000000000000000000000000000000000000000';\n                const userAddress = await signer.getAddress();\n                const hasAdminRole = await contract.hasRole(DEFAULT_ADMIN_ROLE, userAddress);\n                if (!hasAdminRole) {\n                    throw new Error(\"Wallet \".concat(userAddress, \" does not have DEFAULT_ADMIN_ROLE on this token contract\"));\n                }\n                console.log('Updating token price from wallet:', userAddress);\n                console.log('New price:', newPrice);\n                console.log('Contract address:', contractAddress);\n                // Try direct updateTokenPrice first (for upgraded contracts)\n                let gasEstimate;\n                try {\n                    gasEstimate = await contract.updateTokenPrice.estimateGas(newPrice);\n                    console.log('Gas estimate (direct):', gasEstimate.toString());\n                    // Execute with estimated gas + buffer\n                    const gasLimit = gasEstimate + gasEstimate / 10n; // Add 10% buffer\n                    const tx = await contract.updateTokenPrice(newPrice, {\n                        gasLimit: gasLimit,\n                        gasPrice: ethers__WEBPACK_IMPORTED_MODULE_5__.parseUnits('30', 'gwei')\n                    });\n                    console.log('Transaction sent (direct):', tx.hash);\n                    return tx.wait();\n                } catch (directError) {\n                    console.log('Direct updateTokenPrice failed, trying updateTokenMetadata fallback:', directError.message);\n                    // Fallback to updateTokenMetadata for older contracts\n                    const currentMetadata = await contract.getTokenMetadata();\n                    const currentBonusTiers = currentMetadata[1];\n                    const currentDetails = currentMetadata[2];\n                    gasEstimate = await contract.updateTokenMetadata.estimateGas(newPrice, currentBonusTiers, currentDetails);\n                    console.log('Gas estimate (fallback):', gasEstimate.toString());\n                    const gasLimit = gasEstimate + gasEstimate / 10n;\n                    const tx = await contract.updateTokenMetadata(newPrice, currentBonusTiers, currentDetails, {\n                        gasLimit: gasLimit,\n                        gasPrice: ethers__WEBPACK_IMPORTED_MODULE_5__.parseUnits('30', 'gwei')\n                    });\n                    console.log('Transaction sent (fallback):', tx.hash);\n                    return tx.wait();\n                }\n            } catch (error) {\n                console.error('Update token price error:', error);\n                if (error.code === 'ACTION_REJECTED') {\n                    throw new Error('Transaction was rejected by user');\n                } else if (error.reason) {\n                    throw new Error(\"Contract error: \".concat(error.reason));\n                } else if (error.message.includes('Gas estimation failed')) {\n                    throw error; // Re-throw gas estimation errors as-is\n                } else {\n                    throw new Error(\"Failed to update token price: \".concat(error.message));\n                }\n            }\n        }\n    }[\"useModularToken.useCallback[updateTokenPrice]\"], [\n        signer,\n        tokenAddress\n    ]);\n    // Update bonus tiers (now using direct updateBonusTiers function)\n    const updateBonusTiers = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[updateBonusTiers]\": async (newBonusTiers)=>{\n            const contractAddress = tokenAddress || SECURITY_TOKEN_CORE_ADDRESS;\n            if (!signer || !contractAddress) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(contractAddress, SecurityTokenCoreABI, signer);\n            try {\n                // Try direct updateBonusTiers first (for upgraded contracts)\n                try {\n                    const tx = await contract.updateBonusTiers(newBonusTiers, {\n                        gasLimit: 300000\n                    });\n                    console.log('Bonus tiers updated (direct):', tx.hash);\n                    return tx.wait();\n                } catch (directError) {\n                    console.log('Direct updateBonusTiers failed, trying updateTokenMetadata fallback:', directError.message);\n                    // Fallback to updateTokenMetadata for older contracts\n                    const currentMetadata = await contract.getTokenMetadata();\n                    const currentPrice = currentMetadata[0];\n                    const currentDetails = currentMetadata[2];\n                    const tx = await contract.updateTokenMetadata(currentPrice, newBonusTiers, currentDetails, {\n                        gasLimit: 300000\n                    });\n                    console.log('Bonus tiers updated (fallback):', tx.hash);\n                    return tx.wait();\n                }\n            } catch (error) {\n                console.error('Update bonus tiers error:', error);\n                if (error.code === 'ACTION_REJECTED') {\n                    throw new Error('Transaction was rejected by user');\n                } else if (error.reason) {\n                    throw new Error(\"Contract error: \".concat(error.reason));\n                } else {\n                    throw new Error(\"Failed to update bonus tiers: \".concat(error.message));\n                }\n            }\n        }\n    }[\"useModularToken.useCallback[updateBonusTiers]\"], [\n        signer,\n        tokenAddress\n    ]);\n    // Update max supply\n    const updateMaxSupply = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[updateMaxSupply]\": async (newMaxSupply)=>{\n            const contractAddress = tokenAddress || SECURITY_TOKEN_CORE_ADDRESS;\n            if (!signer || !contractAddress) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(contractAddress, SecurityTokenCoreABI, signer);\n            try {\n                const maxSupplyWei = ethers__WEBPACK_IMPORTED_MODULE_5__.parseUnits(newMaxSupply, (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.decimals) || 0);\n                const tx = await contract.updateMaxSupply(maxSupplyWei, {\n                    gasLimit: 300000\n                });\n                return tx.wait();\n            } catch (error) {\n                console.error('Update max supply error:', error);\n                if (error.code === 'ACTION_REJECTED') {\n                    throw new Error('Transaction was rejected by user');\n                } else if (error.reason) {\n                    throw new Error(\"Contract error: \".concat(error.reason));\n                } else {\n                    throw new Error(\"Failed to update max supply: \".concat(error.message));\n                }\n            }\n        }\n    }[\"useModularToken.useCallback[updateMaxSupply]\"], [\n        signer,\n        tokenInfo,\n        tokenAddress\n    ]);\n    // Add to whitelist with identity registration\n    const addToWhitelist = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[addToWhitelist]\": async (address)=>{\n            const contractAddress = tokenAddress || SECURITY_TOKEN_CORE_ADDRESS;\n            if (!signer || !contractAddress) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(contractAddress, SecurityTokenCoreABI, signer);\n            try {\n                // First check if the address is already verified/registered\n                const isVerified = await contract.isVerified(address);\n                if (!isVerified) {\n                    // Need to register identity first - use the API approach for this\n                    throw new Error('Address must be registered first. Please use the API method or register the identity manually.');\n                }\n                // Now try to add to whitelist\n                const tx = await contract.addToWhitelist(address, {\n                    gasLimit: 400000,\n                    gasPrice: ethers__WEBPACK_IMPORTED_MODULE_5__.parseUnits('30', 'gwei')\n                });\n                return tx.wait();\n            } catch (error) {\n                console.error('Add to whitelist error:', error);\n                if (error.code === 'ACTION_REJECTED') {\n                    throw new Error('Transaction was rejected by user');\n                } else if (error.reason) {\n                    throw new Error(\"Contract error: \".concat(error.reason));\n                } else if (error.message.includes('identity not registered')) {\n                    throw new Error('Address must be registered first. Please use the API method which handles registration automatically.');\n                } else {\n                    throw new Error(\"Failed to add address to whitelist: \".concat(error.message));\n                }\n            }\n        }\n    }[\"useModularToken.useCallback[addToWhitelist]\"], [\n        signer,\n        tokenAddress\n    ]);\n    // Remove from whitelist\n    const removeFromWhitelist = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[removeFromWhitelist]\": async (address)=>{\n            const contractAddress = tokenAddress || SECURITY_TOKEN_CORE_ADDRESS;\n            if (!signer || !contractAddress) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(contractAddress, SecurityTokenCoreABI, signer);\n            try {\n                const tx = await contract.removeFromWhitelist(address, {\n                    gasLimit: 300000\n                });\n                return tx.wait();\n            } catch (error) {\n                console.error('Remove from whitelist error:', error);\n                if (error.code === 'ACTION_REJECTED') {\n                    throw new Error('Transaction was rejected by user');\n                } else if (error.reason) {\n                    throw new Error(\"Contract error: \".concat(error.reason));\n                } else {\n                    throw new Error(\"Failed to remove address from whitelist: \".concat(error.message));\n                }\n            }\n        }\n    }[\"useModularToken.useCallback[removeFromWhitelist]\"], [\n        signer,\n        tokenAddress\n    ]);\n    // Refresh all data\n    const refreshData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[refreshData]\": async ()=>{\n            await Promise.all([\n                loadTokenInfo(),\n                loadContractAddresses(),\n                loadUserRoles(),\n                loadUpgradeInfo()\n            ]);\n        }\n    }[\"useModularToken.useCallback[refreshData]\"], [\n        loadTokenInfo,\n        loadContractAddresses,\n        loadUserRoles,\n        loadUpgradeInfo\n    ]);\n    // Initialize on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useModularToken.useEffect\": ()=>{\n            initializeProvider();\n        }\n    }[\"useModularToken.useEffect\"], [\n        initializeProvider\n    ]);\n    // Load data when provider is ready\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useModularToken.useEffect\": ()=>{\n            if (provider && signer) {\n                refreshData();\n            }\n        }\n    }[\"useModularToken.useEffect\"], [\n        provider,\n        signer,\n        refreshData\n    ]);\n    return {\n        // State\n        provider,\n        signer,\n        tokenInfo,\n        contractAddresses,\n        userRoles,\n        upgradeInfo,\n        pendingUpgrades,\n        upgradeHistory,\n        loading,\n        error,\n        // Actions\n        initializeProvider,\n        loadTokenInfo,\n        loadContractAddresses,\n        loadUserRoles,\n        loadUpgradeInfo,\n        mintTokens,\n        togglePause,\n        scheduleUpgrade,\n        executeUpgrade,\n        emergencyUpgrade,\n        toggleEmergencyMode,\n        cancelUpgrade,\n        refreshData,\n        // Admin Functions\n        updateTokenPrice,\n        updateBonusTiers,\n        updateMaxSupply,\n        addToWhitelist,\n        removeFromWhitelist,\n        forceTransfer,\n        // Utilities\n        setError,\n        setLoading\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useModularToken.ts\n"));

/***/ })

});