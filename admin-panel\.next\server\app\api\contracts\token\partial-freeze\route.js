/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/contracts/token/partial-freeze/route";
exports.ids = ["app/api/contracts/token/partial-freeze/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcontracts%2Ftoken%2Fpartial-freeze%2Froute&page=%2Fapi%2Fcontracts%2Ftoken%2Fpartial-freeze%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcontracts%2Ftoken%2Fpartial-freeze%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcontracts%2Ftoken%2Fpartial-freeze%2Froute&page=%2Fapi%2Fcontracts%2Ftoken%2Fpartial-freeze%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcontracts%2Ftoken%2Fpartial-freeze%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_github_tokendev_newroo_admin_panel_src_app_api_contracts_token_partial_freeze_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/contracts/token/partial-freeze/route.ts */ \"(rsc)/./src/app/api/contracts/token/partial-freeze/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/contracts/token/partial-freeze/route\",\n        pathname: \"/api/contracts/token/partial-freeze\",\n        filename: \"route\",\n        bundlePath: \"app/api/contracts/token/partial-freeze/route\"\n    },\n    resolvedPagePath: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\api\\\\contracts\\\\token\\\\partial-freeze\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_github_tokendev_newroo_admin_panel_src_app_api_contracts_token_partial_freeze_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcontracts%2Ftoken%2Fpartial-freeze%2Froute&page=%2Fapi%2Fcontracts%2Ftoken%2Fpartial-freeze%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcontracts%2Ftoken%2Fpartial-freeze%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/contracts/token/partial-freeze/route.ts":
/*!*************************************************************!*\
  !*** ./src/app/api/contracts/token/partial-freeze/route.ts ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/address/checks.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/utils/units.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/providers/provider-jsonrpc.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/wallet/wallet.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/contract/contract.js\");\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _contracts_SecurityToken_json__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../../../contracts/SecurityToken.json */ \"(rsc)/./src/contracts/SecurityToken.json\");\n\n\n\n// Extract ABI from artifact\nconst SecurityTokenABI = _contracts_SecurityToken_json__WEBPACK_IMPORTED_MODULE_1__.abi;\n// Load private key from environment variable\nconst PRIVATE_KEY = process.env.CONTRACT_ADMIN_PRIVATE_KEY;\nconst RPC_URLS = {\n    amoy: process.env.AMOY_RPC_URL || 'https://rpc-amoy.polygon.technology/',\n    polygon: process.env.POLYGON_RPC_URL || 'https://polygon-rpc.com',\n    unknown: process.env.AMOY_RPC_URL || 'https://rpc-amoy.polygon.technology/'\n};\n// Network chain IDs\nconst CHAIN_IDS = {\n    amoy: 80002,\n    polygon: 137,\n    unknown: 80002\n};\n// Vault address - this should be a controlled wallet where frozen tokens are stored\n// The ideal approach would be to have a dedicated escrow contract for this purpose\n// For now, we'll use a hardcoded address that should be managed by the admin\nconst VAULT_ADDRESSES = {\n    amoy: process.env.AMOY_VAULT_ADDRESS || '******************************************',\n    polygon: process.env.POLYGON_VAULT_ADDRESS || '******************************************',\n    unknown: process.env.AMOY_VAULT_ADDRESS || '******************************************'\n};\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { tokenAddress, fromAddress, amount, network = 'amoy' } = body;\n        if (!tokenAddress || !fromAddress || !amount) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Token address, from address, and amount are required'\n            }, {\n                status: 400\n            });\n        }\n        if (!PRIVATE_KEY) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'CONTRACT_ADMIN_PRIVATE_KEY environment variable not set',\n                details: 'For security reasons, the API requires a secure method to sign transactions.',\n                clientSideInstructions: true,\n                message: 'The server is not configured with admin credentials.'\n            }, {\n                status: 422\n            });\n        }\n        // Validate addresses\n        if (!ethers__WEBPACK_IMPORTED_MODULE_2__.isAddress(tokenAddress) || !ethers__WEBPACK_IMPORTED_MODULE_2__.isAddress(fromAddress)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Invalid address format provided'\n            }, {\n                status: 400\n            });\n        }\n        // Validate amount\n        let parsedAmount;\n        try {\n            parsedAmount = ethers__WEBPACK_IMPORTED_MODULE_3__.parseUnits(amount.toString(), 18); // Assuming 18 decimals\n        } catch (error) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Invalid amount format'\n            }, {\n                status: 400\n            });\n        }\n        // Get RPC URL for the specified network, defaulting to Amoy\n        const actualNetwork = network === 'unknown' ? 'amoy' : network;\n        const rpcUrl = RPC_URLS[actualNetwork] || RPC_URLS.amoy;\n        const chainId = CHAIN_IDS[actualNetwork] || CHAIN_IDS.amoy;\n        // Get vault address for the network\n        const vaultAddress = VAULT_ADDRESSES[actualNetwork] || VAULT_ADDRESSES.amoy;\n        console.log(`Using network: ${actualNetwork}, RPC URL: ${rpcUrl}, Chain ID: ${chainId}`);\n        console.log(`Vault address: ${vaultAddress}`);\n        // Connect to the network\n        const provider = new ethers__WEBPACK_IMPORTED_MODULE_4__.JsonRpcProvider(rpcUrl, {\n            chainId,\n            name: actualNetwork\n        });\n        // Ensure the network is connected\n        const network_details = await provider.getNetwork();\n        console.log(`Connected to network: ${network_details.name} (Chain ID: ${network_details.chainId})`);\n        // Create wallet and connect to provider\n        const wallet = new ethers__WEBPACK_IMPORTED_MODULE_5__.Wallet(PRIVATE_KEY, provider);\n        console.log(`Wallet address: ${wallet.address}`);\n        // Connect to the token contract\n        const tokenContract = new ethers__WEBPACK_IMPORTED_MODULE_6__.Contract(tokenAddress, SecurityTokenABI.abi, wallet);\n        // Check current balance of the address\n        const currentBalance = await tokenContract.balanceOf(fromAddress);\n        console.log(`Current balance of ${fromAddress}: ${ethers__WEBPACK_IMPORTED_MODULE_3__.formatUnits(currentBalance, 18)}`);\n        if (currentBalance < parsedAmount) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: `Insufficient balance. Address has ${ethers__WEBPACK_IMPORTED_MODULE_3__.formatUnits(currentBalance, 18)} tokens but trying to freeze ${amount} tokens.`\n            }, {\n                status: 400\n            });\n        }\n        // Execute the force transfer using adminTransfer function\n        console.log(`Freezing ${amount} tokens from ${fromAddress} by transferring to vault ${vaultAddress}...`);\n        try {\n            // Using the adminTransfer function to move tokens to the vault\n            const tx = await tokenContract.adminTransfer(fromAddress, vaultAddress, parsedAmount);\n            console.log(`Transaction hash: ${tx.hash}`);\n            // Wait for the transaction to be mined\n            const receipt = await tx.wait();\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                action: 'partialFreeze',\n                txHash: tx.hash,\n                blockNumber: receipt.blockNumber,\n                from: fromAddress,\n                to: vaultAddress,\n                amount: amount\n            });\n        } catch (txError) {\n            console.error('Transaction error:', txError);\n            // Check if the issue might be a permissions problem\n            try {\n                const DEFAULT_ADMIN_ROLE = await tokenContract.DEFAULT_ADMIN_ROLE();\n                const hasAdminRole = await tokenContract.hasRole(DEFAULT_ADMIN_ROLE, wallet.address);\n                if (!hasAdminRole) {\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        success: false,\n                        error: \"The connected wallet doesn't have the DEFAULT_ADMIN_ROLE required for partial freezing\",\n                        details: `Please grant DEFAULT_ADMIN_ROLE to ${wallet.address} on the token contract`\n                    }, {\n                        status: 403\n                    });\n                }\n            } catch (roleCheckError) {\n                console.error('Role check error:', roleCheckError);\n            }\n            throw txError; // Re-throw to be caught by the outer catch\n        }\n    } catch (error) {\n        console.error('Error freezing tokens:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: error.message || 'An unknown error occurred'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/contracts/token/partial-freeze/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/contracts/SecurityToken.json":
/*!******************************************!*\
  !*** ./src/contracts/SecurityToken.json ***!
  \******************************************/
/***/ ((module) => {

"use strict";
module.exports = /*#__PURE__*/JSON.parse('{"abi":[{"inputs":[],"stateMutability":"nonpayable","type":"constructor"},{"inputs":[],"name":"AccessControlBadConfirmation","type":"error"},{"inputs":[{"internalType":"address","name":"account","type":"address"},{"internalType":"bytes32","name":"neededRole","type":"bytes32"}],"name":"AccessControlUnauthorizedAccount","type":"error"},{"inputs":[{"internalType":"address","name":"target","type":"address"}],"name":"AddressEmptyCode","type":"error"},{"inputs":[{"internalType":"address","name":"implementation","type":"address"}],"name":"ERC1967InvalidImplementation","type":"error"},{"inputs":[],"name":"ERC1967NonPayable","type":"error"},{"inputs":[{"internalType":"address","name":"spender","type":"address"},{"internalType":"uint256","name":"allowance","type":"uint256"},{"internalType":"uint256","name":"needed","type":"uint256"}],"name":"ERC20InsufficientAllowance","type":"error"},{"inputs":[{"internalType":"address","name":"sender","type":"address"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"uint256","name":"needed","type":"uint256"}],"name":"ERC20InsufficientBalance","type":"error"},{"inputs":[{"internalType":"address","name":"approver","type":"address"}],"name":"ERC20InvalidApprover","type":"error"},{"inputs":[{"internalType":"address","name":"receiver","type":"address"}],"name":"ERC20InvalidReceiver","type":"error"},{"inputs":[{"internalType":"address","name":"sender","type":"address"}],"name":"ERC20InvalidSender","type":"error"},{"inputs":[{"internalType":"address","name":"spender","type":"address"}],"name":"ERC20InvalidSpender","type":"error"},{"inputs":[],"name":"EnforcedPause","type":"error"},{"inputs":[],"name":"ExpectedPause","type":"error"},{"inputs":[],"name":"FailedCall","type":"error"},{"inputs":[],"name":"InvalidInitialization","type":"error"},{"inputs":[],"name":"NotInitializing","type":"error"},{"inputs":[],"name":"ReentrancyGuardReentrantCall","type":"error"},{"inputs":[],"name":"UUPSUnauthorizedCallContext","type":"error"},{"inputs":[{"internalType":"bytes32","name":"slot","type":"bytes32"}],"name":"UUPSUnsupportedProxiableUUID","type":"error"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"agent","type":"address"}],"name":"AgentAdded","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"agent","type":"address"}],"name":"AgentRemoved","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"account","type":"address"},{"indexed":false,"internalType":"uint256","name":"timestamp","type":"uint256"}],"name":"AgreementAccepted","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"owner","type":"address"},{"indexed":true,"internalType":"address","name":"spender","type":"address"},{"indexed":false,"internalType":"uint256","name":"value","type":"uint256"}],"name":"Approval","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"oldCompliance","type":"address"},{"indexed":true,"internalType":"address","name":"newCompliance","type":"address"}],"name":"ComplianceUpdated","type":"event"},{"anonymous":false,"inputs":[{"indexed":false,"internalType":"bool","name":"enabled","type":"bool"}],"name":"ConditionalTransfersUpdated","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"admin","type":"address"}],"name":"EmergencyPaused","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"admin","type":"address"}],"name":"EmergencyUnpaused","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"from","type":"address"},{"indexed":true,"internalType":"address","name":"to","type":"address"},{"indexed":false,"internalType":"uint256","name":"value","type":"uint256"}],"name":"ForcedTransfer","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"bytes4","name":"functionSelector","type":"bytes4"},{"indexed":true,"internalType":"address","name":"admin","type":"address"}],"name":"FunctionPaused","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"bytes4","name":"functionSelector","type":"bytes4"},{"indexed":true,"internalType":"address","name":"admin","type":"address"}],"name":"FunctionUnpaused","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"oldRegistry","type":"address"},{"indexed":true,"internalType":"address","name":"newRegistry","type":"address"}],"name":"IdentityRegistryUpdated","type":"event"},{"anonymous":false,"inputs":[{"indexed":false,"internalType":"uint64","name":"version","type":"uint64"}],"name":"Initialized","type":"event"},{"anonymous":false,"inputs":[{"indexed":false,"internalType":"uint256","name":"oldMaxSupply","type":"uint256"},{"indexed":false,"internalType":"uint256","name":"newMaxSupply","type":"uint256"}],"name":"MaxSupplyUpdated","type":"event"},{"anonymous":false,"inputs":[{"indexed":false,"internalType":"address","name":"account","type":"address"}],"name":"Paused","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"bytes32","name":"role","type":"bytes32"},{"indexed":true,"internalType":"bytes32","name":"previousAdminRole","type":"bytes32"},{"indexed":true,"internalType":"bytes32","name":"newAdminRole","type":"bytes32"}],"name":"RoleAdminChanged","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"bytes32","name":"role","type":"bytes32"},{"indexed":true,"internalType":"address","name":"account","type":"address"},{"indexed":true,"internalType":"address","name":"sender","type":"address"}],"name":"RoleGranted","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"bytes32","name":"role","type":"bytes32"},{"indexed":true,"internalType":"address","name":"account","type":"address"},{"indexed":true,"internalType":"address","name":"sender","type":"address"}],"name":"RoleRevoked","type":"event"},{"anonymous":false,"inputs":[{"indexed":false,"internalType":"string","name":"tokenImageUrl","type":"string"}],"name":"TokenImageUpdated","type":"event"},{"anonymous":false,"inputs":[{"indexed":false,"internalType":"string","name":"tokenPrice","type":"string"},{"indexed":false,"internalType":"string","name":"bonusTiers","type":"string"},{"indexed":false,"internalType":"string","name":"tokenDetails","type":"string"}],"name":"TokenMetadataUpdated","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"from","type":"address"},{"indexed":true,"internalType":"address","name":"to","type":"address"},{"indexed":false,"internalType":"uint256","name":"value","type":"uint256"}],"name":"Transfer","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"bytes32","name":"transferId","type":"bytes32"},{"indexed":true,"internalType":"address","name":"from","type":"address"},{"indexed":true,"internalType":"address","name":"to","type":"address"},{"indexed":false,"internalType":"uint256","name":"amount","type":"uint256"}],"name":"TransferApproved","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"from","type":"address"},{"indexed":true,"internalType":"address","name":"to","type":"address"},{"indexed":false,"internalType":"uint256","name":"transferAmount","type":"uint256"},{"indexed":false,"internalType":"uint256","name":"feeAmount","type":"uint256"}],"name":"TransferFeeCollected","type":"event"},{"anonymous":false,"inputs":[{"indexed":false,"internalType":"bool","name":"enabled","type":"bool"},{"indexed":false,"internalType":"uint256","name":"feePercentage","type":"uint256"},{"indexed":false,"internalType":"address","name":"feeCollector","type":"address"}],"name":"TransferFeesUpdated","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"account","type":"address"},{"indexed":false,"internalType":"bool","name":"whitelisted","type":"bool"}],"name":"TransferWhitelistAddressUpdated","type":"event"},{"anonymous":false,"inputs":[{"indexed":false,"internalType":"bool","name":"enabled","type":"bool"}],"name":"TransferWhitelistUpdated","type":"event"},{"anonymous":false,"inputs":[{"indexed":false,"internalType":"address","name":"account","type":"address"}],"name":"Unpaused","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"implementation","type":"address"}],"name":"Upgraded","type":"event"},{"inputs":[],"name":"AGENT_ROLE","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"DEFAULT_ADMIN_ROLE","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"TRANSFER_MANAGER_ROLE","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"UPGRADE_INTERFACE_VERSION","outputs":[{"internalType":"string","name":"","type":"string"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"acceptAgreement","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"agent","type":"address"}],"name":"addAgent","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"addToWhitelist","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"owner","type":"address"},{"internalType":"address","name":"spender","type":"address"}],"name":"allowance","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"spender","type":"address"},{"internalType":"uint256","name":"value","type":"uint256"}],"name":"approve","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"from","type":"address"},{"internalType":"address","name":"to","type":"address"},{"internalType":"uint256","name":"amount","type":"uint256"},{"internalType":"uint256","name":"nonce","type":"uint256"}],"name":"approveTransfer","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"balanceOf","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address[]","name":"accounts","type":"address[]"}],"name":"batchAddToWhitelist","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[],"name":"bonusTiers","outputs":[{"internalType":"string","name":"","type":"string"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"uint256","name":"value","type":"uint256"}],"name":"burn","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"},{"internalType":"uint256","name":"value","type":"uint256"}],"name":"burnFrom","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"from","type":"address"},{"internalType":"address","name":"to","type":"address"},{"internalType":"uint256","name":"amount","type":"uint256"}],"name":"canTransfer","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"compliance","outputs":[{"internalType":"address","name":"","type":"address"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"conditionalTransfersEnabled","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"decimals","outputs":[{"internalType":"uint8","name":"","type":"uint8"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"emergencyPause","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[],"name":"emergencyUnpause","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"to","type":"address"},{"internalType":"uint256","name":"amount","type":"uint256"},{"internalType":"uint256","name":"nonce","type":"uint256"}],"name":"executeApprovedTransfer","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"from","type":"address"},{"internalType":"address","name":"to","type":"address"},{"internalType":"uint256","name":"amount","type":"uint256"}],"name":"forcedTransfer","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"freezeAddress","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"uint256","name":"index","type":"uint256"}],"name":"getAgentAt","outputs":[{"internalType":"address","name":"","type":"address"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"getAgentCount","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"getAgreementAcceptanceTimestamp","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"getAllAgents","outputs":[{"internalType":"address[]","name":"","type":"address[]"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"bytes32","name":"role","type":"bytes32"}],"name":"getRoleAdmin","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"getTransferFeeConfig","outputs":[{"internalType":"uint256","name":"feePercentage","type":"uint256"},{"internalType":"address","name":"feeCollector","type":"address"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"getTransferNonce","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"address","name":"account","type":"address"}],"name":"grantRole","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"hasAcceptedAgreement","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"address","name":"account","type":"address"}],"name":"hasRole","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"identityRegistry","outputs":[{"internalType":"address","name":"","type":"address"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"string","name":"name_","type":"string"},{"internalType":"string","name":"symbol_","type":"string"},{"internalType":"uint8","name":"decimals_","type":"uint8"},{"internalType":"uint256","name":"maxSupply_","type":"uint256"},{"internalType":"address","name":"identityRegistry_","type":"address"},{"internalType":"address","name":"compliance_","type":"address"},{"internalType":"address","name":"admin_","type":"address"},{"internalType":"string","name":"tokenPrice_","type":"string"},{"internalType":"string","name":"bonusTiers_","type":"string"},{"internalType":"string","name":"tokenDetails_","type":"string"},{"internalType":"string","name":"tokenImageUrl_","type":"string"}],"name":"initialize","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"investorCountry","outputs":[{"internalType":"uint16","name":"","type":"uint16"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"isAgent","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"isEmergencyPaused","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"bytes4","name":"functionSelector","type":"bytes4"}],"name":"isFunctionPaused","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"isTransferWhitelisted","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"isVerified","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"isWhitelisted","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"maxSupply","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"to","type":"address"},{"internalType":"uint256","name":"amount","type":"uint256"}],"name":"mint","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[],"name":"name","outputs":[{"internalType":"string","name":"","type":"string"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"pause","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"bytes4","name":"functionSelector","type":"bytes4"}],"name":"pauseFunction","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[],"name":"paused","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"proxiableUUID","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"agent","type":"address"}],"name":"removeAgent","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"removeFromWhitelist","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"address","name":"callerConfirmation","type":"address"}],"name":"renounceRole","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"address","name":"account","type":"address"}],"name":"revokeRole","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"bool","name":"enabled","type":"bool"}],"name":"setConditionalTransfers","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"uint256","name":"feePercentage","type":"uint256"},{"internalType":"address","name":"feeCollector","type":"address"}],"name":"setTransferFees","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"bool","name":"enabled","type":"bool"}],"name":"setTransferWhitelist","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"},{"internalType":"bool","name":"whitelisted","type":"bool"}],"name":"setTransferWhitelistAddress","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"bytes4","name":"interfaceId","type":"bytes4"}],"name":"supportsInterface","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"symbol","outputs":[{"internalType":"string","name":"","type":"string"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"tokenDetails","outputs":[{"internalType":"string","name":"","type":"string"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"tokenImageUrl","outputs":[{"internalType":"string","name":"","type":"string"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"tokenPrice","outputs":[{"internalType":"string","name":"","type":"string"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"totalSupply","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"to","type":"address"},{"internalType":"uint256","name":"value","type":"uint256"}],"name":"transfer","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"nonpayable","type":"function"},{"inputs":[],"name":"transferFeesEnabled","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"from","type":"address"},{"internalType":"address","name":"to","type":"address"},{"internalType":"uint256","name":"value","type":"uint256"}],"name":"transferFrom","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"nonpayable","type":"function"},{"inputs":[],"name":"transferWhitelistEnabled","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"unfreezeAddress","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[],"name":"unpause","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"bytes4","name":"functionSelector","type":"bytes4"}],"name":"unpauseFunction","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"newCompliance","type":"address"}],"name":"updateCompliance","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"newIdentityRegistry","type":"address"}],"name":"updateIdentityRegistry","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"uint256","name":"newMaxSupply","type":"uint256"}],"name":"updateMaxSupply","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"string","name":"tokenImageUrl_","type":"string"}],"name":"updateTokenImageUrl","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"string","name":"tokenPrice_","type":"string"},{"internalType":"string","name":"bonusTiers_","type":"string"},{"internalType":"string","name":"tokenDetails_","type":"string"}],"name":"updateTokenMetadata","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"newImplementation","type":"address"},{"internalType":"bytes","name":"data","type":"bytes"}],"name":"upgradeToAndCall","outputs":[],"stateMutability":"payable","type":"function"},{"inputs":[],"name":"version","outputs":[{"internalType":"string","name":"","type":"string"}],"stateMutability":"pure","type":"function"}]}');

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/ethers","vendor-chunks/@noble","vendor-chunks/@adraffy","vendor-chunks/aes-js"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcontracts%2Ftoken%2Fpartial-freeze%2Froute&page=%2Fapi%2Fcontracts%2Ftoken%2Fpartial-freeze%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcontracts%2Ftoken%2Fpartial-freeze%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();