/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/admin/manage-whitelist/route";
exports.ids = ["app/api/admin/manage-whitelist/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fmanage-whitelist%2Froute&page=%2Fapi%2Fadmin%2Fmanage-whitelist%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fmanage-whitelist%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fmanage-whitelist%2Froute&page=%2Fapi%2Fadmin%2Fmanage-whitelist%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fmanage-whitelist%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_github_tokendev_newroo_admin_panel_src_app_api_admin_manage_whitelist_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/admin/manage-whitelist/route.ts */ \"(rsc)/./src/app/api/admin/manage-whitelist/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/admin/manage-whitelist/route\",\n        pathname: \"/api/admin/manage-whitelist\",\n        filename: \"route\",\n        bundlePath: \"app/api/admin/manage-whitelist/route\"\n    },\n    resolvedPagePath: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\api\\\\admin\\\\manage-whitelist\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_github_tokendev_newroo_admin_panel_src_app_api_admin_manage_whitelist_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fmanage-whitelist%2Froute&page=%2Fapi%2Fadmin%2Fmanage-whitelist%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fmanage-whitelist%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/admin/manage-whitelist/route.ts":
/*!*****************************************************!*\
  !*** ./src/app/api/admin/manage-whitelist/route.ts ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/address/checks.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/providers/provider-jsonrpc.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/wallet/wallet.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/contract/contract.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/utils/units.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/abi/interface.js\");\n\n\nconst SECURITY_TOKEN_CORE_ABI = [\n    \"function addToWhitelist(address account) external\",\n    \"function removeFromWhitelist(address account) external\",\n    \"function isWhitelisted(address account) external view returns (bool)\"\n];\nasync function POST(request) {\n    try {\n        const { tokenAddress, address, action } = await request.json();\n        if (!tokenAddress) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Token address is required'\n            }, {\n                status: 400\n            });\n        }\n        if (!address) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Address is required'\n            }, {\n                status: 400\n            });\n        }\n        if (!action || ![\n            'add',\n            'remove'\n        ].includes(action)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Action must be \"add\" or \"remove\"'\n            }, {\n                status: 400\n            });\n        }\n        // Validate addresses\n        if (!ethers__WEBPACK_IMPORTED_MODULE_1__.isAddress(tokenAddress) || !ethers__WEBPACK_IMPORTED_MODULE_1__.isAddress(address)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Invalid address format'\n            }, {\n                status: 400\n            });\n        }\n        // Get environment variables\n        const privateKey = process.env.CONTRACT_ADMIN_PRIVATE_KEY;\n        const rpcUrl = process.env.AMOY_RPC_URL || \"https://rpc-amoy.polygon.technology/\";\n        if (!privateKey) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Admin private key not configured'\n            }, {\n                status: 500\n            });\n        }\n        if (!rpcUrl) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'RPC URL not configured'\n            }, {\n                status: 500\n            });\n        }\n        console.log(`Attempting to ${action} address ${address} ${action === 'add' ? 'to' : 'from'} whitelist on token:`, tokenAddress);\n        // Setup provider and signer\n        const provider = new ethers__WEBPACK_IMPORTED_MODULE_2__.JsonRpcProvider(rpcUrl);\n        const signer = new ethers__WEBPACK_IMPORTED_MODULE_3__.Wallet(privateKey, provider);\n        // Get token contract\n        const tokenContract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(tokenAddress, SECURITY_TOKEN_CORE_ABI, signer);\n        // Check current whitelist status\n        const currentlyWhitelisted = await tokenContract.isWhitelisted(address);\n        console.log('Current whitelist status:', currentlyWhitelisted);\n        // Validate action makes sense\n        if (action === 'add' && currentlyWhitelisted) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                message: 'Address is already whitelisted',\n                alreadyInDesiredState: true\n            });\n        }\n        if (action === 'remove' && !currentlyWhitelisted) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                message: 'Address is already not whitelisted',\n                alreadyInDesiredState: true\n            });\n        }\n        // Multiple strategies to handle Amoy testnet issues\n        const strategies = [\n            // Strategy 1: Standard contract call\n            async ()=>{\n                const tx = action === 'add' ? await tokenContract.addToWhitelist(address, {\n                    gasLimit: 300000,\n                    gasPrice: ethers__WEBPACK_IMPORTED_MODULE_5__.parseUnits('30', 'gwei')\n                }) : await tokenContract.removeFromWhitelist(address, {\n                    gasLimit: 300000,\n                    gasPrice: ethers__WEBPACK_IMPORTED_MODULE_5__.parseUnits('30', 'gwei')\n                });\n                return tx.wait();\n            },\n            // Strategy 2: Raw transaction approach\n            async ()=>{\n                const iface = new ethers__WEBPACK_IMPORTED_MODULE_6__.Interface(SECURITY_TOKEN_CORE_ABI);\n                const functionName = action === 'add' ? 'addToWhitelist' : 'removeFromWhitelist';\n                const data = iface.encodeFunctionData(functionName, [\n                    address\n                ]);\n                const nonce = await signer.getNonce();\n                const tx = await signer.sendTransaction({\n                    to: tokenAddress,\n                    data: data,\n                    gasLimit: 300000,\n                    gasPrice: ethers__WEBPACK_IMPORTED_MODULE_5__.parseUnits('30', 'gwei'),\n                    nonce: nonce\n                });\n                return tx.wait();\n            }\n        ];\n        let lastError;\n        for(let i = 0; i < strategies.length; i++){\n            try {\n                console.log(`Attempting whitelist ${action} strategy ${i + 1}...`);\n                const receipt = await strategies[i]();\n                console.log(`Whitelist ${action} successful with strategy ${i + 1}. Tx hash:`, receipt.hash);\n                // Verify the action worked\n                const newWhitelistStatus = await tokenContract.isWhitelisted(address);\n                const expectedStatus = action === 'add';\n                if (newWhitelistStatus === expectedStatus) {\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        success: true,\n                        message: `Address ${action === 'add' ? 'added to' : 'removed from'} whitelist successfully`,\n                        transactionHash: receipt.hash,\n                        blockNumber: receipt.blockNumber,\n                        address: address,\n                        whitelisted: newWhitelistStatus,\n                        strategy: i + 1\n                    });\n                } else {\n                    throw new Error(`Transaction succeeded but whitelist status didn't change as expected`);\n                }\n            } catch (error) {\n                console.error(`Strategy ${i + 1} failed:`, error);\n                lastError = error;\n                // If this is not the last strategy, wait and try the next one\n                if (i < strategies.length - 1) {\n                    await new Promise((resolve)=>setTimeout(resolve, 2000));\n                }\n            }\n        }\n        // All strategies failed\n        console.error('All whitelist strategies failed. Last error:', lastError);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: `Failed to ${action} address ${action === 'add' ? 'to' : 'from'} whitelist after trying multiple methods`,\n            details: lastError?.message || 'Unknown error',\n            suggestion: 'The Amoy testnet may be experiencing issues. Please try again in a few minutes.'\n        }, {\n            status: 500\n        });\n    } catch (error) {\n        console.error('Manage whitelist API error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error',\n            details: error.message\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/admin/manage-whitelist/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/ethers","vendor-chunks/@noble","vendor-chunks/@adraffy","vendor-chunks/aes-js"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fmanage-whitelist%2Froute&page=%2Fapi%2Fadmin%2Fmanage-whitelist%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fmanage-whitelist%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();