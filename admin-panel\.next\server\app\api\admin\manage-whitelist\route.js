/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/admin/manage-whitelist/route";
exports.ids = ["app/api/admin/manage-whitelist/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fmanage-whitelist%2Froute&page=%2Fapi%2Fadmin%2Fmanage-whitelist%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fmanage-whitelist%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fmanage-whitelist%2Froute&page=%2Fapi%2Fadmin%2Fmanage-whitelist%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fmanage-whitelist%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_github_tokendev_newroo_admin_panel_src_app_api_admin_manage_whitelist_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/admin/manage-whitelist/route.ts */ \"(rsc)/./src/app/api/admin/manage-whitelist/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/admin/manage-whitelist/route\",\n        pathname: \"/api/admin/manage-whitelist\",\n        filename: \"route\",\n        bundlePath: \"app/api/admin/manage-whitelist/route\"\n    },\n    resolvedPagePath: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\api\\\\admin\\\\manage-whitelist\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_github_tokendev_newroo_admin_panel_src_app_api_admin_manage_whitelist_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fmanage-whitelist%2Froute&page=%2Fapi%2Fadmin%2Fmanage-whitelist%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fmanage-whitelist%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/admin/manage-whitelist/route.ts":
/*!*****************************************************!*\
  !*** ./src/app/api/admin/manage-whitelist/route.ts ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/address/checks.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/providers/provider-jsonrpc.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/wallet/wallet.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/contract/contract.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/utils/units.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/abi/interface.js\");\n\n\nconst SECURITY_TOKEN_CORE_ABI = [\n    \"function addToWhitelist(address account) external\",\n    \"function removeFromWhitelist(address account) external\",\n    \"function isWhitelisted(address account) external view returns (bool)\"\n];\nasync function POST(request) {\n    try {\n        const { tokenAddress, address, action } = await request.json();\n        if (!tokenAddress) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Token address is required'\n            }, {\n                status: 400\n            });\n        }\n        if (!address) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Address is required'\n            }, {\n                status: 400\n            });\n        }\n        if (!action || ![\n            'add',\n            'remove'\n        ].includes(action)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Action must be \"add\" or \"remove\"'\n            }, {\n                status: 400\n            });\n        }\n        // Validate addresses\n        if (!ethers__WEBPACK_IMPORTED_MODULE_1__.isAddress(tokenAddress) || !ethers__WEBPACK_IMPORTED_MODULE_1__.isAddress(address)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Invalid address format'\n            }, {\n                status: 400\n            });\n        }\n        // Get environment variables\n        const privateKey = process.env.CONTRACT_ADMIN_PRIVATE_KEY;\n        const rpcUrl = process.env.AMOY_RPC_URL || \"https://rpc-amoy.polygon.technology/\";\n        if (!privateKey) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Admin private key not configured'\n            }, {\n                status: 500\n            });\n        }\n        if (!rpcUrl) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'RPC URL not configured'\n            }, {\n                status: 500\n            });\n        }\n        console.log(`Attempting to ${action} address ${address} ${action === 'add' ? 'to' : 'from'} whitelist on token:`, tokenAddress);\n        // Setup provider and signer\n        const provider = new ethers__WEBPACK_IMPORTED_MODULE_2__.JsonRpcProvider(rpcUrl);\n        const signer = new ethers__WEBPACK_IMPORTED_MODULE_3__.Wallet(privateKey, provider);\n        // Get token contract\n        const tokenContract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(tokenAddress, SECURITY_TOKEN_CORE_ABI, signer);\n        // Check current whitelist status\n        const currentlyWhitelisted = await tokenContract.isWhitelisted(address);\n        console.log('Current whitelist status:', currentlyWhitelisted);\n        // Validate action makes sense\n        if (action === 'add' && currentlyWhitelisted) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                message: 'Address is already whitelisted',\n                alreadyInDesiredState: true\n            });\n        }\n        if (action === 'remove' && !currentlyWhitelisted) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                message: 'Address is already not whitelisted',\n                alreadyInDesiredState: true\n            });\n        }\n        // For adding to whitelist, check if identity is registered first\n        if (action === 'add') {\n            try {\n                const isVerified = await tokenContract.isVerified(address);\n                console.log(`Address ${address} verified status:`, isVerified);\n                if (!isVerified) {\n                    console.log('Address not verified, attempting to register identity first...');\n                    // Try to register identity first using the identity API\n                    const identityResponse = await fetch(`${process.env.NEXTAUTH_URL || 'http://localhost:6677'}/api/identity`, {\n                        method: 'POST',\n                        headers: {\n                            'Content-Type': 'application/json'\n                        },\n                        body: JSON.stringify({\n                            action: 'register',\n                            address: address,\n                            country: 'United States' // Default country\n                        })\n                    });\n                    if (!identityResponse.ok) {\n                        const errorData = await identityResponse.json();\n                        console.log('Identity registration failed:', errorData);\n                    // Continue anyway, maybe the identity is registered but verification check failed\n                    } else {\n                        console.log('Identity registration successful');\n                        // Wait a moment for the transaction to be mined\n                        await new Promise((resolve)=>setTimeout(resolve, 2000));\n                    }\n                }\n            } catch (verificationError) {\n                console.log('Verification check failed, continuing with whitelist attempt:', verificationError);\n            // Continue anyway, the verification might work during the actual whitelist call\n            }\n        }\n        // Multiple strategies to handle Amoy testnet issues\n        const strategies = [\n            // Strategy 1: Standard contract call\n            async ()=>{\n                const tx = action === 'add' ? await tokenContract.addToWhitelist(address, {\n                    gasLimit: 300000,\n                    gasPrice: ethers__WEBPACK_IMPORTED_MODULE_5__.parseUnits('30', 'gwei')\n                }) : await tokenContract.removeFromWhitelist(address, {\n                    gasLimit: 300000,\n                    gasPrice: ethers__WEBPACK_IMPORTED_MODULE_5__.parseUnits('30', 'gwei')\n                });\n                return tx.wait();\n            },\n            // Strategy 2: Raw transaction approach\n            async ()=>{\n                const iface = new ethers__WEBPACK_IMPORTED_MODULE_6__.Interface(SECURITY_TOKEN_CORE_ABI);\n                const functionName = action === 'add' ? 'addToWhitelist' : 'removeFromWhitelist';\n                const data = iface.encodeFunctionData(functionName, [\n                    address\n                ]);\n                const nonce = await signer.getNonce();\n                const tx = await signer.sendTransaction({\n                    to: tokenAddress,\n                    data: data,\n                    gasLimit: 300000,\n                    gasPrice: ethers__WEBPACK_IMPORTED_MODULE_5__.parseUnits('30', 'gwei'),\n                    nonce: nonce\n                });\n                return tx.wait();\n            }\n        ];\n        let lastError;\n        for(let i = 0; i < strategies.length; i++){\n            try {\n                console.log(`Attempting whitelist ${action} strategy ${i + 1}...`);\n                const receipt = await strategies[i]();\n                console.log(`Whitelist ${action} successful with strategy ${i + 1}. Tx hash:`, receipt.hash);\n                // Verify the action worked\n                const newWhitelistStatus = await tokenContract.isWhitelisted(address);\n                const expectedStatus = action === 'add';\n                if (newWhitelistStatus === expectedStatus) {\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        success: true,\n                        message: `Address ${action === 'add' ? 'added to' : 'removed from'} whitelist successfully`,\n                        transactionHash: receipt.hash,\n                        blockNumber: receipt.blockNumber,\n                        address: address,\n                        whitelisted: newWhitelistStatus,\n                        strategy: i + 1\n                    });\n                } else {\n                    throw new Error(`Transaction succeeded but whitelist status didn't change as expected`);\n                }\n            } catch (error) {\n                console.error(`Strategy ${i + 1} failed:`, error);\n                lastError = error;\n                // If this is not the last strategy, wait and try the next one\n                if (i < strategies.length - 1) {\n                    await new Promise((resolve)=>setTimeout(resolve, 2000));\n                }\n            }\n        }\n        // All strategies failed\n        console.error('All whitelist strategies failed. Last error:', lastError);\n        // Provide specific error messages based on the error type\n        let errorMessage = `Failed to ${action} address ${action === 'add' ? 'to' : 'from'} whitelist after trying multiple methods`;\n        let suggestion = 'The Amoy testnet may be experiencing issues. Please try again in a few minutes.';\n        if (lastError?.reason) {\n            if (lastError.reason.includes('identity not registered')) {\n                errorMessage = `Identity not registered: The address ${address} must be registered with the identity registry first.`;\n                suggestion = 'Please register the identity first or contact support if this error persists.';\n            } else if (lastError.reason.includes('already whitelisted')) {\n                errorMessage = `Address ${address} is already whitelisted.`;\n                suggestion = 'No action needed - the address is already in the whitelist.';\n            } else if (lastError.reason.includes('address is frozen')) {\n                errorMessage = `Address ${address} is frozen and cannot be whitelisted.`;\n                suggestion = 'Please unfreeze the address first before adding to whitelist.';\n            }\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: errorMessage,\n            details: lastError?.message || 'Unknown error',\n            suggestion: suggestion\n        }, {\n            status: 500\n        });\n    } catch (error) {\n        console.error('Manage whitelist API error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error',\n            details: error.message\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/admin/manage-whitelist/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/ethers","vendor-chunks/@noble","vendor-chunks/@adraffy","vendor-chunks/aes-js"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fmanage-whitelist%2Froute&page=%2Fapi%2Fadmin%2Fmanage-whitelist%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fmanage-whitelist%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();