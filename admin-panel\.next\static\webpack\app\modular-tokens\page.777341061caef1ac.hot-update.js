"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/modular-tokens/page",{

/***/ "(app-pages-browser)/./src/hooks/useModularToken.ts":
/*!**************************************!*\
  !*** ./src/hooks/useModularToken.ts ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useModularToken: () => (/* binding */ useModularToken)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/providers/provider-browser.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/contract/contract.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/utils/units.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/crypto/keccak.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/utils/utf8.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/constants/addresses.js\");\n/* harmony import */ var _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/contracts/SecurityTokenCore.json */ \"(app-pages-browser)/./src/contracts/SecurityTokenCore.json\");\n/* harmony import */ var _contracts_UpgradeManager_json__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contracts/UpgradeManager.json */ \"(app-pages-browser)/./src/contracts/UpgradeManager.json\");\n/* __next_internal_client_entry_do_not_use__ useModularToken auto */ \n\n// Import ABIs - extract the abi property from the JSON artifacts\n\n\n// Extract ABIs from artifacts\nconst SecurityTokenCoreABI = _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_1__.abi;\nconst UpgradeManagerABI = _contracts_UpgradeManager_json__WEBPACK_IMPORTED_MODULE_2__.abi;\n// Contract addresses from environment\nconst SECURITY_TOKEN_CORE_ADDRESS = \"******************************************\";\nconst UPGRADE_MANAGER_ADDRESS = \"0xb7a53dC340C2E5e823002B174629e2a44B8ed815\";\nfunction useModularToken(tokenAddress) {\n    const [provider, setProvider] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [signer, setSigner] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [tokenInfo, setTokenInfo] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [contractAddresses, setContractAddresses] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [userRoles, setUserRoles] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [upgradeInfo, setUpgradeInfo] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [pendingUpgrades, setPendingUpgrades] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [upgradeHistory, setUpgradeHistory] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    // Initialize provider and signer\n    const initializeProvider = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[initializeProvider]\": async ()=>{\n            try {\n                if ( true && window.ethereum) {\n                    const provider = new ethers__WEBPACK_IMPORTED_MODULE_3__.BrowserProvider(window.ethereum);\n                    await provider.send('eth_requestAccounts', []);\n                    const signer = await provider.getSigner();\n                    setProvider(provider);\n                    setSigner(signer);\n                    return {\n                        provider,\n                        signer\n                    };\n                } else {\n                    throw new Error('MetaMask not found');\n                }\n            } catch (error) {\n                console.error('Error initializing provider:', error);\n                setError('Failed to connect to wallet');\n                return null;\n            }\n        }\n    }[\"useModularToken.useCallback[initializeProvider]\"], []);\n    // Load token information\n    const loadTokenInfo = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[loadTokenInfo]\": async ()=>{\n            const contractAddress = tokenAddress || SECURITY_TOKEN_CORE_ADDRESS;\n            if (!provider || !contractAddress) return;\n            try {\n                setLoading(true);\n                const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(contractAddress, SecurityTokenCoreABI, provider);\n                const [name, symbol, version, totalSupply, maxSupply, decimals, paused, metadata] = await Promise.all([\n                    contract.name(),\n                    contract.symbol(),\n                    contract.version(),\n                    contract.totalSupply(),\n                    contract.maxSupply(),\n                    contract.decimals(),\n                    contract.paused(),\n                    contract.getTokenMetadata()\n                ]);\n                console.log('Token contract data:');\n                console.log('- Name:', name);\n                console.log('- Symbol:', symbol);\n                console.log('- Decimals (raw):', decimals);\n                console.log('- Decimals (number):', Number(decimals));\n                console.log('- Total Supply (raw):', totalSupply.toString());\n                console.log('- Max Supply (raw):', maxSupply.toString());\n                console.log('- Metadata (raw):', metadata);\n                console.log('- Token Price:', metadata[0]);\n                console.log('- Bonus Tiers:', metadata[1]);\n                console.log('- Token Details:', metadata[2]);\n                console.log('- Token Image URL:', metadata[3]);\n                const decimalsNumber = Number(decimals);\n                // Format with proper blockchain decimal precision\n                let formattedTotalSupply;\n                let formattedMaxSupply;\n                if (decimalsNumber === 0) {\n                    // For 0 decimals, show as whole numbers\n                    formattedTotalSupply = totalSupply.toString();\n                    formattedMaxSupply = maxSupply.toString();\n                } else {\n                    // For tokens with decimals, use ethers.formatUnits and preserve decimal precision\n                    const totalSupplyFormatted = ethers__WEBPACK_IMPORTED_MODULE_5__.formatUnits(totalSupply, decimalsNumber);\n                    const maxSupplyFormatted = ethers__WEBPACK_IMPORTED_MODULE_5__.formatUnits(maxSupply, decimalsNumber);\n                    // For blockchain tokens, show with appropriate decimal places\n                    // Remove excessive trailing zeros but keep meaningful precision\n                    formattedTotalSupply = parseFloat(totalSupplyFormatted).toFixed(Math.min(decimalsNumber, 6));\n                    formattedMaxSupply = parseFloat(maxSupplyFormatted).toFixed(Math.min(decimalsNumber, 6));\n                    // Remove trailing zeros after decimal point\n                    formattedTotalSupply = formattedTotalSupply.replace(/\\.?0+$/, '');\n                    formattedMaxSupply = formattedMaxSupply.replace(/\\.?0+$/, '');\n                    // Ensure at least .0 for tokens with decimals\n                    if (!formattedTotalSupply.includes('.')) formattedTotalSupply += '.0';\n                    if (!formattedMaxSupply.includes('.')) formattedMaxSupply += '.0';\n                }\n                console.log('- Total Supply (formatted):', formattedTotalSupply);\n                console.log('- Max Supply (formatted):', formattedMaxSupply);\n                setTokenInfo({\n                    name,\n                    symbol,\n                    version,\n                    totalSupply: formattedTotalSupply,\n                    maxSupply: formattedMaxSupply,\n                    decimals: decimalsNumber,\n                    paused,\n                    metadata: {\n                        tokenPrice: metadata[0],\n                        bonusTiers: metadata[1],\n                        tokenDetails: metadata[2],\n                        tokenImageUrl: metadata[3]\n                    }\n                });\n            } catch (error) {\n                console.error('Error loading token info:', error);\n                setError('Failed to load token information');\n            } finally{\n                setLoading(false);\n            }\n        }\n    }[\"useModularToken.useCallback[loadTokenInfo]\"], [\n        provider,\n        tokenAddress\n    ]);\n    // Load contract addresses\n    const loadContractAddresses = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[loadContractAddresses]\": async ()=>{\n            const contractAddress = tokenAddress || SECURITY_TOKEN_CORE_ADDRESS;\n            if (!provider || !contractAddress) return;\n            try {\n                const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(contractAddress, SecurityTokenCoreABI, provider);\n                // Define module IDs (these are keccak256 hashes of the module names)\n                const moduleIds = {\n                    'Identity Manager': ethers__WEBPACK_IMPORTED_MODULE_6__.keccak256(ethers__WEBPACK_IMPORTED_MODULE_7__.toUtf8Bytes(\"IDENTITY_MANAGER\")),\n                    'Compliance Engine': ethers__WEBPACK_IMPORTED_MODULE_6__.keccak256(ethers__WEBPACK_IMPORTED_MODULE_7__.toUtf8Bytes(\"COMPLIANCE_ENGINE\")),\n                    'Transfer Controller': ethers__WEBPACK_IMPORTED_MODULE_6__.keccak256(ethers__WEBPACK_IMPORTED_MODULE_7__.toUtf8Bytes(\"TRANSFER_CONTROLLER\")),\n                    'Agent Manager': ethers__WEBPACK_IMPORTED_MODULE_6__.keccak256(ethers__WEBPACK_IMPORTED_MODULE_7__.toUtf8Bytes(\"AGENT_MANAGER\")),\n                    'Emergency Manager': ethers__WEBPACK_IMPORTED_MODULE_6__.keccak256(ethers__WEBPACK_IMPORTED_MODULE_7__.toUtf8Bytes(\"EMERGENCY_MANAGER\")),\n                    'KYC Claims Module': ethers__WEBPACK_IMPORTED_MODULE_6__.keccak256(ethers__WEBPACK_IMPORTED_MODULE_7__.toUtf8Bytes(\"KYC_CLAIMS_MODULE\"))\n                };\n                console.log('Fetching module addresses...');\n                // Fetch all module addresses\n                const addresses = {\n                    'Token Contract': contractAddress\n                };\n                for (const [moduleName, moduleId] of Object.entries(moduleIds)){\n                    try {\n                        const moduleAddress = await contract.getModule(moduleId);\n                        if (moduleAddress && moduleAddress !== ethers__WEBPACK_IMPORTED_MODULE_8__.ZeroAddress) {\n                            addresses[moduleName] = moduleAddress;\n                            console.log(\"\".concat(moduleName, \": \").concat(moduleAddress));\n                        } else {\n                            console.log(\"\".concat(moduleName, \": Not registered\"));\n                        }\n                    } catch (error) {\n                        console.warn(\"Error fetching \".concat(moduleName, \" address:\"), error);\n                    }\n                }\n                setContractAddresses(addresses);\n            } catch (error) {\n                console.error('Error loading contract addresses:', error);\n            }\n        }\n    }[\"useModularToken.useCallback[loadContractAddresses]\"], [\n        provider,\n        tokenAddress\n    ]);\n    // Load user roles\n    const loadUserRoles = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[loadUserRoles]\": async ()=>{\n            const contractAddress = tokenAddress || SECURITY_TOKEN_CORE_ADDRESS;\n            if (!provider || !signer || !contractAddress) return;\n            try {\n                const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(contractAddress, SecurityTokenCoreABI, provider);\n                const userAddress = await signer.getAddress();\n                // Define role hashes\n                const DEFAULT_ADMIN_ROLE = '0x0000000000000000000000000000000000000000000000000000000000000000';\n                const AGENT_ROLE = ethers__WEBPACK_IMPORTED_MODULE_6__.keccak256(ethers__WEBPACK_IMPORTED_MODULE_7__.toUtf8Bytes(\"AGENT_ROLE\"));\n                const TRANSFER_MANAGER_ROLE = ethers__WEBPACK_IMPORTED_MODULE_6__.keccak256(ethers__WEBPACK_IMPORTED_MODULE_7__.toUtf8Bytes(\"TRANSFER_MANAGER_ROLE\"));\n                const MODULE_MANAGER_ROLE = ethers__WEBPACK_IMPORTED_MODULE_6__.keccak256(ethers__WEBPACK_IMPORTED_MODULE_7__.toUtf8Bytes(\"MODULE_MANAGER_ROLE\"));\n                console.log('Checking roles for user:', userAddress);\n                // Check all roles\n                const roles = {};\n                try {\n                    roles['DEFAULT_ADMIN_ROLE'] = await contract.hasRole(DEFAULT_ADMIN_ROLE, userAddress);\n                    console.log('DEFAULT_ADMIN_ROLE:', roles['DEFAULT_ADMIN_ROLE']);\n                } catch (error) {\n                    console.warn('Error checking DEFAULT_ADMIN_ROLE:', error);\n                    roles['DEFAULT_ADMIN_ROLE'] = false;\n                }\n                try {\n                    roles['AGENT_ROLE'] = await contract.hasRole(AGENT_ROLE, userAddress);\n                    console.log('AGENT_ROLE:', roles['AGENT_ROLE']);\n                } catch (error) {\n                    console.warn('Error checking AGENT_ROLE:', error);\n                    roles['AGENT_ROLE'] = false;\n                }\n                try {\n                    roles['TRANSFER_MANAGER_ROLE'] = await contract.hasRole(TRANSFER_MANAGER_ROLE, userAddress);\n                    console.log('TRANSFER_MANAGER_ROLE:', roles['TRANSFER_MANAGER_ROLE']);\n                } catch (error) {\n                    console.warn('Error checking TRANSFER_MANAGER_ROLE:', error);\n                    roles['TRANSFER_MANAGER_ROLE'] = false;\n                }\n                try {\n                    roles['MODULE_MANAGER_ROLE'] = await contract.hasRole(MODULE_MANAGER_ROLE, userAddress);\n                    console.log('MODULE_MANAGER_ROLE:', roles['MODULE_MANAGER_ROLE']);\n                } catch (error) {\n                    console.warn('Error checking MODULE_MANAGER_ROLE:', error);\n                    roles['MODULE_MANAGER_ROLE'] = false;\n                }\n                setUserRoles(roles);\n            } catch (error) {\n                console.error('Error loading user roles:', error);\n            }\n        }\n    }[\"useModularToken.useCallback[loadUserRoles]\"], [\n        provider,\n        signer,\n        tokenAddress\n    ]);\n    // Load upgrade information\n    const loadUpgradeInfo = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[loadUpgradeInfo]\": async ()=>{\n            if (!provider || !UPGRADE_MANAGER_ADDRESS) return;\n            try {\n                setLoading(true);\n                const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(UPGRADE_MANAGER_ADDRESS, UpgradeManagerABI, provider);\n                const [emergencyModeActive, registeredModules, upgradeDelay, emergencyModeDuration, pendingUpgradeIds] = await Promise.all([\n                    contract.isEmergencyModeActive(),\n                    contract.getRegisteredModules(),\n                    contract.UPGRADE_DELAY(),\n                    contract.EMERGENCY_MODE_DURATION(),\n                    contract.getPendingUpgradeIds()\n                ]);\n                setUpgradeInfo({\n                    emergencyModeActive,\n                    registeredModules,\n                    upgradeDelay: Number(upgradeDelay),\n                    emergencyModeDuration: Number(emergencyModeDuration)\n                });\n                // Load pending upgrades\n                const pendingUpgradesData = await Promise.all(pendingUpgradeIds.map({\n                    \"useModularToken.useCallback[loadUpgradeInfo]\": async (id)=>{\n                        const upgrade = await contract.pendingUpgrades(id);\n                        return {\n                            upgradeId: id,\n                            moduleId: upgrade.moduleId,\n                            proxy: upgrade.proxy,\n                            newImplementation: upgrade.newImplementation,\n                            executeTime: Number(upgrade.executeTime),\n                            executed: upgrade.executed,\n                            cancelled: upgrade.cancelled,\n                            description: upgrade.description\n                        };\n                    }\n                }[\"useModularToken.useCallback[loadUpgradeInfo]\"]));\n                setPendingUpgrades(pendingUpgradesData);\n                // Load upgrade history for SecurityTokenCore\n                const SECURITY_TOKEN_CORE_ID = ethers__WEBPACK_IMPORTED_MODULE_6__.keccak256(ethers__WEBPACK_IMPORTED_MODULE_7__.toUtf8Bytes(\"SECURITY_TOKEN_CORE\"));\n                const history = await contract.getUpgradeHistory(SECURITY_TOKEN_CORE_ID);\n                setUpgradeHistory(history.map({\n                    \"useModularToken.useCallback[loadUpgradeInfo]\": (record)=>({\n                            oldImplementation: record.oldImplementation,\n                            newImplementation: record.newImplementation,\n                            timestamp: Number(record.timestamp),\n                            executor: record.executor,\n                            version: record.version,\n                            isEmergency: record.isEmergency,\n                            description: record.description\n                        })\n                }[\"useModularToken.useCallback[loadUpgradeInfo]\"]));\n            } catch (error) {\n                console.error('Error loading upgrade info:', error);\n                setError('Failed to load upgrade information');\n            } finally{\n                setLoading(false);\n            }\n        }\n    }[\"useModularToken.useCallback[loadUpgradeInfo]\"], [\n        provider\n    ]);\n    // Mint tokens\n    const mintTokens = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[mintTokens]\": async (address, amount)=>{\n            const contractAddress = tokenAddress || SECURITY_TOKEN_CORE_ADDRESS;\n            if (!signer || !contractAddress) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(contractAddress, SecurityTokenCoreABI, signer);\n            try {\n                const decimals = (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.decimals) || 0;\n                const amountWei = ethers__WEBPACK_IMPORTED_MODULE_5__.parseUnits(amount, decimals);\n                const tx = await contract.mint(address, amountWei, {\n                    gasLimit: 400000\n                });\n                return tx.wait();\n            } catch (error) {\n                console.error('Mint tokens error:', error);\n                if (error.code === 'ACTION_REJECTED') {\n                    throw new Error('Transaction was rejected by user');\n                } else if (error.reason) {\n                    throw new Error(\"Contract error: \".concat(error.reason));\n                } else {\n                    throw new Error(\"Failed to mint tokens: \".concat(error.message));\n                }\n            }\n        }\n    }[\"useModularToken.useCallback[mintTokens]\"], [\n        signer,\n        tokenInfo,\n        tokenAddress\n    ]);\n    // Toggle pause state with multiple retry strategies\n    const togglePause = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[togglePause]\": async ()=>{\n            const contractAddress = tokenAddress || SECURITY_TOKEN_CORE_ADDRESS;\n            if (!signer || !contractAddress) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(contractAddress, SecurityTokenCoreABI, signer);\n            const isPausing = !(tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.paused);\n            const functionName = isPausing ? 'pause' : 'unpause';\n            // Try multiple strategies to handle Amoy testnet issues\n            const strategies = [\n                {\n                    \"useModularToken.useCallback[togglePause]\": // Strategy 1: Standard call with high gas\n                    async ()=>{\n                        const tx = isPausing ? await contract.pause({\n                            gasLimit: 500000,\n                            gasPrice: ethers__WEBPACK_IMPORTED_MODULE_5__.parseUnits('30', 'gwei')\n                        }) : await contract.unpause({\n                            gasLimit: 500000,\n                            gasPrice: ethers__WEBPACK_IMPORTED_MODULE_5__.parseUnits('30', 'gwei')\n                        });\n                        return tx.wait();\n                    }\n                }[\"useModularToken.useCallback[togglePause]\"],\n                {\n                    \"useModularToken.useCallback[togglePause]\": // Strategy 2: Raw transaction approach (like the working script)\n                    async ()=>{\n                        const functionSelector = isPausing ? '0x8456cb59' : '0x3f4ba83a';\n                        const nonce = await signer.getNonce();\n                        const tx = await signer.sendTransaction({\n                            to: contractAddress,\n                            data: functionSelector,\n                            gasLimit: 500000,\n                            gasPrice: ethers__WEBPACK_IMPORTED_MODULE_5__.parseUnits('30', 'gwei'),\n                            nonce: nonce\n                        });\n                        return tx.wait();\n                    }\n                }[\"useModularToken.useCallback[togglePause]\"]\n            ];\n            for(let i = 0; i < strategies.length; i++){\n                try {\n                    console.log(\"Attempting \".concat(functionName, \" strategy \").concat(i + 1, \"...\"));\n                    const result = await strategies[i]();\n                    console.log(\"\".concat(functionName, \" successful with strategy \").concat(i + 1));\n                    return result;\n                } catch (error) {\n                    console.error(\"Strategy \".concat(i + 1, \" failed:\"), error);\n                    if (error.code === 'ACTION_REJECTED') {\n                        throw new Error('Transaction was rejected by user');\n                    }\n                    // If this is the last strategy, throw a comprehensive error\n                    if (i === strategies.length - 1) {\n                        var _error_error;\n                        if (error.code === 'UNKNOWN_ERROR' && ((_error_error = error.error) === null || _error_error === void 0 ? void 0 : _error_error.code) === -32603) {\n                            throw new Error(\"Amoy testnet RPC error: The network is experiencing issues. Please try again in a few minutes, or use a different RPC endpoint.\");\n                        } else if (error.reason) {\n                            throw new Error(\"Contract error: \".concat(error.reason));\n                        } else {\n                            throw new Error(\"Failed to \".concat(functionName, \" token after trying multiple methods: \").concat(error.message));\n                        }\n                    }\n                    // Wait a bit before trying the next strategy\n                    await new Promise({\n                        \"useModularToken.useCallback[togglePause]\": (resolve)=>setTimeout(resolve, 1000)\n                    }[\"useModularToken.useCallback[togglePause]\"]);\n                }\n            }\n        }\n    }[\"useModularToken.useCallback[togglePause]\"], [\n        signer,\n        tokenInfo,\n        tokenAddress\n    ]);\n    // Schedule upgrade\n    const scheduleUpgrade = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[scheduleUpgrade]\": async (implementationAddress, description)=>{\n            if (!signer || !UPGRADE_MANAGER_ADDRESS) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(UPGRADE_MANAGER_ADDRESS, UpgradeManagerABI, signer);\n            const SECURITY_TOKEN_CORE_ID = ethers__WEBPACK_IMPORTED_MODULE_6__.keccak256(ethers__WEBPACK_IMPORTED_MODULE_7__.toUtf8Bytes(\"SECURITY_TOKEN_CORE\"));\n            const tx = await contract.scheduleUpgrade(SECURITY_TOKEN_CORE_ID, implementationAddress, description);\n            return tx.wait();\n        }\n    }[\"useModularToken.useCallback[scheduleUpgrade]\"], [\n        signer\n    ]);\n    // Execute upgrade\n    const executeUpgrade = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[executeUpgrade]\": async (upgradeId)=>{\n            if (!signer || !UPGRADE_MANAGER_ADDRESS) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(UPGRADE_MANAGER_ADDRESS, UpgradeManagerABI, signer);\n            const tx = await contract.executeUpgrade(upgradeId);\n            return tx.wait();\n        }\n    }[\"useModularToken.useCallback[executeUpgrade]\"], [\n        signer\n    ]);\n    // Emergency upgrade\n    const emergencyUpgrade = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[emergencyUpgrade]\": async (implementationAddress, description)=>{\n            if (!signer || !UPGRADE_MANAGER_ADDRESS) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(UPGRADE_MANAGER_ADDRESS, UpgradeManagerABI, signer);\n            const SECURITY_TOKEN_CORE_ID = ethers__WEBPACK_IMPORTED_MODULE_6__.keccak256(ethers__WEBPACK_IMPORTED_MODULE_7__.toUtf8Bytes(\"SECURITY_TOKEN_CORE\"));\n            const tx = await contract.emergencyUpgrade(SECURITY_TOKEN_CORE_ID, implementationAddress, description);\n            return tx.wait();\n        }\n    }[\"useModularToken.useCallback[emergencyUpgrade]\"], [\n        signer\n    ]);\n    // Toggle emergency mode\n    const toggleEmergencyMode = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[toggleEmergencyMode]\": async ()=>{\n            if (!signer || !UPGRADE_MANAGER_ADDRESS) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(UPGRADE_MANAGER_ADDRESS, UpgradeManagerABI, signer);\n            const tx = (upgradeInfo === null || upgradeInfo === void 0 ? void 0 : upgradeInfo.emergencyModeActive) ? await contract.deactivateEmergencyMode() : await contract.activateEmergencyMode();\n            return tx.wait();\n        }\n    }[\"useModularToken.useCallback[toggleEmergencyMode]\"], [\n        signer,\n        upgradeInfo\n    ]);\n    // Cancel upgrade\n    const cancelUpgrade = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[cancelUpgrade]\": async (upgradeId)=>{\n            if (!signer || !UPGRADE_MANAGER_ADDRESS) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(UPGRADE_MANAGER_ADDRESS, UpgradeManagerABI, signer);\n            const tx = await contract.cancelUpgrade(upgradeId);\n            return tx.wait();\n        }\n    }[\"useModularToken.useCallback[cancelUpgrade]\"], [\n        signer\n    ]);\n    // Update token price (now using direct updateTokenPrice function)\n    const updateTokenPrice = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[updateTokenPrice]\": async (newPrice)=>{\n            const contractAddress = tokenAddress || SECURITY_TOKEN_CORE_ADDRESS;\n            if (!signer || !contractAddress) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(contractAddress, SecurityTokenCoreABI, signer);\n            try {\n                // First check if user has admin role\n                const DEFAULT_ADMIN_ROLE = '0x0000000000000000000000000000000000000000000000000000000000000000';\n                const userAddress = await signer.getAddress();\n                const hasAdminRole = await contract.hasRole(DEFAULT_ADMIN_ROLE, userAddress);\n                if (!hasAdminRole) {\n                    throw new Error(\"Wallet \".concat(userAddress, \" does not have DEFAULT_ADMIN_ROLE on this token contract\"));\n                }\n                console.log('Updating token price from wallet:', userAddress);\n                console.log('New price:', newPrice);\n                console.log('Contract address:', contractAddress);\n                // Try direct updateTokenPrice first (for upgraded contracts)\n                let gasEstimate;\n                try {\n                    gasEstimate = await contract.updateTokenPrice.estimateGas(newPrice);\n                    console.log('Gas estimate (direct):', gasEstimate.toString());\n                    // Execute with estimated gas + buffer\n                    const gasLimit = gasEstimate + gasEstimate / 10n; // Add 10% buffer\n                    const tx = await contract.updateTokenPrice(newPrice, {\n                        gasLimit: gasLimit,\n                        gasPrice: ethers__WEBPACK_IMPORTED_MODULE_5__.parseUnits('30', 'gwei')\n                    });\n                    console.log('Transaction sent (direct):', tx.hash);\n                    return tx.wait();\n                } catch (directError) {\n                    console.log('Direct updateTokenPrice failed, trying updateTokenMetadata fallback:', directError.message);\n                    // Fallback to updateTokenMetadata for older contracts\n                    const currentMetadata = await contract.getTokenMetadata();\n                    const currentBonusTiers = currentMetadata[1];\n                    const currentDetails = currentMetadata[2];\n                    gasEstimate = await contract.updateTokenMetadata.estimateGas(newPrice, currentBonusTiers, currentDetails);\n                    console.log('Gas estimate (fallback):', gasEstimate.toString());\n                    const gasLimit = gasEstimate + gasEstimate / 10n;\n                    const tx = await contract.updateTokenMetadata(newPrice, currentBonusTiers, currentDetails, {\n                        gasLimit: gasLimit,\n                        gasPrice: ethers__WEBPACK_IMPORTED_MODULE_5__.parseUnits('30', 'gwei')\n                    });\n                    console.log('Transaction sent (fallback):', tx.hash);\n                    return tx.wait();\n                }\n            } catch (error) {\n                console.error('Update token price error:', error);\n                if (error.code === 'ACTION_REJECTED') {\n                    throw new Error('Transaction was rejected by user');\n                } else if (error.reason) {\n                    throw new Error(\"Contract error: \".concat(error.reason));\n                } else if (error.message.includes('Gas estimation failed')) {\n                    throw error; // Re-throw gas estimation errors as-is\n                } else {\n                    throw new Error(\"Failed to update token price: \".concat(error.message));\n                }\n            }\n        }\n    }[\"useModularToken.useCallback[updateTokenPrice]\"], [\n        signer,\n        tokenAddress\n    ]);\n    // Update bonus tiers (using updateTokenMetadata for compatibility)\n    const updateBonusTiers = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[updateBonusTiers]\": async (newBonusTiers)=>{\n            const contractAddress = tokenAddress || SECURITY_TOKEN_CORE_ADDRESS;\n            if (!signer || !contractAddress) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(contractAddress, SecurityTokenCoreABI, signer);\n            try {\n                // Get current metadata to preserve other fields\n                const currentMetadata = await contract.getTokenMetadata();\n                const currentPrice = currentMetadata[0];\n                const currentDetails = currentMetadata[2];\n                const tx = await contract.updateTokenMetadata(currentPrice, newBonusTiers, currentDetails, {\n                    gasLimit: 300000\n                });\n                return tx.wait();\n            } catch (error) {\n                console.error('Update bonus tiers error:', error);\n                if (error.code === 'ACTION_REJECTED') {\n                    throw new Error('Transaction was rejected by user');\n                } else if (error.reason) {\n                    throw new Error(\"Contract error: \".concat(error.reason));\n                } else {\n                    throw new Error(\"Failed to update bonus tiers: \".concat(error.message));\n                }\n            }\n        }\n    }[\"useModularToken.useCallback[updateBonusTiers]\"], [\n        signer,\n        tokenAddress\n    ]);\n    // Update max supply\n    const updateMaxSupply = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[updateMaxSupply]\": async (newMaxSupply)=>{\n            const contractAddress = tokenAddress || SECURITY_TOKEN_CORE_ADDRESS;\n            if (!signer || !contractAddress) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(contractAddress, SecurityTokenCoreABI, signer);\n            try {\n                const maxSupplyWei = ethers__WEBPACK_IMPORTED_MODULE_5__.parseUnits(newMaxSupply, (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.decimals) || 0);\n                const tx = await contract.updateMaxSupply(maxSupplyWei, {\n                    gasLimit: 300000\n                });\n                return tx.wait();\n            } catch (error) {\n                console.error('Update max supply error:', error);\n                if (error.code === 'ACTION_REJECTED') {\n                    throw new Error('Transaction was rejected by user');\n                } else if (error.reason) {\n                    throw new Error(\"Contract error: \".concat(error.reason));\n                } else {\n                    throw new Error(\"Failed to update max supply: \".concat(error.message));\n                }\n            }\n        }\n    }[\"useModularToken.useCallback[updateMaxSupply]\"], [\n        signer,\n        tokenInfo,\n        tokenAddress\n    ]);\n    // Add to whitelist with identity registration\n    const addToWhitelist = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[addToWhitelist]\": async (address)=>{\n            const contractAddress = tokenAddress || SECURITY_TOKEN_CORE_ADDRESS;\n            if (!signer || !contractAddress) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(contractAddress, SecurityTokenCoreABI, signer);\n            try {\n                // First check if the address is already verified/registered\n                const isVerified = await contract.isVerified(address);\n                if (!isVerified) {\n                    // Need to register identity first - use the API approach for this\n                    throw new Error('Address must be registered first. Please use the API method or register the identity manually.');\n                }\n                // Now try to add to whitelist\n                const tx = await contract.addToWhitelist(address, {\n                    gasLimit: 400000,\n                    gasPrice: ethers__WEBPACK_IMPORTED_MODULE_5__.parseUnits('30', 'gwei')\n                });\n                return tx.wait();\n            } catch (error) {\n                console.error('Add to whitelist error:', error);\n                if (error.code === 'ACTION_REJECTED') {\n                    throw new Error('Transaction was rejected by user');\n                } else if (error.reason) {\n                    throw new Error(\"Contract error: \".concat(error.reason));\n                } else if (error.message.includes('identity not registered')) {\n                    throw new Error('Address must be registered first. Please use the API method which handles registration automatically.');\n                } else {\n                    throw new Error(\"Failed to add address to whitelist: \".concat(error.message));\n                }\n            }\n        }\n    }[\"useModularToken.useCallback[addToWhitelist]\"], [\n        signer,\n        tokenAddress\n    ]);\n    // Remove from whitelist\n    const removeFromWhitelist = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[removeFromWhitelist]\": async (address)=>{\n            const contractAddress = tokenAddress || SECURITY_TOKEN_CORE_ADDRESS;\n            if (!signer || !contractAddress) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(contractAddress, SecurityTokenCoreABI, signer);\n            try {\n                const tx = await contract.removeFromWhitelist(address, {\n                    gasLimit: 300000\n                });\n                return tx.wait();\n            } catch (error) {\n                console.error('Remove from whitelist error:', error);\n                if (error.code === 'ACTION_REJECTED') {\n                    throw new Error('Transaction was rejected by user');\n                } else if (error.reason) {\n                    throw new Error(\"Contract error: \".concat(error.reason));\n                } else {\n                    throw new Error(\"Failed to remove address from whitelist: \".concat(error.message));\n                }\n            }\n        }\n    }[\"useModularToken.useCallback[removeFromWhitelist]\"], [\n        signer,\n        tokenAddress\n    ]);\n    // Refresh all data\n    const refreshData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[refreshData]\": async ()=>{\n            await Promise.all([\n                loadTokenInfo(),\n                loadContractAddresses(),\n                loadUserRoles(),\n                loadUpgradeInfo()\n            ]);\n        }\n    }[\"useModularToken.useCallback[refreshData]\"], [\n        loadTokenInfo,\n        loadContractAddresses,\n        loadUserRoles,\n        loadUpgradeInfo\n    ]);\n    // Initialize on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useModularToken.useEffect\": ()=>{\n            initializeProvider();\n        }\n    }[\"useModularToken.useEffect\"], [\n        initializeProvider\n    ]);\n    // Load data when provider is ready\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useModularToken.useEffect\": ()=>{\n            if (provider && signer) {\n                refreshData();\n            }\n        }\n    }[\"useModularToken.useEffect\"], [\n        provider,\n        signer,\n        refreshData\n    ]);\n    return {\n        // State\n        provider,\n        signer,\n        tokenInfo,\n        contractAddresses,\n        userRoles,\n        upgradeInfo,\n        pendingUpgrades,\n        upgradeHistory,\n        loading,\n        error,\n        // Actions\n        initializeProvider,\n        loadTokenInfo,\n        loadContractAddresses,\n        loadUserRoles,\n        loadUpgradeInfo,\n        mintTokens,\n        togglePause,\n        scheduleUpgrade,\n        executeUpgrade,\n        emergencyUpgrade,\n        toggleEmergencyMode,\n        cancelUpgrade,\n        refreshData,\n        // Admin Functions\n        updateTokenPrice,\n        updateBonusTiers,\n        updateMaxSupply,\n        addToWhitelist,\n        removeFromWhitelist,\n        // Utilities\n        setError,\n        setLoading\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useModularToken.ts\n"));

/***/ })

});