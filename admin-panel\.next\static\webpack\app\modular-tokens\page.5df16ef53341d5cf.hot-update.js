"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/modular-tokens/page",{

/***/ "(app-pages-browser)/./src/hooks/useModularToken.ts":
/*!**************************************!*\
  !*** ./src/hooks/useModularToken.ts ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useModularToken: () => (/* binding */ useModularToken)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/providers/provider-browser.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/contract/contract.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/utils/units.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/crypto/keccak.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/utils/utf8.js\");\n/* harmony import */ var _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/contracts/SecurityTokenCore.json */ \"(app-pages-browser)/./src/contracts/SecurityTokenCore.json\");\n/* harmony import */ var _contracts_UpgradeManager_json__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contracts/UpgradeManager.json */ \"(app-pages-browser)/./src/contracts/UpgradeManager.json\");\n/* __next_internal_client_entry_do_not_use__ useModularToken auto */ \n\n// Import ABIs - extract the abi property from the JSON artifacts\n\n\n// Extract ABIs from artifacts\nconst SecurityTokenCoreABI = _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_1__.abi;\nconst UpgradeManagerABI = _contracts_UpgradeManager_json__WEBPACK_IMPORTED_MODULE_2__.abi;\n// Contract addresses from environment\nconst SECURITY_TOKEN_CORE_ADDRESS = \"******************************************\";\nconst UPGRADE_MANAGER_ADDRESS = \"0xb7a53dC340C2E5e823002B174629e2a44B8ed815\";\nfunction useModularToken(tokenAddress) {\n    const [provider, setProvider] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [signer, setSigner] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [tokenInfo, setTokenInfo] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [upgradeInfo, setUpgradeInfo] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [pendingUpgrades, setPendingUpgrades] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [upgradeHistory, setUpgradeHistory] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    // Initialize provider and signer\n    const initializeProvider = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[initializeProvider]\": async ()=>{\n            try {\n                if ( true && window.ethereum) {\n                    const provider = new ethers__WEBPACK_IMPORTED_MODULE_3__.BrowserProvider(window.ethereum);\n                    await provider.send('eth_requestAccounts', []);\n                    const signer = await provider.getSigner();\n                    setProvider(provider);\n                    setSigner(signer);\n                    return {\n                        provider,\n                        signer\n                    };\n                } else {\n                    throw new Error('MetaMask not found');\n                }\n            } catch (error) {\n                console.error('Error initializing provider:', error);\n                setError('Failed to connect to wallet');\n                return null;\n            }\n        }\n    }[\"useModularToken.useCallback[initializeProvider]\"], []);\n    // Load token information\n    const loadTokenInfo = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[loadTokenInfo]\": async ()=>{\n            const contractAddress = tokenAddress || SECURITY_TOKEN_CORE_ADDRESS;\n            if (!provider || !contractAddress) return;\n            try {\n                setLoading(true);\n                const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(contractAddress, SecurityTokenCoreABI, provider);\n                const [name, symbol, version, totalSupply, maxSupply, decimals, paused, metadata] = await Promise.all([\n                    contract.name(),\n                    contract.symbol(),\n                    contract.version(),\n                    contract.totalSupply(),\n                    contract.maxSupply(),\n                    contract.decimals(),\n                    contract.paused(),\n                    contract.getTokenMetadata()\n                ]);\n                setTokenInfo({\n                    name,\n                    symbol,\n                    version,\n                    totalSupply: ethers__WEBPACK_IMPORTED_MODULE_5__.formatUnits(totalSupply, decimals),\n                    maxSupply: ethers__WEBPACK_IMPORTED_MODULE_5__.formatUnits(maxSupply, decimals),\n                    decimals,\n                    paused,\n                    metadata: {\n                        tokenPrice: metadata[0],\n                        bonusTiers: metadata[1],\n                        tokenDetails: metadata[2],\n                        tokenImageUrl: metadata[3]\n                    }\n                });\n            } catch (error) {\n                console.error('Error loading token info:', error);\n                setError('Failed to load token information');\n            } finally{\n                setLoading(false);\n            }\n        }\n    }[\"useModularToken.useCallback[loadTokenInfo]\"], [\n        provider,\n        tokenAddress\n    ]);\n    // Load upgrade information\n    const loadUpgradeInfo = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[loadUpgradeInfo]\": async ()=>{\n            if (!provider || !UPGRADE_MANAGER_ADDRESS) return;\n            try {\n                setLoading(true);\n                const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(UPGRADE_MANAGER_ADDRESS, UpgradeManagerABI, provider);\n                const [emergencyModeActive, registeredModules, upgradeDelay, emergencyModeDuration, pendingUpgradeIds] = await Promise.all([\n                    contract.isEmergencyModeActive(),\n                    contract.getRegisteredModules(),\n                    contract.UPGRADE_DELAY(),\n                    contract.EMERGENCY_MODE_DURATION(),\n                    contract.getPendingUpgradeIds()\n                ]);\n                setUpgradeInfo({\n                    emergencyModeActive,\n                    registeredModules,\n                    upgradeDelay: Number(upgradeDelay),\n                    emergencyModeDuration: Number(emergencyModeDuration)\n                });\n                // Load pending upgrades\n                const pendingUpgradesData = await Promise.all(pendingUpgradeIds.map({\n                    \"useModularToken.useCallback[loadUpgradeInfo]\": async (id)=>{\n                        const upgrade = await contract.pendingUpgrades(id);\n                        return {\n                            upgradeId: id,\n                            moduleId: upgrade.moduleId,\n                            proxy: upgrade.proxy,\n                            newImplementation: upgrade.newImplementation,\n                            executeTime: Number(upgrade.executeTime),\n                            executed: upgrade.executed,\n                            cancelled: upgrade.cancelled,\n                            description: upgrade.description\n                        };\n                    }\n                }[\"useModularToken.useCallback[loadUpgradeInfo]\"]));\n                setPendingUpgrades(pendingUpgradesData);\n                // Load upgrade history for SecurityTokenCore\n                const SECURITY_TOKEN_CORE_ID = ethers__WEBPACK_IMPORTED_MODULE_6__.keccak256(ethers__WEBPACK_IMPORTED_MODULE_7__.toUtf8Bytes(\"SECURITY_TOKEN_CORE\"));\n                const history = await contract.getUpgradeHistory(SECURITY_TOKEN_CORE_ID);\n                setUpgradeHistory(history.map({\n                    \"useModularToken.useCallback[loadUpgradeInfo]\": (record)=>({\n                            oldImplementation: record.oldImplementation,\n                            newImplementation: record.newImplementation,\n                            timestamp: Number(record.timestamp),\n                            executor: record.executor,\n                            version: record.version,\n                            isEmergency: record.isEmergency,\n                            description: record.description\n                        })\n                }[\"useModularToken.useCallback[loadUpgradeInfo]\"]));\n            } catch (error) {\n                console.error('Error loading upgrade info:', error);\n                setError('Failed to load upgrade information');\n            } finally{\n                setLoading(false);\n            }\n        }\n    }[\"useModularToken.useCallback[loadUpgradeInfo]\"], [\n        provider\n    ]);\n    // Mint tokens\n    const mintTokens = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[mintTokens]\": async (address, amount)=>{\n            const contractAddress = tokenAddress || SECURITY_TOKEN_CORE_ADDRESS;\n            if (!signer || !contractAddress) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(contractAddress, SecurityTokenCoreABI, signer);\n            try {\n                const decimals = (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.decimals) || 0;\n                const amountWei = ethers__WEBPACK_IMPORTED_MODULE_5__.parseUnits(amount, decimals);\n                const tx = await contract.mint(address, amountWei, {\n                    gasLimit: 400000\n                });\n                return tx.wait();\n            } catch (error) {\n                console.error('Mint tokens error:', error);\n                if (error.code === 'ACTION_REJECTED') {\n                    throw new Error('Transaction was rejected by user');\n                } else if (error.reason) {\n                    throw new Error(\"Contract error: \".concat(error.reason));\n                } else {\n                    throw new Error(\"Failed to mint tokens: \".concat(error.message));\n                }\n            }\n        }\n    }[\"useModularToken.useCallback[mintTokens]\"], [\n        signer,\n        tokenInfo,\n        tokenAddress\n    ]);\n    // Toggle pause state with multiple retry strategies\n    const togglePause = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[togglePause]\": async ()=>{\n            const contractAddress = tokenAddress || SECURITY_TOKEN_CORE_ADDRESS;\n            if (!signer || !contractAddress) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(contractAddress, SecurityTokenCoreABI, signer);\n            const isPausing = !(tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.paused);\n            const functionName = isPausing ? 'pause' : 'unpause';\n            // Try multiple strategies to handle Amoy testnet issues\n            const strategies = [\n                {\n                    \"useModularToken.useCallback[togglePause]\": // Strategy 1: Standard call with high gas\n                    async ()=>{\n                        const tx = isPausing ? await contract.pause({\n                            gasLimit: 500000,\n                            gasPrice: ethers__WEBPACK_IMPORTED_MODULE_5__.parseUnits('30', 'gwei')\n                        }) : await contract.unpause({\n                            gasLimit: 500000,\n                            gasPrice: ethers__WEBPACK_IMPORTED_MODULE_5__.parseUnits('30', 'gwei')\n                        });\n                        return tx.wait();\n                    }\n                }[\"useModularToken.useCallback[togglePause]\"],\n                {\n                    \"useModularToken.useCallback[togglePause]\": // Strategy 2: Raw transaction approach (like the working script)\n                    async ()=>{\n                        const functionSelector = isPausing ? '0x8456cb59' : '0x3f4ba83a';\n                        const nonce = await signer.getNonce();\n                        const tx = await signer.sendTransaction({\n                            to: contractAddress,\n                            data: functionSelector,\n                            gasLimit: 500000,\n                            gasPrice: ethers__WEBPACK_IMPORTED_MODULE_5__.parseUnits('30', 'gwei'),\n                            nonce: nonce\n                        });\n                        return tx.wait();\n                    }\n                }[\"useModularToken.useCallback[togglePause]\"]\n            ];\n            for(let i = 0; i < strategies.length; i++){\n                try {\n                    console.log(\"Attempting \".concat(functionName, \" strategy \").concat(i + 1, \"...\"));\n                    const result = await strategies[i]();\n                    console.log(\"\".concat(functionName, \" successful with strategy \").concat(i + 1));\n                    return result;\n                } catch (error) {\n                    console.error(\"Strategy \".concat(i + 1, \" failed:\"), error);\n                    if (error.code === 'ACTION_REJECTED') {\n                        throw new Error('Transaction was rejected by user');\n                    }\n                    // If this is the last strategy, throw a comprehensive error\n                    if (i === strategies.length - 1) {\n                        var _error_error;\n                        if (error.code === 'UNKNOWN_ERROR' && ((_error_error = error.error) === null || _error_error === void 0 ? void 0 : _error_error.code) === -32603) {\n                            throw new Error(\"Amoy testnet RPC error: The network is experiencing issues. Please try again in a few minutes, or use a different RPC endpoint.\");\n                        } else if (error.reason) {\n                            throw new Error(\"Contract error: \".concat(error.reason));\n                        } else {\n                            throw new Error(\"Failed to \".concat(functionName, \" token after trying multiple methods: \").concat(error.message));\n                        }\n                    }\n                    // Wait a bit before trying the next strategy\n                    await new Promise({\n                        \"useModularToken.useCallback[togglePause]\": (resolve)=>setTimeout(resolve, 1000)\n                    }[\"useModularToken.useCallback[togglePause]\"]);\n                }\n            }\n        }\n    }[\"useModularToken.useCallback[togglePause]\"], [\n        signer,\n        tokenInfo,\n        tokenAddress\n    ]);\n    // Schedule upgrade\n    const scheduleUpgrade = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[scheduleUpgrade]\": async (implementationAddress, description)=>{\n            if (!signer || !UPGRADE_MANAGER_ADDRESS) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(UPGRADE_MANAGER_ADDRESS, UpgradeManagerABI, signer);\n            const SECURITY_TOKEN_CORE_ID = ethers__WEBPACK_IMPORTED_MODULE_6__.keccak256(ethers__WEBPACK_IMPORTED_MODULE_7__.toUtf8Bytes(\"SECURITY_TOKEN_CORE\"));\n            const tx = await contract.scheduleUpgrade(SECURITY_TOKEN_CORE_ID, implementationAddress, description);\n            return tx.wait();\n        }\n    }[\"useModularToken.useCallback[scheduleUpgrade]\"], [\n        signer\n    ]);\n    // Execute upgrade\n    const executeUpgrade = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[executeUpgrade]\": async (upgradeId)=>{\n            if (!signer || !UPGRADE_MANAGER_ADDRESS) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(UPGRADE_MANAGER_ADDRESS, UpgradeManagerABI, signer);\n            const tx = await contract.executeUpgrade(upgradeId);\n            return tx.wait();\n        }\n    }[\"useModularToken.useCallback[executeUpgrade]\"], [\n        signer\n    ]);\n    // Emergency upgrade\n    const emergencyUpgrade = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[emergencyUpgrade]\": async (implementationAddress, description)=>{\n            if (!signer || !UPGRADE_MANAGER_ADDRESS) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(UPGRADE_MANAGER_ADDRESS, UpgradeManagerABI, signer);\n            const SECURITY_TOKEN_CORE_ID = ethers__WEBPACK_IMPORTED_MODULE_6__.keccak256(ethers__WEBPACK_IMPORTED_MODULE_7__.toUtf8Bytes(\"SECURITY_TOKEN_CORE\"));\n            const tx = await contract.emergencyUpgrade(SECURITY_TOKEN_CORE_ID, implementationAddress, description);\n            return tx.wait();\n        }\n    }[\"useModularToken.useCallback[emergencyUpgrade]\"], [\n        signer\n    ]);\n    // Toggle emergency mode\n    const toggleEmergencyMode = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[toggleEmergencyMode]\": async ()=>{\n            if (!signer || !UPGRADE_MANAGER_ADDRESS) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(UPGRADE_MANAGER_ADDRESS, UpgradeManagerABI, signer);\n            const tx = (upgradeInfo === null || upgradeInfo === void 0 ? void 0 : upgradeInfo.emergencyModeActive) ? await contract.deactivateEmergencyMode() : await contract.activateEmergencyMode();\n            return tx.wait();\n        }\n    }[\"useModularToken.useCallback[toggleEmergencyMode]\"], [\n        signer,\n        upgradeInfo\n    ]);\n    // Cancel upgrade\n    const cancelUpgrade = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[cancelUpgrade]\": async (upgradeId)=>{\n            if (!signer || !UPGRADE_MANAGER_ADDRESS) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(UPGRADE_MANAGER_ADDRESS, UpgradeManagerABI, signer);\n            const tx = await contract.cancelUpgrade(upgradeId);\n            return tx.wait();\n        }\n    }[\"useModularToken.useCallback[cancelUpgrade]\"], [\n        signer\n    ]);\n    // Update token price\n    const updateTokenPrice = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[updateTokenPrice]\": async (newPrice)=>{\n            const contractAddress = tokenAddress || SECURITY_TOKEN_CORE_ADDRESS;\n            if (!signer || !contractAddress) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(contractAddress, SecurityTokenCoreABI, signer);\n            try {\n                const tx = await contract.updateTokenPrice(newPrice, {\n                    gasLimit: 300000\n                });\n                return tx.wait();\n            } catch (error) {\n                console.error('Update token price error:', error);\n                if (error.code === 'ACTION_REJECTED') {\n                    throw new Error('Transaction was rejected by user');\n                } else if (error.reason) {\n                    throw new Error(\"Contract error: \".concat(error.reason));\n                } else {\n                    throw new Error(\"Failed to update token price: \".concat(error.message));\n                }\n            }\n        }\n    }[\"useModularToken.useCallback[updateTokenPrice]\"], [\n        signer,\n        tokenAddress\n    ]);\n    // Update bonus tiers\n    const updateBonusTiers = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[updateBonusTiers]\": async (newBonusTiers)=>{\n            const contractAddress = tokenAddress || SECURITY_TOKEN_CORE_ADDRESS;\n            if (!signer || !contractAddress) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(contractAddress, SecurityTokenCoreABI, signer);\n            try {\n                const tx = await contract.updateBonusTiers(newBonusTiers, {\n                    gasLimit: 300000\n                });\n                return tx.wait();\n            } catch (error) {\n                console.error('Update bonus tiers error:', error);\n                if (error.code === 'ACTION_REJECTED') {\n                    throw new Error('Transaction was rejected by user');\n                } else if (error.reason) {\n                    throw new Error(\"Contract error: \".concat(error.reason));\n                } else {\n                    throw new Error(\"Failed to update bonus tiers: \".concat(error.message));\n                }\n            }\n        }\n    }[\"useModularToken.useCallback[updateBonusTiers]\"], [\n        signer,\n        tokenAddress\n    ]);\n    // Update max supply\n    const updateMaxSupply = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[updateMaxSupply]\": async (newMaxSupply)=>{\n            const contractAddress = tokenAddress || SECURITY_TOKEN_CORE_ADDRESS;\n            if (!signer || !contractAddress) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(contractAddress, SecurityTokenCoreABI, signer);\n            try {\n                const maxSupplyWei = ethers__WEBPACK_IMPORTED_MODULE_5__.parseUnits(newMaxSupply, (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.decimals) || 0);\n                const tx = await contract.updateMaxSupply(maxSupplyWei, {\n                    gasLimit: 300000\n                });\n                return tx.wait();\n            } catch (error) {\n                console.error('Update max supply error:', error);\n                if (error.code === 'ACTION_REJECTED') {\n                    throw new Error('Transaction was rejected by user');\n                } else if (error.reason) {\n                    throw new Error(\"Contract error: \".concat(error.reason));\n                } else {\n                    throw new Error(\"Failed to update max supply: \".concat(error.message));\n                }\n            }\n        }\n    }[\"useModularToken.useCallback[updateMaxSupply]\"], [\n        signer,\n        tokenInfo,\n        tokenAddress\n    ]);\n    // Add to whitelist\n    const addToWhitelist = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[addToWhitelist]\": async (address)=>{\n            const contractAddress = tokenAddress || SECURITY_TOKEN_CORE_ADDRESS;\n            if (!signer || !contractAddress) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(contractAddress, SecurityTokenCoreABI, signer);\n            try {\n                const tx = await contract.addToWhitelist(address, {\n                    gasLimit: 300000\n                });\n                return tx.wait();\n            } catch (error) {\n                console.error('Add to whitelist error:', error);\n                if (error.code === 'ACTION_REJECTED') {\n                    throw new Error('Transaction was rejected by user');\n                } else if (error.reason) {\n                    throw new Error(\"Contract error: \".concat(error.reason));\n                } else {\n                    throw new Error(\"Failed to add address to whitelist: \".concat(error.message));\n                }\n            }\n        }\n    }[\"useModularToken.useCallback[addToWhitelist]\"], [\n        signer,\n        tokenAddress\n    ]);\n    // Remove from whitelist\n    const removeFromWhitelist = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[removeFromWhitelist]\": async (address)=>{\n            const contractAddress = tokenAddress || SECURITY_TOKEN_CORE_ADDRESS;\n            if (!signer || !contractAddress) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(contractAddress, SecurityTokenCoreABI, signer);\n            try {\n                const tx = await contract.removeFromWhitelist(address, {\n                    gasLimit: 300000\n                });\n                return tx.wait();\n            } catch (error) {\n                console.error('Remove from whitelist error:', error);\n                if (error.code === 'ACTION_REJECTED') {\n                    throw new Error('Transaction was rejected by user');\n                } else if (error.reason) {\n                    throw new Error(\"Contract error: \".concat(error.reason));\n                } else {\n                    throw new Error(\"Failed to remove address from whitelist: \".concat(error.message));\n                }\n            }\n        }\n    }[\"useModularToken.useCallback[removeFromWhitelist]\"], [\n        signer\n    ]);\n    // Refresh all data\n    const refreshData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[refreshData]\": async ()=>{\n            await Promise.all([\n                loadTokenInfo(),\n                loadUpgradeInfo()\n            ]);\n        }\n    }[\"useModularToken.useCallback[refreshData]\"], [\n        loadTokenInfo,\n        loadUpgradeInfo\n    ]);\n    // Initialize on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useModularToken.useEffect\": ()=>{\n            initializeProvider();\n        }\n    }[\"useModularToken.useEffect\"], [\n        initializeProvider\n    ]);\n    // Load data when provider is ready\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useModularToken.useEffect\": ()=>{\n            if (provider && signer) {\n                refreshData();\n            }\n        }\n    }[\"useModularToken.useEffect\"], [\n        provider,\n        signer,\n        refreshData\n    ]);\n    return {\n        // State\n        provider,\n        signer,\n        tokenInfo,\n        upgradeInfo,\n        pendingUpgrades,\n        upgradeHistory,\n        loading,\n        error,\n        // Actions\n        initializeProvider,\n        loadTokenInfo,\n        loadUpgradeInfo,\n        mintTokens,\n        togglePause,\n        scheduleUpgrade,\n        executeUpgrade,\n        emergencyUpgrade,\n        toggleEmergencyMode,\n        cancelUpgrade,\n        refreshData,\n        // Admin Functions\n        updateTokenPrice,\n        updateBonusTiers,\n        updateMaxSupply,\n        addToWhitelist,\n        removeFromWhitelist,\n        // Utilities\n        setError,\n        setLoading\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useModularToken.ts\n"));

/***/ })

});