"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/modular-tokens/[address]/page",{

/***/ "(app-pages-browser)/./src/app/modular-tokens/[address]/page.tsx":
/*!***************************************************!*\
  !*** ./src/app/modular-tokens/[address]/page.tsx ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ModularTokenDetailsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/contract/contract.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/utils/units.js\");\n/* harmony import */ var _hooks_useModularToken__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../hooks/useModularToken */ \"(app-pages-browser)/./src/hooks/useModularToken.ts\");\n/* harmony import */ var _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../contracts/SecurityTokenCore.json */ \"(app-pages-browser)/./src/contracts/SecurityTokenCore.json\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n// Import custom hook\n\n// Import ABI\n\n// Extract ABI from artifact\nconst SecurityTokenCoreABI = _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_4__.abi;\nfunction ModularTokenDetailsPage() {\n    var _tokenInfo_metadata, _tokenInfo_metadata1, _tokenInfo_metadata2, _tokenInfo_metadata3, _tokenInfo_metadata4;\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const tokenAddress = params.address;\n    // Use the custom hook for modular token functionality\n    const { provider, signer, tokenInfo, contractAddresses, userRoles, upgradeInfo, pendingUpgrades, upgradeHistory, loading, error, initializeProvider, mintTokens, togglePause, scheduleUpgrade, toggleEmergencyMode, refreshData, setError, updateTokenPrice, updateBonusTiers, updateMaxSupply, addToWhitelist, removeFromWhitelist } = (0,_hooks_useModularToken__WEBPACK_IMPORTED_MODULE_3__.useModularToken)(tokenAddress); // Pass the token address to the hook\n    // Local state for UI\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [mintAmount, setMintAmount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [mintAddress, setMintAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [upgradeDescription, setUpgradeDescription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [newImplementationAddress, setNewImplementationAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [actionLoading, setActionLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Helper function to update frozen amounts in localStorage\n    const updateFrozenAmounts = (userAddress, amount, operation)=>{\n        if (!tokenAddress) return;\n        const localStorageKey = \"frozen_tokens_\".concat(tokenAddress);\n        let frozenAmounts = {};\n        try {\n            const storedData = localStorage.getItem(localStorageKey);\n            if (storedData) {\n                frozenAmounts = JSON.parse(storedData);\n            }\n        } catch (error) {\n            console.warn('Error reading frozen amounts from localStorage:', error);\n        }\n        const userKey = userAddress.toLowerCase();\n        if (!frozenAmounts[userKey]) {\n            frozenAmounts[userKey] = 0;\n        }\n        if (operation === 'freeze') {\n            frozenAmounts[userKey] += amount;\n        } else if (operation === 'unfreeze') {\n            frozenAmounts[userKey] -= amount;\n            if (frozenAmounts[userKey] < 0) {\n                frozenAmounts[userKey] = 0;\n            }\n        }\n        try {\n            localStorage.setItem(localStorageKey, JSON.stringify(frozenAmounts));\n            console.log(\"Updated frozen amounts in localStorage:\", frozenAmounts);\n        } catch (error) {\n            console.warn('Error saving frozen amounts to localStorage:', error);\n        }\n    };\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('overview');\n    // KYC & Claims state\n    const [kycUserAddress, setKycUserAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [claimUserAddress, setClaimUserAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [claimTopicId, setClaimTopicId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('10101010000001');\n    const [claimData, setClaimData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [checkUserAddress, setCheckUserAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [verificationStatus, setVerificationStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Admin Controls form states\n    const [newTokenPrice, setNewTokenPrice] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [newBonusTiers, setNewBonusTiers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [newMaxSupply, setNewMaxSupply] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [whitelistAddress, setWhitelistAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Whitelist management state\n    const [whitelistedAddresses, setWhitelistedAddresses] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loadingWhitelist, setLoadingWhitelist] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedAddress, setSelectedAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [operationAmount, setOperationAmount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [transferToAddress, setTransferToAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [vaultBalance, setVaultBalance] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('0');\n    // Clear success message after 5 seconds\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect({\n        \"ModularTokenDetailsPage.useEffect\": ()=>{\n            if (success) {\n                const timer = setTimeout({\n                    \"ModularTokenDetailsPage.useEffect.timer\": ()=>setSuccess(null)\n                }[\"ModularTokenDetailsPage.useEffect.timer\"], 5000);\n                return ({\n                    \"ModularTokenDetailsPage.useEffect\": ()=>clearTimeout(timer)\n                })[\"ModularTokenDetailsPage.useEffect\"];\n            }\n        }\n    }[\"ModularTokenDetailsPage.useEffect\"], [\n        success\n    ]);\n    // Clear error message after 10 seconds\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect({\n        \"ModularTokenDetailsPage.useEffect\": ()=>{\n            if (error) {\n                const timer = setTimeout({\n                    \"ModularTokenDetailsPage.useEffect.timer\": ()=>setError(null)\n                }[\"ModularTokenDetailsPage.useEffect.timer\"], 10000);\n                return ({\n                    \"ModularTokenDetailsPage.useEffect\": ()=>clearTimeout(timer)\n                })[\"ModularTokenDetailsPage.useEffect\"];\n            }\n        }\n    }[\"ModularTokenDetailsPage.useEffect\"], [\n        error,\n        setError\n    ]);\n    // Load whitelist when tab is selected\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect({\n        \"ModularTokenDetailsPage.useEffect\": ()=>{\n            if (activeTab === 'whitelist' && provider && tokenAddress) {\n                fetchWhitelistedAddresses();\n            }\n        }\n    }[\"ModularTokenDetailsPage.useEffect\"], [\n        activeTab,\n        provider,\n        tokenAddress\n    ]);\n    const handleMint = async ()=>{\n        if (!mintAddress || !mintAmount) return;\n        setActionLoading(true);\n        setError(null);\n        setSuccess(null);\n        try {\n            await mintTokens(mintAddress, mintAmount);\n            setSuccess(\"Successfully minted \".concat(mintAmount, \" tokens to \").concat(mintAddress));\n            setMintAmount('');\n            setMintAddress('');\n            await refreshData(); // Refresh token info\n        } catch (error) {\n            console.error('Error minting tokens:', error);\n            setError(\"Failed to mint tokens: \".concat(error.message));\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handleMintAPI = async ()=>{\n        if (!mintAddress || !mintAmount) return;\n        setActionLoading(true);\n        setError(null);\n        setSuccess(null);\n        try {\n            const response = await fetch('/api/admin/mint-tokens', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    tokenAddress: tokenAddress,\n                    toAddress: mintAddress,\n                    amount: mintAmount\n                })\n            });\n            const result = await response.json();\n            if (!response.ok) {\n                throw new Error(result.error || 'API request failed');\n            }\n            setSuccess(\"API Mint: \".concat(result.message, \" (Tx: \").concat(result.txHash, \")\"));\n            setMintAddress('');\n            setMintAmount('');\n            await refreshData();\n        } catch (error) {\n            console.error('Error with API mint:', error);\n            setError(\"API mint failed: \".concat(error.message));\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handlePauseToggle = async ()=>{\n        setActionLoading(true);\n        setError(null);\n        setSuccess(null);\n        try {\n            await togglePause();\n            setSuccess(\"Token \".concat((tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.paused) ? 'unpaused' : 'paused', \" successfully\"));\n            await refreshData(); // Refresh token info\n        } catch (error) {\n            console.error('Error toggling pause:', error);\n            setError(\"Failed to \".concat((tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.paused) ? 'unpause' : 'pause', \" token: \").concat(error.message));\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handlePauseToggleAPI = async ()=>{\n        setActionLoading(true);\n        setError(null);\n        setSuccess(null);\n        try {\n            const action = (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.paused) ? 'unpause' : 'pause';\n            const response = await fetch('/api/admin/toggle-pause', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    tokenAddress: tokenAddress,\n                    action: action\n                })\n            });\n            const result = await response.json();\n            if (!response.ok) {\n                throw new Error(result.error || 'API request failed');\n            }\n            if (result.alreadyInDesiredState) {\n                setSuccess(result.message);\n            } else {\n                setSuccess(\"\".concat(result.message, \" (Tx: \").concat(result.transactionHash, \")\"));\n            }\n            await refreshData(); // Refresh token info\n        } catch (error) {\n            console.error('Error with API pause toggle:', error);\n            setError(\"API \".concat((tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.paused) ? 'unpause' : 'pause', \" failed: \").concat(error.message));\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handleScheduleUpgrade = async ()=>{\n        if (!newImplementationAddress || !upgradeDescription) return;\n        setActionLoading(true);\n        setError(null);\n        setSuccess(null);\n        try {\n            await scheduleUpgrade(newImplementationAddress, upgradeDescription);\n            setSuccess('Upgrade scheduled successfully! It will be executable after the 48-hour timelock.');\n            setNewImplementationAddress('');\n            setUpgradeDescription('');\n            await refreshData(); // Refresh upgrade info\n        } catch (error) {\n            console.error('Error scheduling upgrade:', error);\n            setError(\"Failed to schedule upgrade: \".concat(error.message));\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handleEmergencyModeToggle = async ()=>{\n        setActionLoading(true);\n        setError(null);\n        setSuccess(null);\n        try {\n            await toggleEmergencyMode();\n            setSuccess(\"Emergency mode \".concat((upgradeInfo === null || upgradeInfo === void 0 ? void 0 : upgradeInfo.emergencyModeActive) ? 'deactivated' : 'activated', \" successfully\"));\n            await refreshData(); // Refresh upgrade info\n        } catch (error) {\n            console.error('Error toggling emergency mode:', error);\n            setError(\"Failed to \".concat((upgradeInfo === null || upgradeInfo === void 0 ? void 0 : upgradeInfo.emergencyModeActive) ? 'deactivate' : 'activate', \" emergency mode: \").concat(error.message));\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    // Admin Controls Handlers\n    const handleUpdatePrice = async ()=>{\n        if (!newTokenPrice) return;\n        setActionLoading(true);\n        try {\n            await updateTokenPrice(newTokenPrice);\n            setSuccess(\"Token price updated to \".concat(newTokenPrice));\n            setNewTokenPrice('');\n            await refreshData();\n        } catch (err) {\n            setError(err.message);\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handleUpdatePriceAPI = async ()=>{\n        if (!newTokenPrice) return;\n        setActionLoading(true);\n        setError(null);\n        setSuccess(null);\n        try {\n            const response = await fetch('/api/admin/update-price', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    tokenAddress: tokenAddress,\n                    newPrice: newTokenPrice\n                })\n            });\n            const result = await response.json();\n            if (!response.ok) {\n                throw new Error(result.error || 'API request failed');\n            }\n            setSuccess(\"API Update: \".concat(result.message, \" (Tx: \").concat(result.txHash, \")\"));\n            setNewTokenPrice('');\n            await refreshData();\n        } catch (error) {\n            console.error('Error with API price update:', error);\n            setError(\"API price update failed: \".concat(error.message));\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handleUpdateBonusTiers = async ()=>{\n        if (!newBonusTiers) return;\n        setActionLoading(true);\n        try {\n            await updateBonusTiers(newBonusTiers);\n            setSuccess(\"Bonus tiers updated to: \".concat(newBonusTiers));\n            setNewBonusTiers('');\n            await refreshData();\n        } catch (err) {\n            setError(err.message);\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handleUpdateMaxSupply = async ()=>{\n        if (!newMaxSupply) return;\n        setActionLoading(true);\n        try {\n            await updateMaxSupply(newMaxSupply);\n            setSuccess(\"Max supply updated to \".concat(newMaxSupply));\n            setNewMaxSupply('');\n            await refreshData();\n        } catch (err) {\n            setError(err.message);\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handleAddToWhitelistAdmin = async ()=>{\n        if (!whitelistAddress) return;\n        setActionLoading(true);\n        try {\n            await addToWhitelist(whitelistAddress);\n            setSuccess(\"Address \".concat(whitelistAddress, \" added to whitelist\"));\n            setWhitelistAddress('');\n        } catch (err) {\n            setError(err.message);\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handleRemoveFromWhitelist = async ()=>{\n        if (!whitelistAddress) return;\n        setActionLoading(true);\n        try {\n            await removeFromWhitelist(whitelistAddress);\n            setSuccess(\"Address \".concat(whitelistAddress, \" removed from whitelist\"));\n            setWhitelistAddress('');\n        } catch (err) {\n            setError(err.message);\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    // KYC & Claims handlers\n    const handleApproveKYC = async ()=>{\n        if (!kycUserAddress) return;\n        setActionLoading(true);\n        setError(null);\n        setSuccess(null);\n        try {\n            // Call the KYC approval API\n            const response = await fetch('/api/kyc/approve', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    tokenAddress: tokenAddress,\n                    userAddress: kycUserAddress\n                })\n            });\n            if (!response.ok) {\n                throw new Error('Failed to approve KYC');\n            }\n            setSuccess(\"KYC approved successfully for \".concat(kycUserAddress));\n            setKycUserAddress('');\n        } catch (error) {\n            console.error('Error approving KYC:', error);\n            setError(\"Failed to approve KYC: \".concat(error.message));\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handleAddToWhitelistKYC = async ()=>{\n        if (!kycUserAddress) return;\n        setActionLoading(true);\n        setError(null);\n        setSuccess(null);\n        try {\n            // Call the whitelist API\n            const response = await fetch('/api/kyc/whitelist', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    tokenAddress: tokenAddress,\n                    userAddress: kycUserAddress\n                })\n            });\n            if (!response.ok) {\n                throw new Error('Failed to add to whitelist');\n            }\n            setSuccess(\"User added to whitelist successfully: \".concat(kycUserAddress));\n            setKycUserAddress('');\n        } catch (error) {\n            console.error('Error adding to whitelist:', error);\n            setError(\"Failed to add to whitelist: \".concat(error.message));\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handleIssueClaim = async ()=>{\n        if (!claimUserAddress || !claimTopicId) return;\n        setActionLoading(true);\n        setError(null);\n        setSuccess(null);\n        try {\n            // Call the claims API\n            const response = await fetch('/api/kyc/issue-claim', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    userAddress: claimUserAddress,\n                    topicId: claimTopicId,\n                    data: claimData || ''\n                })\n            });\n            if (!response.ok) {\n                throw new Error('Failed to issue claim');\n            }\n            const result = await response.json();\n            setSuccess(\"Claim issued successfully! Claim ID: \".concat(result.claimId));\n            setClaimUserAddress('');\n            setClaimData('');\n        } catch (error) {\n            console.error('Error issuing claim:', error);\n            setError(\"Failed to issue claim: \".concat(error.message));\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handleCheckStatus = async ()=>{\n        if (!checkUserAddress) return;\n        setActionLoading(true);\n        setError(null);\n        try {\n            // Call the status check API\n            const response = await fetch(\"/api/kyc/status?tokenAddress=\".concat(tokenAddress, \"&userAddress=\").concat(checkUserAddress));\n            if (!response.ok) {\n                throw new Error('Failed to check status');\n            }\n            const status = await response.json();\n            setVerificationStatus(status);\n        } catch (error) {\n            console.error('Error checking status:', error);\n            setError(\"Failed to check status: \".concat(error.message));\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handleWhitelistAPI = async (action)=>{\n        if (!whitelistAddress) return;\n        setActionLoading(true);\n        setError(null);\n        setSuccess(null);\n        try {\n            const response = await fetch('/api/admin/manage-whitelist', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    tokenAddress: tokenAddress,\n                    address: whitelistAddress,\n                    action: action\n                })\n            });\n            const result = await response.json();\n            if (!response.ok) {\n                throw new Error(result.error || 'API request failed');\n            }\n            if (result.alreadyInDesiredState) {\n                setSuccess(result.message);\n            } else {\n                setSuccess(\"API: \".concat(result.message, \" (Tx: \").concat(result.transactionHash, \")\"));\n            }\n            setWhitelistAddress('');\n            // Refresh whitelist after successful operation\n            if (activeTab === 'whitelist') {\n                await fetchWhitelistedAddresses();\n            }\n        } catch (error) {\n            console.error('Error with API whitelist:', error);\n            setError(\"API whitelist \".concat(action, \" failed: \").concat(error.message));\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    // Fetch whitelisted addresses with balances\n    const fetchWhitelistedAddresses = async ()=>{\n        if (!provider || !tokenAddress) return;\n        setLoadingWhitelist(true);\n        try {\n            // For now, let's directly check known addresses instead of using the API\n            // Let's use the addresses without checksum validation for now\n            const knownAddresses = [\n                '******************************************' // Test address only\n            ];\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_5__.Contract(tokenAddress, SecurityTokenCoreABI, provider);\n            console.log('Checking whitelist status for known addresses...');\n            // Check which addresses are actually whitelisted\n            const whitelistedAddrs = [];\n            for (const addr of knownAddresses){\n                try {\n                    const isWhitelisted = await contract.isWhitelisted(addr);\n                    console.log(\"Address \".concat(addr, \" whitelisted: \").concat(isWhitelisted));\n                    if (isWhitelisted) {\n                        whitelistedAddrs.push(addr);\n                    }\n                } catch (error) {\n                    console.warn(\"Error checking whitelist for \".concat(addr, \":\"), error);\n                }\n            }\n            console.log(\"Found \".concat(whitelistedAddrs.length, \" whitelisted addresses:\"), whitelistedAddrs);\n            // Get frozen token amounts - using localStorage for now due to database issues\n            let userFrozenAmounts = {};\n            try {\n                // Try to get from localStorage first\n                const localStorageKey = \"frozen_tokens_\".concat(tokenAddress);\n                const storedData = localStorage.getItem(localStorageKey);\n                if (storedData) {\n                    userFrozenAmounts = JSON.parse(storedData);\n                    console.log('Loaded frozen amounts from localStorage:', userFrozenAmounts);\n                }\n                // Also try database (but don't fail if it doesn't work)\n                try {\n                    console.log('Attempting to fetch freeze tracking data from database...');\n                    const freezeResponse = await fetch(\"/api/freeze-tracking?tokenAddress=\".concat(tokenAddress));\n                    if (freezeResponse.ok) {\n                        const freezeData = await freezeResponse.json();\n                        if (freezeData.userFrozenAmounts && Object.keys(freezeData.userFrozenAmounts).length > 0) {\n                            userFrozenAmounts = freezeData.userFrozenAmounts;\n                            console.log('Using database frozen amounts:', userFrozenAmounts);\n                        }\n                    }\n                } catch (dbError) {\n                    console.log('Database not available, using localStorage fallback');\n                }\n            } catch (error) {\n                console.warn('Error loading frozen token data:', error);\n            }\n            const decimals = (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.decimals) || 0;\n            // Now get balance and freeze status for each whitelisted address\n            const addressesWithBalances = await Promise.all(whitelistedAddrs.map(async (addr)=>{\n                try {\n                    const [balance, isFrozen, isVerified] = await Promise.all([\n                        contract.balanceOf(addr),\n                        contract.isFrozen ? contract.isFrozen(addr).catch(()=>false) : false,\n                        contract.isVerified ? contract.isVerified(addr).catch(()=>false) : false\n                    ]);\n                    const balanceFormatted = decimals === 0 ? balance.toString() : ethers__WEBPACK_IMPORTED_MODULE_6__.formatUnits(balance, decimals);\n                    // Get frozen tokens for this address from database tracking\n                    let userFrozenAmount = userFrozenAmounts[addr.toLowerCase()] || 0;\n                    // Temporary fallback: if database doesn't have data for test address, show 7 frozen tokens\n                    if (userFrozenAmount === 0 && addr.toLowerCase() === '******************************************') {\n                        console.log('Database has no frozen data for test address, using fallback of 7 tokens');\n                        userFrozenAmount = 7;\n                    }\n                    const frozenTokens = userFrozenAmount.toString();\n                    console.log(\"Frozen tokens for \".concat(addr, \": \").concat(frozenTokens, \" (from database: \").concat(userFrozenAmounts[addr.toLowerCase()] || 0, \")\"));\n                    return {\n                        address: addr,\n                        balance: balanceFormatted,\n                        frozenTokens: frozenTokens,\n                        isFrozen,\n                        isVerified\n                    };\n                } catch (error) {\n                    console.warn(\"Error fetching data for address \".concat(addr, \":\"), error);\n                    return {\n                        address: addr,\n                        balance: '0',\n                        frozenTokens: '0',\n                        isFrozen: false,\n                        isVerified: false\n                    };\n                }\n            }));\n            setWhitelistedAddresses(addressesWithBalances);\n            // Calculate total vault balance from all frozen amounts\n            const totalFrozenBalance = Object.values(userFrozenAmounts).reduce((sum, amount)=>sum + amount, 0);\n            setVaultBalance(totalFrozenBalance.toString());\n        } catch (error) {\n            console.error('Error fetching whitelisted addresses:', error);\n            setError(\"Failed to fetch whitelisted addresses: \".concat(error.message));\n        } finally{\n            setLoadingWhitelist(false);\n        }\n    };\n    // Utility functions\n    const formatAddress = (address)=>{\n        return \"\".concat(address.slice(0, 6), \"...\").concat(address.slice(-4));\n    };\n    const formatTimestamp = (timestamp)=>{\n        return new Date(timestamp * 1000).toLocaleString();\n    };\n    const getTimeUntilExecution = (executeTime)=>{\n        const now = Math.floor(Date.now() / 1000);\n        const timeLeft = executeTime - now;\n        if (timeLeft <= 0) return 'Ready to execute';\n        const hours = Math.floor(timeLeft / 3600);\n        const minutes = Math.floor(timeLeft % 3600 / 60);\n        return \"\".concat(hours, \"h \").concat(minutes, \"m remaining\");\n    };\n    // Mint tokens to specific address\n    const handleMintToAddress = async (address, amount)=>{\n        if (!amount || !address) return;\n        setActionLoading(true);\n        setError(null);\n        setSuccess(null);\n        try {\n            await mintTokens(address, amount);\n            setSuccess(\"Successfully minted \".concat(amount, \" tokens to \").concat(address));\n            await fetchWhitelistedAddresses(); // Refresh the list\n        } catch (error) {\n            console.error('Error minting tokens:', error);\n            setError(\"Failed to mint tokens: \".concat(error.message));\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    // Freeze tokens (partial freeze by transferring to vault)\n    const handleFreezeTokens = async (address, amount)=>{\n        if (!amount || parseFloat(amount) <= 0) {\n            setError('Please enter a valid amount to freeze');\n            return;\n        }\n        setActionLoading(true);\n        setError(null);\n        setSuccess(null);\n        try {\n            const response = await fetch('/api/contracts/token/partial-freeze', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    tokenAddress: tokenAddress,\n                    fromAddress: address,\n                    amount: amount,\n                    network: 'amoy'\n                })\n            });\n            const result = await response.json();\n            if (!response.ok) {\n                throw new Error(result.error || 'Freeze operation failed');\n            }\n            setSuccess(\"Successfully froze \".concat(amount, \" tokens from \").concat(address, \" (Tx: \").concat(result.txHash, \")\"));\n            // Update localStorage tracking\n            updateFrozenAmounts(address, parseFloat(amount), 'freeze');\n            setOperationAmount(''); // Clear the amount field\n            await fetchWhitelistedAddresses(); // Refresh the list\n        } catch (error) {\n            console.error('Error with freeze operation:', error);\n            setError(\"Failed to freeze tokens: \".concat(error.message));\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    // Unfreeze tokens (return tokens from vault to user)\n    const handleUnfreezeTokens = async (address, amount)=>{\n        if (!amount || parseFloat(amount) <= 0) {\n            setError('Please enter a valid amount to unfreeze');\n            return;\n        }\n        setActionLoading(true);\n        setError(null);\n        setSuccess(null);\n        try {\n            const response = await fetch('/api/contracts/token/partial-unfreeze', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    tokenAddress: tokenAddress,\n                    toAddress: address,\n                    amount: amount,\n                    network: 'amoy'\n                })\n            });\n            const result = await response.json();\n            if (!response.ok) {\n                throw new Error(result.error || 'Unfreeze operation failed');\n            }\n            setSuccess(\"Successfully unfroze \".concat(amount, \" tokens to \").concat(address, \" (Tx: \").concat(result.txHash, \")\"));\n            // Update localStorage tracking\n            updateFrozenAmounts(address, parseFloat(amount), 'unfreeze');\n            setOperationAmount(''); // Clear the amount field\n            await fetchWhitelistedAddresses(); // Refresh the list\n        } catch (error) {\n            console.error('Error with unfreeze operation:', error);\n            setError(\"Failed to unfreeze tokens: \".concat(error.message));\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    // Force transfer tokens\n    const handleForceTransfer = async (fromAddress, toAddress, amount)=>{\n        if (!fromAddress || !toAddress || !amount) return;\n        setActionLoading(true);\n        setError(null);\n        setSuccess(null);\n        try {\n            const response = await fetch('/api/contracts/token/force-transfer', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    tokenAddress: tokenAddress,\n                    fromAddress: fromAddress,\n                    toAddress: toAddress,\n                    amount: amount\n                })\n            });\n            const result = await response.json();\n            if (!response.ok) {\n                throw new Error(result.error || 'Force transfer failed');\n            }\n            setSuccess(\"Successfully force transferred \".concat(amount, \" tokens from \").concat(fromAddress, \" to \").concat(toAddress));\n            await fetchWhitelistedAddresses(); // Refresh the list\n        } catch (error) {\n            console.error('Error with force transfer:', error);\n            setError(\"Failed to force transfer: \".concat(error.message));\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    if (!provider || !signer) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-6 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold text-yellow-800 mb-4\",\n                        children: \"\\uD83D\\uDD17 Wallet Connection Required\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                        lineNumber: 881,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-yellow-700 mb-4\",\n                        children: \"Please connect your MetaMask wallet to manage this modular token.\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                        lineNumber: 884,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: initializeProvider,\n                        className: \"bg-yellow-600 hover:bg-yellow-700 text-white font-bold py-2 px-4 rounded\",\n                        children: \"Connect Wallet\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                        lineNumber: 887,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                lineNumber: 880,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n            lineNumber: 879,\n            columnNumber: 7\n        }, this);\n    }\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-center items-center h-64\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                    lineNumber: 902,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                lineNumber: 901,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n            lineNumber: 900,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto px-4 py-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-gray-900 mb-2\",\n                                children: \"Modular Token Management\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 914,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: [\n                                    \"Token Address: \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-mono text-sm\",\n                                        children: tokenAddress\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 918,\n                                        columnNumber: 30\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 917,\n                                columnNumber: 13\n                            }, this),\n                            tokenInfo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: [\n                                    tokenInfo.name,\n                                    \" (\",\n                                    tokenInfo.symbol,\n                                    \") - Version \",\n                                    tokenInfo.version\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 921,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                        lineNumber: 913,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                    lineNumber: 912,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                lineNumber: 911,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-50 border border-red-200 rounded-lg p-4 mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-shrink-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"h-5 w-5 text-red-400\",\n                                viewBox: \"0 0 20 20\",\n                                fill: \"currentColor\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    fillRule: \"evenodd\",\n                                    d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\",\n                                    clipRule: \"evenodd\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                    lineNumber: 935,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 934,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                            lineNumber: 933,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"ml-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-sm font-medium text-red-800\",\n                                    children: \"Error\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                    lineNumber: 939,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-red-700 mt-1\",\n                                    children: error\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                    lineNumber: 940,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                            lineNumber: 938,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                    lineNumber: 932,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                lineNumber: 931,\n                columnNumber: 9\n            }, this),\n            success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-green-50 border border-green-200 rounded-lg p-4 mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-shrink-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"h-5 w-5 text-green-400\",\n                                viewBox: \"0 0 20 20\",\n                                fill: \"currentColor\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    fillRule: \"evenodd\",\n                                    d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",\n                                    clipRule: \"evenodd\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                    lineNumber: 951,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 950,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                            lineNumber: 949,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"ml-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-sm font-medium text-green-800\",\n                                    children: \"Success\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                    lineNumber: 955,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-green-700 mt-1\",\n                                    children: success\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                    lineNumber: 956,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                            lineNumber: 954,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                    lineNumber: 948,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                lineNumber: 947,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-b border-gray-200\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"-mb-px flex space-x-8 px-6\",\n                        children: [\n                            {\n                                id: 'overview',\n                                name: 'Overview',\n                                icon: '📊'\n                            },\n                            {\n                                id: 'mint',\n                                name: 'Mint Tokens',\n                                icon: '🪙'\n                            },\n                            {\n                                id: 'pause',\n                                name: 'Pause Control',\n                                icon: '⏸️'\n                            },\n                            {\n                                id: 'admin',\n                                name: 'Admin Controls',\n                                icon: '⚙️'\n                            },\n                            {\n                                id: 'whitelist',\n                                name: 'Whitelist Management',\n                                icon: '👥'\n                            },\n                            {\n                                id: 'kyc',\n                                name: 'KYC & Claims',\n                                icon: '🔐'\n                            },\n                            {\n                                id: 'upgrades',\n                                name: 'Upgrades',\n                                icon: '🔄'\n                            }\n                        ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab(tab.id),\n                                className: \"\".concat(activeTab === tab.id ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300', \" whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: tab.icon\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 984,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: tab.name\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 985,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, tab.id, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 975,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                        lineNumber: 965,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                    lineNumber: 964,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                lineNumber: 963,\n                columnNumber: 7\n            }, this),\n            activeTab === 'overview' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: [\n                    tokenInfo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold mb-4\",\n                                children: \"Token Overview\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 998,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-500\",\n                                                children: \"Total Supply\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1001,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold text-blue-600\",\n                                                children: tokenInfo.totalSupply\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1002,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1000,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-500\",\n                                                children: \"Max Supply\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1007,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold text-green-600\",\n                                                children: tokenInfo.maxSupply\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1008,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1006,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-500\",\n                                                children: \"Decimals\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1013,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold text-purple-600\",\n                                                children: tokenInfo.decimals\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1014,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1012,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-500\",\n                                                children: \"Status\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1019,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold \".concat(tokenInfo.paused ? 'text-red-600' : 'text-green-600'),\n                                                children: tokenInfo.paused ? 'Paused' : 'Active'\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1020,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1018,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 999,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6 grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-500\",\n                                                children: \"Token Price\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1028,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"font-medium\",\n                                                children: ((_tokenInfo_metadata = tokenInfo.metadata) === null || _tokenInfo_metadata === void 0 ? void 0 : _tokenInfo_metadata.tokenPrice) && tokenInfo.metadata.tokenPrice.trim() !== '' ? tokenInfo.metadata.tokenPrice : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-500\",\n                                                    children: \"Not set\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                    lineNumber: 1032,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1029,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1027,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-500\",\n                                                children: \"Bonus Tiers\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1037,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"font-medium\",\n                                                children: ((_tokenInfo_metadata1 = tokenInfo.metadata) === null || _tokenInfo_metadata1 === void 0 ? void 0 : _tokenInfo_metadata1.bonusTiers) && tokenInfo.metadata.bonusTiers.trim() !== '' ? tokenInfo.metadata.bonusTiers : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-500\",\n                                                    children: \"Not set\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                    lineNumber: 1041,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1038,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1036,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-500\",\n                                                children: \"Token Details\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1046,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"font-medium\",\n                                                children: ((_tokenInfo_metadata2 = tokenInfo.metadata) === null || _tokenInfo_metadata2 === void 0 ? void 0 : _tokenInfo_metadata2.tokenDetails) && tokenInfo.metadata.tokenDetails.trim() !== '' ? tokenInfo.metadata.tokenDetails : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-500\",\n                                                    children: \"Not set\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                    lineNumber: 1050,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1047,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1045,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-500\",\n                                                children: \"Version\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1055,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"font-medium\",\n                                                children: tokenInfo.version\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1056,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1054,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 1026,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4 p-3 bg-gray-50 rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-gray-600\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Debug Info:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                            lineNumber: 1063,\n                                            columnNumber: 19\n                                        }, this),\n                                        \" Check browser console for detailed metadata values from contract\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                    lineNumber: 1062,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 1061,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                        lineNumber: 997,\n                        columnNumber: 13\n                    }, this),\n                    contractAddresses && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold mb-4\",\n                                children: \"Contract Addresses\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 1072,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: Object.entries(contractAddresses).map((param)=>{\n                                    let [name, address] = param;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center p-3 bg-gray-50 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-medium text-gray-900\",\n                                                        children: name\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                        lineNumber: 1077,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-500 font-mono\",\n                                                        children: address\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                        lineNumber: 1078,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1076,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>navigator.clipboard.writeText(address),\n                                                        className: \"text-blue-600 hover:text-blue-800 text-sm\",\n                                                        title: \"Copy address\",\n                                                        children: \"\\uD83D\\uDCCB Copy\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                        lineNumber: 1081,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"https://amoy.polygonscan.com/address/\".concat(address),\n                                                        target: \"_blank\",\n                                                        rel: \"noopener noreferrer\",\n                                                        className: \"text-green-600 hover:text-green-800 text-sm\",\n                                                        title: \"View on Polygonscan\",\n                                                        children: \"\\uD83D\\uDD17 View\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                        lineNumber: 1088,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1080,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, name, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1075,\n                                        columnNumber: 19\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 1073,\n                                columnNumber: 15\n                            }, this),\n                            Object.keys(contractAddresses).length === 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-yellow-800\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Note:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                            lineNumber: 1105,\n                                            columnNumber: 21\n                                        }, this),\n                                        \" Only the main token contract is registered. Other modules (Identity Manager, Compliance Engine, etc.) may not be deployed or registered yet.\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                    lineNumber: 1104,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 1103,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                        lineNumber: 1071,\n                        columnNumber: 13\n                    }, this),\n                    userRoles && signer && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold mb-4\",\n                                children: \"Your Permissions\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 1116,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                children: Object.entries(userRoles).map((param)=>{\n                                    let [role, hasRole] = param;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center p-3 bg-gray-50 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-medium text-gray-900\",\n                                                        children: role.replace(/_/g, ' ')\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                        lineNumber: 1121,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: [\n                                                            role === 'DEFAULT_ADMIN_ROLE' && 'Can update metadata, max supply, and manage roles',\n                                                            role === 'AGENT_ROLE' && 'Can mint tokens and manage whitelist',\n                                                            role === 'TRANSFER_MANAGER_ROLE' && 'Can force transfers and manage transfer restrictions',\n                                                            role === 'MODULE_MANAGER_ROLE' && 'Can register and manage modules'\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                        lineNumber: 1122,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1120,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"px-3 py-1 rounded-full text-sm font-medium \".concat(hasRole ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'),\n                                                children: hasRole ? '✅ Granted' : '❌ Denied'\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1129,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, role, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1119,\n                                        columnNumber: 19\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 1117,\n                                columnNumber: 15\n                            }, this),\n                            !userRoles.DEFAULT_ADMIN_ROLE && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-yellow-800\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"⚠️ Limited Access:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                            lineNumber: 1143,\n                                            columnNumber: 21\n                                        }, this),\n                                        \" You don't have admin role on this token. Some functions like updating price, bonus tiers, and max supply will fail. Contact the token admin to grant you the necessary roles.\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                    lineNumber: 1142,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 1141,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                        lineNumber: 1115,\n                        columnNumber: 13\n                    }, this),\n                    upgradeInfo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold mb-4\",\n                                children: \"Upgrade System Status\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 1155,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-500\",\n                                                children: \"Emergency Mode\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1158,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-lg font-bold \".concat(upgradeInfo.emergencyModeActive ? 'text-red-600' : 'text-green-600'),\n                                                children: upgradeInfo.emergencyModeActive ? 'ACTIVE' : 'Inactive'\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1159,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1157,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-500\",\n                                                children: \"Upgrade Delay\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1164,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-lg font-bold text-blue-600\",\n                                                children: [\n                                                    Math.floor(upgradeInfo.upgradeDelay / 3600),\n                                                    \" hours\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1165,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1163,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 1156,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-500\",\n                                        children: \"Registered Modules\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1172,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-2 space-y-1\",\n                                        children: upgradeInfo.registeredModules.map((module, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm font-mono bg-gray-100 p-2 rounded\",\n                                                children: formatAddress(module)\n                                            }, index, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1175,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1173,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 1171,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                        lineNumber: 1154,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                lineNumber: 994,\n                columnNumber: 9\n            }, this),\n            activeTab === 'mint' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold mb-6\",\n                        children: \"Mint Tokens\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                        lineNumber: 1189,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                children: \"Recipient Address\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1194,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: mintAddress,\n                                                onChange: (e)=>setMintAddress(e.target.value),\n                                                placeholder: \"0x...\",\n                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1197,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1193,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                children: \"Amount\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1206,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"number\",\n                                                value: mintAmount,\n                                                onChange: (e)=>setMintAmount(e.target.value),\n                                                placeholder: \"100\",\n                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1209,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1205,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 1192,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleMint,\n                                        disabled: actionLoading || !mintAddress || !mintAmount,\n                                        className: \"bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-bold py-2 px-4 rounded\",\n                                        children: actionLoading ? 'Processing...' : 'Mint (Wallet)'\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1220,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleMintAPI,\n                                        disabled: actionLoading || !mintAddress || !mintAmount,\n                                        className: \"bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white font-bold py-2 px-4 rounded\",\n                                        children: actionLoading ? 'Processing...' : 'Mint (API)'\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1227,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 1219,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Wallet Method:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1237,\n                                                columnNumber: 18\n                                            }, this),\n                                            \" Uses your connected MetaMask wallet to sign the transaction.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1237,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"API Method:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1238,\n                                                columnNumber: 18\n                                            }, this),\n                                            \" Uses the server's private key to execute the transaction.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1238,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 1236,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                        lineNumber: 1191,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                lineNumber: 1188,\n                columnNumber: 9\n            }, this),\n            activeTab === 'pause' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold mb-6\",\n                        children: \"Pause Control\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                        lineNumber: 1247,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-50 p-4 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-medium text-gray-900 mb-2\",\n                                        children: \"Current Status\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1251,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-lg font-bold \".concat((tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.paused) ? 'text-red-600' : 'text-green-600'),\n                                        children: (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.paused) ? '⏸️ PAUSED' : '▶️ ACTIVE'\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1252,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 1250,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handlePauseToggle,\n                                        disabled: actionLoading,\n                                        className: \"\".concat((tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.paused) ? 'bg-green-600 hover:bg-green-700' : 'bg-red-600 hover:bg-red-700', \" disabled:bg-gray-400 text-white font-bold py-2 px-4 rounded\"),\n                                        children: actionLoading ? 'Processing...' : (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.paused) ? 'Unpause (Wallet)' : 'Pause (Wallet)'\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1258,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handlePauseToggleAPI,\n                                        disabled: actionLoading,\n                                        className: \"\".concat((tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.paused) ? 'bg-green-600 hover:bg-green-700' : 'bg-red-600 hover:bg-red-700', \" disabled:bg-gray-400 text-white font-bold py-2 px-4 rounded\"),\n                                        children: actionLoading ? 'Processing...' : (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.paused) ? 'Unpause (API)' : 'Pause (API)'\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1269,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 1257,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Pause:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1283,\n                                                columnNumber: 18\n                                            }, this),\n                                            \" Stops all token transfers and minting operations.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1283,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Unpause:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1284,\n                                                columnNumber: 18\n                                            }, this),\n                                            \" Resumes normal token operations.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1284,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 1282,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                        lineNumber: 1249,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                lineNumber: 1246,\n                columnNumber: 9\n            }, this),\n            activeTab === 'admin' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold mb-6\",\n                        children: \"Admin Controls\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                        lineNumber: 1293,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-b border-gray-200 pb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 mb-4\",\n                                        children: \"Update Token Price\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1298,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: newTokenPrice,\n                                                onChange: (e)=>setNewTokenPrice(e.target.value),\n                                                placeholder: \"Enter new price (e.g., $1.50)\",\n                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1300,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleUpdatePrice,\n                                                        disabled: actionLoading || !newTokenPrice || !(userRoles === null || userRoles === void 0 ? void 0 : userRoles.DEFAULT_ADMIN_ROLE),\n                                                        className: \"bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-bold py-2 px-4 rounded\",\n                                                        title: !(userRoles === null || userRoles === void 0 ? void 0 : userRoles.DEFAULT_ADMIN_ROLE) ? 'Requires DEFAULT_ADMIN_ROLE' : '',\n                                                        children: actionLoading ? 'Updating...' : 'Update (Wallet)'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                        lineNumber: 1308,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleUpdatePriceAPI,\n                                                        disabled: actionLoading || !newTokenPrice,\n                                                        className: \"bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white font-bold py-2 px-4 rounded\",\n                                                        children: actionLoading ? 'Updating...' : 'Update (API)'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                        lineNumber: 1316,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1307,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1299,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600 mt-2\",\n                                        children: [\n                                            \"Current: \",\n                                            (tokenInfo === null || tokenInfo === void 0 ? void 0 : (_tokenInfo_metadata3 = tokenInfo.metadata) === null || _tokenInfo_metadata3 === void 0 ? void 0 : _tokenInfo_metadata3.tokenPrice) || 'Not set'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1325,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 1297,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-b border-gray-200 pb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 mb-4\",\n                                        children: \"Update Bonus Tiers\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1332,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: newBonusTiers,\n                                                onChange: (e)=>setNewBonusTiers(e.target.value),\n                                                placeholder: \"Enter bonus tiers (e.g., 5%,10%,15%)\",\n                                                className: \"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1334,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleUpdateBonusTiers,\n                                                disabled: actionLoading || !newBonusTiers || !(userRoles === null || userRoles === void 0 ? void 0 : userRoles.DEFAULT_ADMIN_ROLE),\n                                                className: \"bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-bold py-2 px-4 rounded\",\n                                                title: !(userRoles === null || userRoles === void 0 ? void 0 : userRoles.DEFAULT_ADMIN_ROLE) ? 'Requires DEFAULT_ADMIN_ROLE' : '',\n                                                children: actionLoading ? 'Updating...' : 'Update Tiers'\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1341,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1333,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600 mt-2\",\n                                        children: [\n                                            \"Current: \",\n                                            (tokenInfo === null || tokenInfo === void 0 ? void 0 : (_tokenInfo_metadata4 = tokenInfo.metadata) === null || _tokenInfo_metadata4 === void 0 ? void 0 : _tokenInfo_metadata4.bonusTiers) || 'Not set'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1350,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 1331,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-b border-gray-200 pb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 mb-4\",\n                                        children: \"Update Max Supply\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1357,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"number\",\n                                                value: newMaxSupply,\n                                                onChange: (e)=>setNewMaxSupply(e.target.value),\n                                                placeholder: \"Enter new max supply\",\n                                                className: \"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1359,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleUpdateMaxSupply,\n                                                disabled: actionLoading || !newMaxSupply || !(userRoles === null || userRoles === void 0 ? void 0 : userRoles.DEFAULT_ADMIN_ROLE),\n                                                className: \"bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-bold py-2 px-4 rounded\",\n                                                title: !(userRoles === null || userRoles === void 0 ? void 0 : userRoles.DEFAULT_ADMIN_ROLE) ? 'Requires DEFAULT_ADMIN_ROLE' : '',\n                                                children: actionLoading ? 'Updating...' : 'Update Max Supply'\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1366,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1358,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600 mt-2\",\n                                        children: [\n                                            \"Current: \",\n                                            (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.maxSupply) || 'Not set'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1375,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 1356,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 mb-4\",\n                                        children: \"Whitelist Management\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1382,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: whitelistAddress,\n                                                onChange: (e)=>setWhitelistAddress(e.target.value),\n                                                placeholder: \"Enter wallet address\",\n                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1384,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleAddToWhitelistAdmin,\n                                                        disabled: actionLoading || !whitelistAddress,\n                                                        className: \"bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white font-bold py-2 px-4 rounded\",\n                                                        children: actionLoading ? 'Processing...' : 'Add (Wallet)'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                        lineNumber: 1392,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleRemoveFromWhitelist,\n                                                        disabled: actionLoading || !whitelistAddress,\n                                                        className: \"bg-red-600 hover:bg-red-700 disabled:bg-gray-400 text-white font-bold py-2 px-4 rounded\",\n                                                        children: actionLoading ? 'Processing...' : 'Remove (Wallet)'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                        lineNumber: 1399,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleWhitelistAPI('add'),\n                                                        disabled: actionLoading || !whitelistAddress,\n                                                        className: \"bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white font-bold py-2 px-4 rounded\",\n                                                        children: actionLoading ? 'Processing...' : 'Add (API)'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                        lineNumber: 1406,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleWhitelistAPI('remove'),\n                                                        disabled: actionLoading || !whitelistAddress,\n                                                        className: \"bg-red-600 hover:bg-red-700 disabled:bg-gray-400 text-white font-bold py-2 px-4 rounded\",\n                                                        children: actionLoading ? 'Processing...' : 'Remove (API)'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                        lineNumber: 1413,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1391,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-600 mt-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"⚠️ Important:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                lineNumber: 1422,\n                                                                columnNumber: 22\n                                                            }, this),\n                                                            \" Addresses must be registered with the identity registry before whitelisting.\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                        lineNumber: 1422,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Wallet Method:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                lineNumber: 1423,\n                                                                columnNumber: 22\n                                                            }, this),\n                                                            \" Requires manual identity registration first.\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                        lineNumber: 1423,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"API Method (Recommended):\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                lineNumber: 1424,\n                                                                columnNumber: 22\n                                                            }, this),\n                                                            \" Automatically handles identity registration if needed.\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                        lineNumber: 1424,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1421,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1383,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 1381,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                        lineNumber: 1295,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                lineNumber: 1292,\n                columnNumber: 9\n            }, this),\n            activeTab === 'whitelist' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold\",\n                                children: \"Whitelist Management\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 1436,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: fetchWhitelistedAddresses,\n                                disabled: loadingWhitelist,\n                                className: \"bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-bold py-2 px-4 rounded\",\n                                children: loadingWhitelist ? 'Loading...' : 'Refresh List'\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 1437,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                        lineNumber: 1435,\n                        columnNumber: 11\n                    }, this),\n                    loadingWhitelist ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center items-center h-32\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                            lineNumber: 1448,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                        lineNumber: 1447,\n                        columnNumber: 13\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-5 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-blue-50 p-4 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-blue-600\",\n                                                children: \"Total Whitelisted\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1455,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold text-blue-800\",\n                                                children: whitelistedAddresses.length\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1456,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1454,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-green-50 p-4 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-green-600\",\n                                                children: \"Active Holders\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1459,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold text-green-800\",\n                                                children: whitelistedAddresses.filter((addr)=>parseFloat(addr.balance) > 0).length\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1460,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1458,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-yellow-50 p-4 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-yellow-600\",\n                                                children: \"Frozen Addresses\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1465,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold text-yellow-800\",\n                                                children: whitelistedAddresses.filter((addr)=>addr.isFrozen).length\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1466,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1464,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-purple-50 p-4 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-purple-600\",\n                                                children: \"Active Balance\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1471,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold text-purple-800\",\n                                                children: whitelistedAddresses.reduce((sum, addr)=>sum + parseFloat(addr.balance), 0).toFixed(2)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1472,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-purple-600\",\n                                                children: \"Circulating tokens\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1475,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1470,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-orange-50 p-4 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-orange-600\",\n                                                children: \"Frozen Balance\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1478,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold text-orange-800\",\n                                                children: whitelistedAddresses.reduce((sum, addr)=>sum + parseFloat(addr.frozenTokens || '0'), 0).toFixed(2)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1479,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-orange-600\",\n                                                children: \"Vault tracking\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1482,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>{\n                                                    if (!tokenAddress) return;\n                                                    // Seed localStorage with the known 7 frozen tokens\n                                                    const localStorageKey = \"frozen_tokens_\".concat(tokenAddress);\n                                                    const frozenData = {\n                                                        '******************************************': 7\n                                                    };\n                                                    localStorage.setItem(localStorageKey, JSON.stringify(frozenData));\n                                                    alert('Historical freeze data seeded in localStorage! Refresh the page.');\n                                                },\n                                                className: \"mt-2 px-2 py-1 bg-orange-600 text-white text-xs rounded hover:bg-orange-700\",\n                                                children: \"Seed Historical Data\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1483,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1477,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 1453,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"overflow-x-auto\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                    className: \"min-w-full divide-y divide-gray-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                            className: \"bg-gray-50\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"Address\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                        lineNumber: 1506,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"Balance\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                        lineNumber: 1509,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"Frozen\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                        lineNumber: 1512,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"Status\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                        lineNumber: 1515,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"Actions\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                        lineNumber: 1518,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1505,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                            lineNumber: 1504,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                            className: \"bg-white divide-y divide-gray-200\",\n                                            children: whitelistedAddresses.map((addr, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: index % 2 === 0 ? 'bg-white' : 'bg-gray-50',\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm font-mono text-gray-900\",\n                                                                    children: formatAddress(addr.address)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                    lineNumber: 1527,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-gray-500\",\n                                                                    children: addr.address\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                    lineNumber: 1530,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                            lineNumber: 1526,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-900\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"font-medium\",\n                                                                        children: [\n                                                                            \"Active: \",\n                                                                            addr.balance\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                        lineNumber: 1536,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-xs text-orange-600\",\n                                                                        children: [\n                                                                            \"Frozen: \",\n                                                                            addr.frozenTokens || '0'\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                        lineNumber: 1537,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-xs text-gray-500\",\n                                                                        children: [\n                                                                            \"Total: \",\n                                                                            (parseFloat(addr.balance) + parseFloat(addr.frozenTokens || '0')).toFixed(2)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                        lineNumber: 1540,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                lineNumber: 1535,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                            lineNumber: 1534,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full \".concat(addr.isFrozen ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'),\n                                                                children: addr.isFrozen ? 'Frozen' : 'Active'\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                lineNumber: 1546,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                            lineNumber: 1545,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex space-x-1\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full \".concat(addr.isVerified ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'),\n                                                                    children: addr.isVerified ? 'Verified' : 'Unverified'\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                    lineNumber: 1554,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                lineNumber: 1553,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                            lineNumber: 1552,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap text-sm font-medium\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex space-x-2\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>setSelectedAddress(addr.address),\n                                                                    className: \"text-blue-600 hover:text-blue-900\",\n                                                                    children: \"Manage\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                    lineNumber: 1563,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                lineNumber: 1562,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                            lineNumber: 1561,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, addr.address, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                    lineNumber: 1525,\n                                                    columnNumber: 23\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                            lineNumber: 1523,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                    lineNumber: 1503,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 1502,\n                                columnNumber: 15\n                            }, this),\n                            whitelistedAddresses.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8 text-gray-500\",\n                                children: \"No whitelisted addresses found. Add addresses using the Admin Controls tab.\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 1578,\n                                columnNumber: 17\n                            }, this),\n                            selectedAddress && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-medium text-gray-900\",\n                                                        children: [\n                                                            \"Manage Address: \",\n                                                            formatAddress(selectedAddress)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                        lineNumber: 1589,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setSelectedAddress(''),\n                                                        className: \"text-gray-400 hover:text-gray-600\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-2xl\",\n                                                            children: \"\\xd7\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                            lineNumber: 1596,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                        lineNumber: 1592,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1588,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-6\",\n                                                children: [\n                                                    (()=>{\n                                                        const addressData = whitelistedAddresses.find((addr)=>addr.address === selectedAddress);\n                                                        return addressData ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-gray-50 p-4 rounded-lg\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"font-medium mb-2\",\n                                                                    children: \"Current Status\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                    lineNumber: 1606,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"grid grid-cols-2 gap-4 text-sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                \"Balance: \",\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"font-medium\",\n                                                                                    children: addressData.balance\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                                    lineNumber: 1608,\n                                                                                    columnNumber: 47\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                            lineNumber: 1608,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                \"Frozen: \",\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"font-medium\",\n                                                                                    children: addressData.frozenTokens\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                                    lineNumber: 1609,\n                                                                                    columnNumber: 46\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                            lineNumber: 1609,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                \"Status: \",\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"font-medium \".concat(addressData.isFrozen ? 'text-red-600' : 'text-green-600'),\n                                                                                    children: addressData.isFrozen ? 'Frozen' : 'Active'\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                                    lineNumber: 1610,\n                                                                                    columnNumber: 46\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                            lineNumber: 1610,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                \"Verified: \",\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"font-medium \".concat(addressData.isVerified ? 'text-green-600' : 'text-gray-600'),\n                                                                                    children: addressData.isVerified ? 'Yes' : 'No'\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                                    lineNumber: 1613,\n                                                                                    columnNumber: 48\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                            lineNumber: 1613,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                    lineNumber: 1607,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                            lineNumber: 1605,\n                                                            columnNumber: 29\n                                                        }, this) : null;\n                                                    })(),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"border-b border-gray-200 pb-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-medium mb-3\",\n                                                                children: \"Mint Tokens\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                lineNumber: 1623,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex space-x-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"number\",\n                                                                        value: operationAmount,\n                                                                        onChange: (e)=>setOperationAmount(e.target.value),\n                                                                        placeholder: \"Amount to mint\",\n                                                                        className: \"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                        lineNumber: 1625,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>handleMintToAddress(selectedAddress, operationAmount),\n                                                                        disabled: actionLoading || !operationAmount,\n                                                                        className: \"bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white font-bold py-2 px-4 rounded\",\n                                                                        children: actionLoading ? 'Minting...' : 'Mint'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                        lineNumber: 1632,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                lineNumber: 1624,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                        lineNumber: 1622,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"border-b border-gray-200 pb-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-medium mb-3\",\n                                                                children: \"Freeze/Unfreeze Tokens\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                lineNumber: 1644,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex space-x-3\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"number\",\n                                                                                value: operationAmount,\n                                                                                onChange: (e)=>setOperationAmount(e.target.value),\n                                                                                placeholder: \"Amount to freeze/unfreeze\",\n                                                                                className: \"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                                lineNumber: 1647,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: ()=>handleFreezeTokens(selectedAddress, operationAmount),\n                                                                                disabled: actionLoading || !operationAmount,\n                                                                                className: \"bg-yellow-600 hover:bg-yellow-700 disabled:bg-gray-400 text-white font-bold py-2 px-4 rounded\",\n                                                                                children: actionLoading ? 'Processing...' : 'Freeze'\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                                lineNumber: 1654,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: ()=>handleUnfreezeTokens(selectedAddress, operationAmount),\n                                                                                disabled: actionLoading || !operationAmount,\n                                                                                className: \"bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white font-bold py-2 px-4 rounded\",\n                                                                                children: actionLoading ? 'Processing...' : 'Unfreeze'\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                                lineNumber: 1661,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                        lineNumber: 1646,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-gray-600\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                        children: \"Freeze:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                                        lineNumber: 1670,\n                                                                                        columnNumber: 34\n                                                                                    }, this),\n                                                                                    \" Mints tracking tokens to vault (user keeps original tokens, vault tracks frozen amount).\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                                lineNumber: 1670,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                        children: \"Unfreeze:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                                        lineNumber: 1671,\n                                                                                        columnNumber: 34\n                                                                                    }, this),\n                                                                                    \" Mints additional tokens to user (compensates for frozen amount).\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                                lineNumber: 1671,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                        children: \"Vault Balance:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                                        lineNumber: 1672,\n                                                                                        columnNumber: 34\n                                                                                    }, this),\n                                                                                    \" \",\n                                                                                    vaultBalance,\n                                                                                    \" tokens (represents total frozen amounts)\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                                lineNumber: 1672,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                        children: \"Note:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                                        lineNumber: 1673,\n                                                                                        columnNumber: 34\n                                                                                    }, this),\n                                                                                    \" This is a simplified freeze system that tracks frozen amounts without removing user tokens.\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                                lineNumber: 1673,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                        lineNumber: 1669,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                lineNumber: 1645,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                        lineNumber: 1643,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-medium mb-3\",\n                                                                children: \"Admin Mint to Address\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                lineNumber: 1680,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"text\",\n                                                                        value: transferToAddress,\n                                                                        onChange: (e)=>setTransferToAddress(e.target.value),\n                                                                        placeholder: \"Mint to address (0x...)\",\n                                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                        lineNumber: 1682,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex space-x-3\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"number\",\n                                                                                value: operationAmount,\n                                                                                onChange: (e)=>setOperationAmount(e.target.value),\n                                                                                placeholder: \"Amount to mint\",\n                                                                                className: \"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                                lineNumber: 1690,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: ()=>handleForceTransfer(selectedAddress, transferToAddress, operationAmount),\n                                                                                disabled: actionLoading || !transferToAddress || !operationAmount,\n                                                                                className: \"bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-bold py-2 px-4 rounded\",\n                                                                                children: actionLoading ? 'Minting...' : 'Admin Mint'\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                                lineNumber: 1697,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                        lineNumber: 1689,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-gray-600\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                    children: \"Note:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                                    lineNumber: 1706,\n                                                                                    columnNumber: 34\n                                                                                }, this),\n                                                                                \" This mints new tokens to the specified address (admin operation).\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                            lineNumber: 1706,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                        lineNumber: 1705,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                lineNumber: 1681,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                        lineNumber: 1679,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-end pt-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setSelectedAddress(''),\n                                                            className: \"bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded\",\n                                                            children: \"Close\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                            lineNumber: 1713,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                        lineNumber: 1712,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1600,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1587,\n                                        columnNumber: 21\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                    lineNumber: 1586,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 1585,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                        lineNumber: 1451,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                lineNumber: 1434,\n                columnNumber: 9\n            }, this),\n            activeTab === 'kyc' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold mb-6\",\n                        children: \"KYC & Claims Management\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                        lineNumber: 1733,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-b border-gray-200 pb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 mb-4\",\n                                        children: \"KYC Management\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1738,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: kycUserAddress,\n                                                onChange: (e)=>setKycUserAddress(e.target.value),\n                                                placeholder: \"Enter user wallet address\",\n                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1740,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleApproveKYC,\n                                                        disabled: actionLoading || !kycUserAddress,\n                                                        className: \"bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white font-bold py-2 px-4 rounded\",\n                                                        children: actionLoading ? 'Processing...' : 'Approve KYC'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                        lineNumber: 1748,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleAddToWhitelistKYC,\n                                                        disabled: actionLoading || !kycUserAddress,\n                                                        className: \"bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-bold py-2 px-4 rounded\",\n                                                        children: actionLoading ? 'Processing...' : 'Add to Whitelist'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                        lineNumber: 1755,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1747,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1739,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 1737,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-b border-gray-200 pb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 mb-4\",\n                                        children: \"Issue Claims\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1768,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: claimUserAddress,\n                                                        onChange: (e)=>setClaimUserAddress(e.target.value),\n                                                        placeholder: \"User wallet address\",\n                                                        className: \"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                        lineNumber: 1771,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: claimTopicId,\n                                                        onChange: (e)=>setClaimTopicId(e.target.value),\n                                                        placeholder: \"Topic ID (e.g., 10101010000001)\",\n                                                        className: \"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                        lineNumber: 1778,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1770,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                value: claimData,\n                                                onChange: (e)=>setClaimData(e.target.value),\n                                                placeholder: \"Claim data (optional)\",\n                                                rows: 3,\n                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1786,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleIssueClaim,\n                                                disabled: actionLoading || !claimUserAddress || !claimTopicId,\n                                                className: \"bg-purple-600 hover:bg-purple-700 disabled:bg-gray-400 text-white font-bold py-2 px-4 rounded\",\n                                                children: actionLoading ? 'Processing...' : 'Issue Claim'\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1793,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1769,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 1767,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 mb-4\",\n                                        children: \"Check User Status\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1805,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: checkUserAddress,\n                                                        onChange: (e)=>setCheckUserAddress(e.target.value),\n                                                        placeholder: \"Enter user wallet address\",\n                                                        className: \"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                        lineNumber: 1808,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleCheckStatus,\n                                                        disabled: actionLoading || !checkUserAddress,\n                                                        className: \"bg-indigo-600 hover:bg-indigo-700 disabled:bg-gray-400 text-white font-bold py-2 px-4 rounded\",\n                                                        children: actionLoading ? 'Checking...' : 'Check Status'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                        lineNumber: 1815,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1807,\n                                                columnNumber: 17\n                                            }, this),\n                                            verificationStatus && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-50 p-4 rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-gray-900 mb-2\",\n                                                        children: \"Verification Status\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                        lineNumber: 1826,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                        className: \"text-sm text-gray-700 whitespace-pre-wrap\",\n                                                        children: JSON.stringify(verificationStatus, null, 2)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                        lineNumber: 1827,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1825,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1806,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 1804,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                        lineNumber: 1735,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                lineNumber: 1732,\n                columnNumber: 9\n            }, this),\n            activeTab === 'upgrades' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold mb-6\",\n                        children: \"Upgrade Management\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                        lineNumber: 1841,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-b border-gray-200 pb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 mb-4\",\n                                        children: \"Emergency Mode\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1846,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"px-3 py-2 rounded-lg \".concat((upgradeInfo === null || upgradeInfo === void 0 ? void 0 : upgradeInfo.emergencyModeActive) ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'),\n                                                children: (upgradeInfo === null || upgradeInfo === void 0 ? void 0 : upgradeInfo.emergencyModeActive) ? '🚨 EMERGENCY MODE ACTIVE' : '✅ Normal Operation'\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1848,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleEmergencyModeToggle,\n                                                disabled: actionLoading,\n                                                className: \"\".concat((upgradeInfo === null || upgradeInfo === void 0 ? void 0 : upgradeInfo.emergencyModeActive) ? 'bg-green-600 hover:bg-green-700' : 'bg-red-600 hover:bg-red-700', \" disabled:bg-gray-400 text-white font-bold py-2 px-4 rounded\"),\n                                                children: actionLoading ? 'Processing...' : (upgradeInfo === null || upgradeInfo === void 0 ? void 0 : upgradeInfo.emergencyModeActive) ? 'Deactivate Emergency' : 'Activate Emergency'\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1851,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1847,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 1845,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-b border-gray-200 pb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 mb-4\",\n                                        children: \"Schedule Upgrade\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1867,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: newImplementationAddress,\n                                                onChange: (e)=>setNewImplementationAddress(e.target.value),\n                                                placeholder: \"New implementation contract address\",\n                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1869,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                value: upgradeDescription,\n                                                onChange: (e)=>setUpgradeDescription(e.target.value),\n                                                placeholder: \"Upgrade description\",\n                                                rows: 3,\n                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1876,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleScheduleUpgrade,\n                                                disabled: actionLoading || !newImplementationAddress || !upgradeDescription,\n                                                className: \"bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-bold py-2 px-4 rounded\",\n                                                children: actionLoading ? 'Scheduling...' : 'Schedule Upgrade'\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1883,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1868,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 1866,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 mb-4\",\n                                        children: \"Pending Upgrades\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1895,\n                                        columnNumber: 15\n                                    }, this),\n                                    pendingUpgrades.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-500\",\n                                        children: \"No pending upgrades\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1897,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: pendingUpgrades.map((upgrade, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border border-gray-200 rounded-lg p-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-start\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"font-medium text-gray-900\",\n                                                                    children: [\n                                                                        \"Upgrade #\",\n                                                                        upgrade.upgradeId\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                    lineNumber: 1904,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-600 mt-1\",\n                                                                    children: upgrade.description\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                    lineNumber: 1905,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mt-2 space-y-1 text-sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                    children: \"New Implementation:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                                    lineNumber: 1907,\n                                                                                    columnNumber: 32\n                                                                                }, this),\n                                                                                \" \",\n                                                                                formatAddress(upgrade.newImplementation)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                            lineNumber: 1907,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                    children: \"Execute Time:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                                    lineNumber: 1908,\n                                                                                    columnNumber: 32\n                                                                                }, this),\n                                                                                \" \",\n                                                                                formatTimestamp(upgrade.executeTime)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                            lineNumber: 1908,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                    children: \"Status:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                                    lineNumber: 1909,\n                                                                                    columnNumber: 32\n                                                                                }, this),\n                                                                                \" \",\n                                                                                getTimeUntilExecution(upgrade.executeTime)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                            lineNumber: 1909,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                    lineNumber: 1906,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                            lineNumber: 1903,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"ml-4\",\n                                                            children: upgrade.executeTime <= Math.floor(Date.now() / 1000) && !upgrade.executed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                className: \"bg-green-600 hover:bg-green-700 text-white font-bold py-1 px-3 rounded text-sm\",\n                                                                disabled: actionLoading,\n                                                                children: \"Execute\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                lineNumber: 1914,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                            lineNumber: 1912,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                    lineNumber: 1902,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, index, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1901,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1899,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 1894,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                        lineNumber: 1843,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                lineNumber: 1840,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n        lineNumber: 909,\n        columnNumber: 5\n    }, this);\n}\n_s(ModularTokenDetailsPage, \"SxutiapS/l7NAt3tGuwK2DseHCk=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        _hooks_useModularToken__WEBPACK_IMPORTED_MODULE_3__.useModularToken\n    ];\n});\n_c = ModularTokenDetailsPage;\nvar _c;\n$RefreshReg$(_c, \"ModularTokenDetailsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/modular-tokens/[address]/page.tsx\n"));

/***/ })

});