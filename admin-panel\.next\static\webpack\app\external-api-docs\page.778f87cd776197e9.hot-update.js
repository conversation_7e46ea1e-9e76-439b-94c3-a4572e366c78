"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/external-api-docs/page",{

/***/ "(app-pages-browser)/./src/app/external-api-docs/page.tsx":
/*!********************************************!*\
  !*** ./src/app/external-api-docs/page.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ExternalAPIDocsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction ExternalAPIDocsPage() {\n    _s();\n    const [selectedEndpoint, setSelectedEndpoint] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('tokens');\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('overview');\n    const apiEndpoints = {\n        tokens: {\n            method: 'GET',\n            path: '/api/external/tokens',\n            description: 'Retrieve information about deployed security tokens',\n            parameters: [\n                {\n                    name: 'address',\n                    type: 'string',\n                    required: false,\n                    description: 'Specific token contract address to retrieve'\n                },\n                {\n                    name: 'format',\n                    type: 'string',\n                    required: false,\n                    description: 'Response format (json, xml) - default: json'\n                }\n            ],\n            response: {\n                success: true,\n                data: {\n                    tokens: [\n                        {\n                            address: '******************************************',\n                            name: 'Augment Security Token',\n                            symbol: 'AST',\n                            version: '4.0.0',\n                            totalSupply: '1000000',\n                            maxSupply: '100000000',\n                            decimals: 0,\n                            price: '7.50 USD',\n                            bonusTiers: 'Early: 45%, Standard: 30%, Late: 15%',\n                            isPaused: false,\n                            totalInvestors: 25,\n                            issuer: {\n                                name: 'Augment Technologies',\n                                address: '0x56f3726C92B8B92a6ab71983886F91718540d888',\n                                website: 'https://augment.com',\n                                description: 'Leading blockchain technology company'\n                            },\n                            metadata: {\n                                deployedAt: '2024-01-15T10:30:00Z',\n                                lastUpdated: '2024-01-20T14:45:00Z',\n                                network: 'Polygon Amoy Testnet',\n                                contractType: 'ERC-3643 Security Token'\n                            },\n                            compliance: {\n                                kycRequired: true,\n                                whitelistEnabled: true,\n                                claimsRequired: [\n                                    '10101010000001',\n                                    '10101010000004'\n                                ]\n                            },\n                            statistics: {\n                                totalTransactions: 150,\n                                averageTransactionValue: '5000',\n                                largestTransaction: '50000',\n                                activeInvestors: 20\n                            }\n                        }\n                    ],\n                    totalCount: 1,\n                    summary: {\n                        totalSupply: '1000000',\n                        totalInvestors: 25,\n                        activeTokens: 1,\n                        pausedTokens: 0\n                    }\n                },\n                timestamp: '2024-01-20T15:00:00Z',\n                version: '1.0.0'\n            },\n            example: 'curl -X GET \"https://your-domain.com/api/external/tokens\" \\\\\\n  -H \"Content-Type: application/json\"'\n        },\n        investors: {\n            method: 'GET',\n            path: '/api/external/investors',\n            description: 'Retrieve investor information and portfolio data',\n            parameters: [\n                {\n                    name: 'wallet',\n                    type: 'string',\n                    required: false,\n                    description: 'Specific wallet address to retrieve'\n                },\n                {\n                    name: 'email',\n                    type: 'string',\n                    required: false,\n                    description: 'Specific email address to retrieve'\n                },\n                {\n                    name: 'stats',\n                    type: 'boolean',\n                    required: false,\n                    description: 'Include aggregated statistics - default: false'\n                }\n            ],\n            response: {\n                success: true,\n                data: {\n                    investors: [\n                        {\n                            id: 'inv_123',\n                            walletAddress: '******************************************',\n                            email: '<EMAIL>',\n                            kycStatus: 'approved',\n                            whitelistStatus: {\n                                '******************************************': {\n                                    approved: true,\n                                    approvedAt: '2024-01-15T12:00:00Z',\n                                    approvedBy: 'admin'\n                                }\n                            },\n                            qualificationStatus: 'completed',\n                            tokens: [\n                                {\n                                    address: '******************************************',\n                                    name: 'Augment Security Token',\n                                    symbol: 'AST',\n                                    balance: '1000',\n                                    frozenBalance: '0',\n                                    totalInvested: '7500',\n                                    averagePrice: '7.50',\n                                    firstPurchase: '2024-01-15T14:30:00Z',\n                                    lastTransaction: '2024-01-18T09:15:00Z'\n                                }\n                            ],\n                            profile: {\n                                firstName: 'John',\n                                lastName: 'Doe',\n                                country: 'United States',\n                                investorType: 'individual',\n                                accreditationStatus: 'accredited'\n                            },\n                            compliance: {\n                                claimsIssued: [\n                                    '10101010000001'\n                                ],\n                                agreementsAccepted: [\n                                    '******************************************'\n                                ],\n                                lastKycUpdate: '2024-01-15T11:00:00Z',\n                                riskAssessment: 'low'\n                            },\n                            statistics: {\n                                totalInvestments: 1,\n                                totalValue: '7500',\n                                portfolioTokens: 1,\n                                joinedDate: '2024-01-15T10:00:00Z',\n                                lastActivity: '2024-01-18T09:15:00Z'\n                            }\n                        }\n                    ],\n                    totalCount: 1,\n                    statistics: {\n                        totalInvestors: 25,\n                        kycApproved: 20,\n                        kycPending: 5,\n                        qualificationCompleted: 18,\n                        totalInvestmentValue: '187500',\n                        averageInvestmentValue: '7500',\n                        investorsByCountry: {\n                            'United States': 15,\n                            'Canada': 5,\n                            'United Kingdom': 3,\n                            'Germany': 2\n                        },\n                        investorsByType: {\n                            'individual': 20,\n                            'institutional': 5\n                        }\n                    }\n                },\n                timestamp: '2024-01-20T15:00:00Z',\n                version: '1.0.0'\n            },\n            example: 'curl -X GET \"https://your-domain.com/api/external/investors?stats=true\" \\\\\\n  -H \"Content-Type: application/json\"'\n        }\n    };\n    const currentEndpoint = apiEndpoints[selectedEndpoint];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto p-6 space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-4xl font-bold mb-4\",\n                        children: \"External API Documentation\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                        lineNumber: 202,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xl text-gray-600 mb-8\",\n                        children: \"Comprehensive API for third-party integrations with the ERC-3643 Security Token System\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                        lineNumber: 203,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-blue-50 border border-blue-200 rounded-lg p-4 mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-blue-800\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Base URL:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 13\n                                    }, this),\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                        className: \"bg-blue-100 px-2 py-1 rounded\",\n                                        children: \"https://your-domain.com\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 40\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-blue-800 mt-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Version:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                                        lineNumber: 211,\n                                        columnNumber: 13\n                                    }, this),\n                                    \" 1.0.0 | \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Format:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                                        lineNumber: 211,\n                                        columnNumber: 47\n                                    }, this),\n                                    \" JSON | \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"CORS:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                                        lineNumber: 211,\n                                        columnNumber: 79\n                                    }, this),\n                                    \" Enabled | \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Auth:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                                        lineNumber: 211,\n                                        columnNumber: 112\n                                    }, this),\n                                    \" Bearer Token\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-blue-800 mt-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Rate Limit:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                                        lineNumber: 214,\n                                        columnNumber: 13\n                                    }, this),\n                                    \" 100 requests/minute | \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"API Keys:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                                        lineNumber: 214,\n                                        columnNumber: 64\n                                    }, this),\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/api-keys\",\n                                        className: \"underline\",\n                                        children: \"Manage here\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                                        lineNumber: 214,\n                                        columnNumber: 91\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                                lineNumber: 213,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                        lineNumber: 206,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                lineNumber: 201,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex space-x-1 bg-gray-100 p-1 rounded-lg\",\n                    children: [\n                        'overview',\n                        'authentication',\n                        'endpoints',\n                        'examples'\n                    ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setActiveTab(tab),\n                            className: \"px-4 py-2 rounded-md font-medium capitalize \".concat(activeTab === tab ? 'bg-white text-blue-600 shadow' : 'text-gray-600 hover:text-gray-900'),\n                            children: tab\n                        }, tab, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                            lineNumber: 223,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                    lineNumber: 221,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                lineNumber: 220,\n                columnNumber: 7\n            }, this),\n            activeTab === 'overview' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow p-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold mb-6\",\n                        children: \"API Overview\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                        lineNumber: 241,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-6 mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border rounded-lg p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold mb-3\",\n                                        children: \"\\uD83C\\uDFE2 Token Information API\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                                        lineNumber: 245,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mb-4\",\n                                        children: \"Access comprehensive information about deployed security tokens including metadata, compliance status, and real-time statistics.\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                                        lineNumber: 246,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"text-sm text-gray-600 space-y-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"• Token details and metadata\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                                                lineNumber: 251,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"• Supply and pricing information\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                                                lineNumber: 252,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"• Compliance and regulatory status\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                                                lineNumber: 253,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"• Real-time statistics\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                                                lineNumber: 254,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                                        lineNumber: 250,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                                lineNumber: 244,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border rounded-lg p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold mb-3\",\n                                        children: \"\\uD83D\\uDC65 Investor Data API\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                                        lineNumber: 259,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mb-4\",\n                                        children: \"Retrieve investor profiles, portfolio information, and compliance status for comprehensive investor management.\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                                        lineNumber: 260,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"text-sm text-gray-600 space-y-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"• Investor profiles and KYC status\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                                                lineNumber: 265,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"• Portfolio and investment history\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                                                lineNumber: 266,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"• Whitelist and compliance data\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                                                lineNumber: 267,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"• Aggregated statistics\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                                                lineNumber: 268,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                                        lineNumber: 264,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                                lineNumber: 258,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                        lineNumber: 243,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-50 rounded-lg p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold mb-4\",\n                                children: \"Key Features\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                                lineNumber: 274,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl mb-2\",\n                                                children: \"\\uD83D\\uDD12\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                                                lineNumber: 277,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-medium\",\n                                                children: \"Secure Access\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                                                lineNumber: 278,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"CORS-enabled with optional authentication\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                                                lineNumber: 279,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                                        lineNumber: 276,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl mb-2\",\n                                                children: \"⚡\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                                                lineNumber: 282,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-medium\",\n                                                children: \"Real-time Data\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                                                lineNumber: 283,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Live blockchain data with caching\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                                                lineNumber: 284,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                                        lineNumber: 281,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl mb-2\",\n                                                children: \"\\uD83D\\uDCCA\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                                                lineNumber: 287,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-medium\",\n                                                children: \"Rich Analytics\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                                                lineNumber: 288,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Comprehensive statistics and insights\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                                                lineNumber: 289,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                                        lineNumber: 286,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                                lineNumber: 275,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                        lineNumber: 273,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                lineNumber: 240,\n                columnNumber: 9\n            }, this),\n            activeTab === 'authentication' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow p-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold mb-6\",\n                        children: \"Authentication\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                        lineNumber: 298,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-green-50 border border-green-200 rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-green-800 mb-2\",\n                                        children: \"Public Access\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                                        lineNumber: 302,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-green-700\",\n                                        children: \"The external API endpoints are currently publicly accessible without authentication. This allows for easy integration and testing.\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                                        lineNumber: 303,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                                lineNumber: 301,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-50 border border-blue-200 rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-blue-800 mb-2\",\n                                        children: \"CORS Support\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                                        lineNumber: 310,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-blue-700 mb-3\",\n                                        children: \"Cross-Origin Resource Sharing (CORS) is enabled for all external API endpoints:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                                        lineNumber: 311,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"text-blue-700 space-y-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: [\n                                                    \"• \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                        children: \"Access-Control-Allow-Origin: *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                                                        lineNumber: 315,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                                                lineNumber: 315,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: [\n                                                    \"• \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                        children: \"Access-Control-Allow-Methods: GET, OPTIONS\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                                                        lineNumber: 316,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                                                lineNumber: 316,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: [\n                                                    \"• \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                        children: \"Access-Control-Allow-Headers: Content-Type, Authorization\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                                                        lineNumber: 317,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                                                lineNumber: 317,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                                        lineNumber: 314,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                                lineNumber: 309,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-yellow-800 mb-2\",\n                                        children: \"Future Authentication\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                                        lineNumber: 322,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-yellow-700\",\n                                        children: \"API key authentication will be implemented in future versions for enhanced security and rate limiting. Current implementation includes caching with 5-minute TTL.\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                                        lineNumber: 323,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                                lineNumber: 321,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                        lineNumber: 300,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                lineNumber: 297,\n                columnNumber: 9\n            }, this),\n            activeTab === 'endpoints' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow p-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold mb-6\",\n                        children: \"API Endpoints\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                        lineNumber: 334,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-2 mb-6\",\n                        children: Object.keys(apiEndpoints).map((key)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setSelectedEndpoint(key),\n                                className: \"px-4 py-2 rounded-md font-medium capitalize \".concat(selectedEndpoint === key ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'),\n                                children: key\n                            }, key, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                                lineNumber: 339,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                        lineNumber: 337,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"border rounded-lg p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"px-3 py-1 rounded text-sm font-medium \".concat(currentEndpoint.method === 'GET' ? 'bg-green-100 text-green-800' : currentEndpoint.method === 'POST' ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'),\n                                        children: currentEndpoint.method\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                                        lineNumber: 356,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                        className: \"text-lg font-mono bg-gray-100 px-3 py-1 rounded\",\n                                        children: currentEndpoint.path\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                                        lineNumber: 363,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                                lineNumber: 355,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mb-6\",\n                                children: currentEndpoint.description\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                                lineNumber: 368,\n                                columnNumber: 13\n                            }, this),\n                            currentEndpoint.parameters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-lg font-semibold mb-3\",\n                                        children: \"Parameters\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                                        lineNumber: 373,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"overflow-x-auto\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                            className: \"w-full border-collapse border border-gray-300\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                        className: \"bg-gray-50\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"border border-gray-300 px-4 py-2 text-left\",\n                                                                children: \"Name\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                                                                lineNumber: 378,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"border border-gray-300 px-4 py-2 text-left\",\n                                                                children: \"Type\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                                                                lineNumber: 379,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"border border-gray-300 px-4 py-2 text-left\",\n                                                                children: \"Required\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                                                                lineNumber: 380,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"border border-gray-300 px-4 py-2 text-left\",\n                                                                children: \"Description\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                                                                lineNumber: 381,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                                                        lineNumber: 377,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                                                    lineNumber: 376,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                    children: currentEndpoint.parameters.map((param, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"border border-gray-300 px-4 py-2 font-mono text-sm\",\n                                                                    children: param.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                                                                    lineNumber: 387,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"border border-gray-300 px-4 py-2 text-sm\",\n                                                                    children: param.type\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                                                                    lineNumber: 390,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"border border-gray-300 px-4 py-2 text-sm\",\n                                                                    children: param.required ? '✅ Yes' : '❌ No'\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                                                                    lineNumber: 393,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"border border-gray-300 px-4 py-2 text-sm\",\n                                                                    children: param.description\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                                                                    lineNumber: 396,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                                                            lineNumber: 386,\n                                                            columnNumber: 25\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                                                    lineNumber: 384,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                                            lineNumber: 375,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                                        lineNumber: 374,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                                lineNumber: 372,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-lg font-semibold mb-3\",\n                                        children: \"Response\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                                        lineNumber: 409,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                        className: \"bg-gray-50 p-4 rounded-lg overflow-x-auto text-sm\",\n                                        children: JSON.stringify(currentEndpoint.response, null, 2)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                                        lineNumber: 410,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                                lineNumber: 408,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-lg font-semibold mb-3\",\n                                        children: \"Example Request\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                                        lineNumber: 417,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                        className: \"bg-gray-900 text-green-400 p-4 rounded-lg overflow-x-auto text-sm\",\n                                        children: currentEndpoint.example\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                                        lineNumber: 418,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                                lineNumber: 416,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                        lineNumber: 354,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                lineNumber: 333,\n                columnNumber: 9\n            }, this),\n            activeTab === 'examples' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow p-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold mb-6\",\n                        children: \"Integration Examples\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                        lineNumber: 428,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-semibold mb-4\",\n                                        children: \"JavaScript / Node.js\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                                        lineNumber: 433,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                        className: \"bg-gray-900 text-green-400 p-4 rounded-lg overflow-x-auto text-sm\",\n                                        children: \"// Fetch all tokens\\nconst response = await fetch('https://your-domain.com/api/external/tokens');\\nconst data = await response.json();\\n\\nif (data.success) {\\n  console.log('Total tokens:', data.data.totalCount);\\n  data.data.tokens.forEach(token => {\\n    console.log(`${token.name} (${token.symbol}): ${token.totalSupply} tokens`);\\n  });\\n}\\n\\n// Fetch specific investor\\nconst investorResponse = await fetch(\\n  'https://your-domain.com/api/external/investors?wallet=******************************************&stats=true'\\n);\\nconst investorData = await investorResponse.json();\\n\\nif (investorData.success) {\\n  const investor = investorData.data.investors[0];\\n  console.log(`Investor: ${investor.profile.firstName} ${investor.profile.lastName}`);\\n  console.log(`KYC Status: ${investor.kycStatus}`);\\n  console.log(`Total Investment: $${investor.statistics.totalValue}`);\\n}\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                                        lineNumber: 434,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                                lineNumber: 432,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-semibold mb-4\",\n                                        children: \"Python\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                                        lineNumber: 463,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                        className: \"bg-gray-900 text-green-400 p-4 rounded-lg overflow-x-auto text-sm\",\n                                        children: \"import requests\\nimport json\\n\\n# Fetch all tokens\\nresponse = requests.get('https://your-domain.com/api/external/tokens')\\ndata = response.json()\\n\\nif data['success']:\\n    print(f\\\"Total tokens: {data['data']['totalCount']}\\\")\\n    for token in data['data']['tokens']:\\n        print(f\\\"{token['name']} ({token['symbol']}): {token['totalSupply']} tokens\\\")\\n\\n# Fetch investor with statistics\\ninvestor_response = requests.get(\\n    'https://your-domain.com/api/external/investors',\\n    params={'wallet': '******************************************', 'stats': 'true'}\\n)\\ninvestor_data = investor_response.json()\\n\\nif investor_data['success']:\\n    investor = investor_data['data']['investors'][0]\\n    print(f\\\"Investor: {investor['profile']['firstName']} {investor['profile']['lastName']}\\\")\\n    print(f\\\"KYC Status: {investor['kycStatus']}\\\")\\n    print(f\\\"Total Investment: \".concat(investor['statistics']['totalValue'], '\")')\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                                        lineNumber: 464,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                                lineNumber: 462,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-semibold mb-4\",\n                                        children: \"cURL\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                                        lineNumber: 494,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                        className: \"bg-gray-900 text-green-400 p-4 rounded-lg overflow-x-auto text-sm\",\n                                        children: '# Get all tokens\\ncurl -X GET \"https://your-domain.com/api/external/tokens\" \\\\\\n  -H \"Content-Type: application/json\"\\n\\n# Get specific token\\ncurl -X GET \"https://your-domain.com/api/external/tokens?address=******************************************\" \\\\\\n  -H \"Content-Type: application/json\"\\n\\n# Get investor data with statistics\\ncurl -X GET \"https://your-domain.com/api/external/investors?stats=true\" \\\\\\n  -H \"Content-Type: application/json\"\\n\\n# Get specific investor by wallet\\ncurl -X GET \"https://your-domain.com/api/external/investors?wallet=******************************************\" \\\\\\n  -H \"Content-Type: application/json\"'\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                                        lineNumber: 495,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                                lineNumber: 493,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                        lineNumber: 430,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n                lineNumber: 427,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\external-api-docs\\\\page.tsx\",\n        lineNumber: 199,\n        columnNumber: 5\n    }, this);\n}\n_s(ExternalAPIDocsPage, \"yxhrrVOKF6xhI7wr8yzIR+CISxs=\");\n_c = ExternalAPIDocsPage;\nvar _c;\n$RefreshReg$(_c, \"ExternalAPIDocsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/external-api-docs/page.tsx\n"));

/***/ })

});