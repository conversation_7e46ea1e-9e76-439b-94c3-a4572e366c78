/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/admin/mint-tokens/route";
exports.ids = ["app/api/admin/mint-tokens/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fmint-tokens%2Froute&page=%2Fapi%2Fadmin%2Fmint-tokens%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fmint-tokens%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fmint-tokens%2Froute&page=%2Fapi%2Fadmin%2Fmint-tokens%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fmint-tokens%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_github_tokendev_newroo_admin_panel_src_app_api_admin_mint_tokens_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/admin/mint-tokens/route.ts */ \"(rsc)/./src/app/api/admin/mint-tokens/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/admin/mint-tokens/route\",\n        pathname: \"/api/admin/mint-tokens\",\n        filename: \"route\",\n        bundlePath: \"app/api/admin/mint-tokens/route\"\n    },\n    resolvedPagePath: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\api\\\\admin\\\\mint-tokens\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_github_tokendev_newroo_admin_panel_src_app_api_admin_mint_tokens_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fmint-tokens%2Froute&page=%2Fapi%2Fadmin%2Fmint-tokens%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fmint-tokens%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/admin/mint-tokens/route.ts":
/*!************************************************!*\
  !*** ./src/app/api/admin/mint-tokens/route.ts ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/address/checks.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/providers/provider-jsonrpc.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/wallet/wallet.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/contract/contract.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/utils/units.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/abi/interface.js\");\n\n\nconst SECURITY_TOKEN_CORE_ABI = [\n    \"function mint(address to, uint256 amount) external\",\n    \"function balanceOf(address account) external view returns (uint256)\",\n    \"function totalSupply() external view returns (uint256)\",\n    \"function maxSupply() external view returns (uint256)\"\n];\nasync function POST(request) {\n    try {\n        const { tokenAddress, toAddress, amount } = await request.json();\n        if (!tokenAddress || !toAddress || !amount) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Token address, recipient address, and amount are required'\n            }, {\n                status: 400\n            });\n        }\n        // Validate addresses\n        if (!ethers__WEBPACK_IMPORTED_MODULE_1__.isAddress(tokenAddress) || !ethers__WEBPACK_IMPORTED_MODULE_1__.isAddress(toAddress)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Invalid address format'\n            }, {\n                status: 400\n            });\n        }\n        // Validate amount\n        const amountNum = parseInt(amount);\n        if (isNaN(amountNum) || amountNum <= 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Invalid amount value'\n            }, {\n                status: 400\n            });\n        }\n        // Get environment variables\n        const rpcUrl = process.env.AMOY_RPC_URL;\n        const privateKey = process.env.CONTRACT_ADMIN_PRIVATE_KEY;\n        if (!rpcUrl || !privateKey) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Server configuration error'\n            }, {\n                status: 500\n            });\n        }\n        // Setup provider and signer\n        const provider = new ethers__WEBPACK_IMPORTED_MODULE_2__.JsonRpcProvider(rpcUrl);\n        const signer = new ethers__WEBPACK_IMPORTED_MODULE_3__.Wallet(privateKey, provider);\n        // Get token contract\n        const tokenContract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(tokenAddress, SECURITY_TOKEN_CORE_ABI, signer);\n        // Get current values for comparison\n        const currentBalance = await tokenContract.balanceOf(toAddress);\n        const currentTotalSupply = await tokenContract.totalSupply();\n        const maxSupply = await tokenContract.maxSupply();\n        console.log('Current balance:', currentBalance.toString());\n        console.log('Current total supply:', currentTotalSupply.toString());\n        console.log('Max supply:', maxSupply.toString());\n        console.log('Amount to mint:', amount);\n        // Convert to proper units (assuming 0 decimals based on our token)\n        const amountBN = ethers__WEBPACK_IMPORTED_MODULE_5__.parseUnits(amount, 0);\n        // Check if minting would exceed max supply\n        if (currentTotalSupply + amountBN > maxSupply) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Minting would exceed maximum supply'\n            }, {\n                status: 400\n            });\n        }\n        // Multiple strategies to handle Amoy testnet issues\n        const strategies = [\n            // Strategy 1: Standard contract call\n            async ()=>{\n                const tx = await tokenContract.mint(toAddress, amountBN, {\n                    gasLimit: 400000,\n                    gasPrice: ethers__WEBPACK_IMPORTED_MODULE_5__.parseUnits('30', 'gwei')\n                });\n                return tx.wait();\n            },\n            // Strategy 2: Raw transaction approach\n            async ()=>{\n                const iface = new ethers__WEBPACK_IMPORTED_MODULE_6__.Interface(SECURITY_TOKEN_CORE_ABI);\n                const data = iface.encodeFunctionData('mint', [\n                    toAddress,\n                    amountBN\n                ]);\n                const nonce = await signer.getNonce();\n                const tx = await signer.sendTransaction({\n                    to: tokenAddress,\n                    data: data,\n                    gasLimit: 400000,\n                    gasPrice: ethers__WEBPACK_IMPORTED_MODULE_5__.parseUnits('30', 'gwei'),\n                    nonce: nonce\n                });\n                return tx.wait();\n            }\n        ];\n        let lastError;\n        let receipt;\n        for(let i = 0; i < strategies.length; i++){\n            try {\n                console.log(`Attempting mint strategy ${i + 1}...`);\n                receipt = await strategies[i]();\n                console.log(`Mint successful with strategy ${i + 1}. Tx hash:`, receipt.hash);\n                break; // Success, exit loop\n            } catch (error) {\n                console.error(`Strategy ${i + 1} failed:`, error);\n                lastError = error;\n                // If this is not the last strategy, wait and try the next one\n                if (i < strategies.length - 1) {\n                    await new Promise((resolve)=>setTimeout(resolve, 2000));\n                }\n            }\n        }\n        // If all strategies failed\n        if (!receipt) {\n            throw lastError || new Error('All mint strategies failed');\n        }\n        // Verify minting\n        const newBalance = await tokenContract.balanceOf(toAddress);\n        const newTotalSupply = await tokenContract.totalSupply();\n        console.log('Tokens minted successfully:', receipt.hash);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'Tokens minted successfully',\n            txHash: receipt.hash,\n            toAddress,\n            amount: amount,\n            oldBalance: currentBalance.toString(),\n            newBalance: newBalance.toString(),\n            oldTotalSupply: currentTotalSupply.toString(),\n            newTotalSupply: newTotalSupply.toString(),\n            tokenAddress,\n            strategy: 'Enhanced with fallback strategies'\n        });\n    } catch (error) {\n        console.error('Error minting tokens:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: `Failed to mint tokens: ${error.message}`\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/admin/mint-tokens/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/ethers","vendor-chunks/@noble","vendor-chunks/@adraffy","vendor-chunks/aes-js"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fmint-tokens%2Froute&page=%2Fapi%2Fadmin%2Fmint-tokens%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fmint-tokens%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();