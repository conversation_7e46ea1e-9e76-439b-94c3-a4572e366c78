"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/modular-tokens/[address]/page",{

/***/ "(app-pages-browser)/./src/hooks/useModularToken.ts":
/*!**************************************!*\
  !*** ./src/hooks/useModularToken.ts ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useModularToken: () => (/* binding */ useModularToken)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/providers/provider-browser.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/contract/contract.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/utils/units.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/crypto/keccak.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/utils/utf8.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/constants/addresses.js\");\n/* harmony import */ var _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/contracts/SecurityTokenCore.json */ \"(app-pages-browser)/./src/contracts/SecurityTokenCore.json\");\n/* harmony import */ var _contracts_UpgradeManager_json__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contracts/UpgradeManager.json */ \"(app-pages-browser)/./src/contracts/UpgradeManager.json\");\n/* __next_internal_client_entry_do_not_use__ useModularToken auto */ \n\n// Import ABIs - extract the abi property from the JSON artifacts\n\n\n// Extract ABIs from artifacts\nconst SecurityTokenCoreABI = _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_1__.abi;\nconst UpgradeManagerABI = _contracts_UpgradeManager_json__WEBPACK_IMPORTED_MODULE_2__.abi;\n// Contract addresses from environment\nconst SECURITY_TOKEN_CORE_ADDRESS = \"******************************************\";\nconst UPGRADE_MANAGER_ADDRESS = \"0xb7a53dC340C2E5e823002B174629e2a44B8ed815\";\nfunction useModularToken(tokenAddress) {\n    const [provider, setProvider] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [signer, setSigner] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [tokenInfo, setTokenInfo] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [contractAddresses, setContractAddresses] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [upgradeInfo, setUpgradeInfo] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [pendingUpgrades, setPendingUpgrades] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [upgradeHistory, setUpgradeHistory] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    // Initialize provider and signer\n    const initializeProvider = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[initializeProvider]\": async ()=>{\n            try {\n                if ( true && window.ethereum) {\n                    const provider = new ethers__WEBPACK_IMPORTED_MODULE_3__.BrowserProvider(window.ethereum);\n                    await provider.send('eth_requestAccounts', []);\n                    const signer = await provider.getSigner();\n                    setProvider(provider);\n                    setSigner(signer);\n                    return {\n                        provider,\n                        signer\n                    };\n                } else {\n                    throw new Error('MetaMask not found');\n                }\n            } catch (error) {\n                console.error('Error initializing provider:', error);\n                setError('Failed to connect to wallet');\n                return null;\n            }\n        }\n    }[\"useModularToken.useCallback[initializeProvider]\"], []);\n    // Load token information\n    const loadTokenInfo = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[loadTokenInfo]\": async ()=>{\n            const contractAddress = tokenAddress || SECURITY_TOKEN_CORE_ADDRESS;\n            if (!provider || !contractAddress) return;\n            try {\n                setLoading(true);\n                const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(contractAddress, SecurityTokenCoreABI, provider);\n                const [name, symbol, version, totalSupply, maxSupply, decimals, paused, metadata] = await Promise.all([\n                    contract.name(),\n                    contract.symbol(),\n                    contract.version(),\n                    contract.totalSupply(),\n                    contract.maxSupply(),\n                    contract.decimals(),\n                    contract.paused(),\n                    contract.getTokenMetadata()\n                ]);\n                console.log('Token contract data:');\n                console.log('- Name:', name);\n                console.log('- Symbol:', symbol);\n                console.log('- Decimals (raw):', decimals);\n                console.log('- Decimals (number):', Number(decimals));\n                console.log('- Total Supply (raw):', totalSupply.toString());\n                console.log('- Max Supply (raw):', maxSupply.toString());\n                console.log('- Metadata (raw):', metadata);\n                console.log('- Token Price:', metadata[0]);\n                console.log('- Bonus Tiers:', metadata[1]);\n                console.log('- Token Details:', metadata[2]);\n                console.log('- Token Image URL:', metadata[3]);\n                const decimalsNumber = Number(decimals);\n                // Format with proper blockchain decimal precision\n                let formattedTotalSupply;\n                let formattedMaxSupply;\n                if (decimalsNumber === 0) {\n                    // For 0 decimals, show as whole numbers\n                    formattedTotalSupply = totalSupply.toString();\n                    formattedMaxSupply = maxSupply.toString();\n                } else {\n                    // For tokens with decimals, use ethers.formatUnits and preserve decimal precision\n                    const totalSupplyFormatted = ethers__WEBPACK_IMPORTED_MODULE_5__.formatUnits(totalSupply, decimalsNumber);\n                    const maxSupplyFormatted = ethers__WEBPACK_IMPORTED_MODULE_5__.formatUnits(maxSupply, decimalsNumber);\n                    // For blockchain tokens, show with appropriate decimal places\n                    // Remove excessive trailing zeros but keep meaningful precision\n                    formattedTotalSupply = parseFloat(totalSupplyFormatted).toFixed(Math.min(decimalsNumber, 6));\n                    formattedMaxSupply = parseFloat(maxSupplyFormatted).toFixed(Math.min(decimalsNumber, 6));\n                    // Remove trailing zeros after decimal point\n                    formattedTotalSupply = formattedTotalSupply.replace(/\\.?0+$/, '');\n                    formattedMaxSupply = formattedMaxSupply.replace(/\\.?0+$/, '');\n                    // Ensure at least .0 for tokens with decimals\n                    if (!formattedTotalSupply.includes('.')) formattedTotalSupply += '.0';\n                    if (!formattedMaxSupply.includes('.')) formattedMaxSupply += '.0';\n                }\n                console.log('- Total Supply (formatted):', formattedTotalSupply);\n                console.log('- Max Supply (formatted):', formattedMaxSupply);\n                setTokenInfo({\n                    name,\n                    symbol,\n                    version,\n                    totalSupply: formattedTotalSupply,\n                    maxSupply: formattedMaxSupply,\n                    decimals: decimalsNumber,\n                    paused,\n                    metadata: {\n                        tokenPrice: metadata[0],\n                        bonusTiers: metadata[1],\n                        tokenDetails: metadata[2],\n                        tokenImageUrl: metadata[3]\n                    }\n                });\n            } catch (error) {\n                console.error('Error loading token info:', error);\n                setError('Failed to load token information');\n            } finally{\n                setLoading(false);\n            }\n        }\n    }[\"useModularToken.useCallback[loadTokenInfo]\"], [\n        provider,\n        tokenAddress\n    ]);\n    // Load contract addresses\n    const loadContractAddresses = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[loadContractAddresses]\": async ()=>{\n            const contractAddress = tokenAddress || SECURITY_TOKEN_CORE_ADDRESS;\n            if (!provider || !contractAddress) return;\n            try {\n                const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(contractAddress, SecurityTokenCoreABI, provider);\n                // Define module IDs (these are keccak256 hashes of the module names)\n                const moduleIds = {\n                    'Identity Manager': ethers__WEBPACK_IMPORTED_MODULE_6__.keccak256(ethers__WEBPACK_IMPORTED_MODULE_7__.toUtf8Bytes(\"IDENTITY_MANAGER\")),\n                    'Compliance Engine': ethers__WEBPACK_IMPORTED_MODULE_6__.keccak256(ethers__WEBPACK_IMPORTED_MODULE_7__.toUtf8Bytes(\"COMPLIANCE_ENGINE\")),\n                    'Transfer Controller': ethers__WEBPACK_IMPORTED_MODULE_6__.keccak256(ethers__WEBPACK_IMPORTED_MODULE_7__.toUtf8Bytes(\"TRANSFER_CONTROLLER\")),\n                    'Agent Manager': ethers__WEBPACK_IMPORTED_MODULE_6__.keccak256(ethers__WEBPACK_IMPORTED_MODULE_7__.toUtf8Bytes(\"AGENT_MANAGER\")),\n                    'Emergency Manager': ethers__WEBPACK_IMPORTED_MODULE_6__.keccak256(ethers__WEBPACK_IMPORTED_MODULE_7__.toUtf8Bytes(\"EMERGENCY_MANAGER\")),\n                    'KYC Claims Module': ethers__WEBPACK_IMPORTED_MODULE_6__.keccak256(ethers__WEBPACK_IMPORTED_MODULE_7__.toUtf8Bytes(\"KYC_CLAIMS_MODULE\"))\n                };\n                console.log('Fetching module addresses...');\n                // Fetch all module addresses\n                const addresses = {\n                    'Token Contract': contractAddress\n                };\n                for (const [moduleName, moduleId] of Object.entries(moduleIds)){\n                    try {\n                        const moduleAddress = await contract.getModule(moduleId);\n                        if (moduleAddress && moduleAddress !== ethers__WEBPACK_IMPORTED_MODULE_8__.ZeroAddress) {\n                            addresses[moduleName] = moduleAddress;\n                            console.log(\"\".concat(moduleName, \": \").concat(moduleAddress));\n                        } else {\n                            console.log(\"\".concat(moduleName, \": Not registered\"));\n                        }\n                    } catch (error) {\n                        console.warn(\"Error fetching \".concat(moduleName, \" address:\"), error);\n                    }\n                }\n                setContractAddresses(addresses);\n            } catch (error) {\n                console.error('Error loading contract addresses:', error);\n            }\n        }\n    }[\"useModularToken.useCallback[loadContractAddresses]\"], [\n        provider,\n        tokenAddress\n    ]);\n    // Load upgrade information\n    const loadUpgradeInfo = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[loadUpgradeInfo]\": async ()=>{\n            if (!provider || !UPGRADE_MANAGER_ADDRESS) return;\n            try {\n                setLoading(true);\n                const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(UPGRADE_MANAGER_ADDRESS, UpgradeManagerABI, provider);\n                const [emergencyModeActive, registeredModules, upgradeDelay, emergencyModeDuration, pendingUpgradeIds] = await Promise.all([\n                    contract.isEmergencyModeActive(),\n                    contract.getRegisteredModules(),\n                    contract.UPGRADE_DELAY(),\n                    contract.EMERGENCY_MODE_DURATION(),\n                    contract.getPendingUpgradeIds()\n                ]);\n                setUpgradeInfo({\n                    emergencyModeActive,\n                    registeredModules,\n                    upgradeDelay: Number(upgradeDelay),\n                    emergencyModeDuration: Number(emergencyModeDuration)\n                });\n                // Load pending upgrades\n                const pendingUpgradesData = await Promise.all(pendingUpgradeIds.map({\n                    \"useModularToken.useCallback[loadUpgradeInfo]\": async (id)=>{\n                        const upgrade = await contract.pendingUpgrades(id);\n                        return {\n                            upgradeId: id,\n                            moduleId: upgrade.moduleId,\n                            proxy: upgrade.proxy,\n                            newImplementation: upgrade.newImplementation,\n                            executeTime: Number(upgrade.executeTime),\n                            executed: upgrade.executed,\n                            cancelled: upgrade.cancelled,\n                            description: upgrade.description\n                        };\n                    }\n                }[\"useModularToken.useCallback[loadUpgradeInfo]\"]));\n                setPendingUpgrades(pendingUpgradesData);\n                // Load upgrade history for SecurityTokenCore\n                const SECURITY_TOKEN_CORE_ID = ethers__WEBPACK_IMPORTED_MODULE_6__.keccak256(ethers__WEBPACK_IMPORTED_MODULE_7__.toUtf8Bytes(\"SECURITY_TOKEN_CORE\"));\n                const history = await contract.getUpgradeHistory(SECURITY_TOKEN_CORE_ID);\n                setUpgradeHistory(history.map({\n                    \"useModularToken.useCallback[loadUpgradeInfo]\": (record)=>({\n                            oldImplementation: record.oldImplementation,\n                            newImplementation: record.newImplementation,\n                            timestamp: Number(record.timestamp),\n                            executor: record.executor,\n                            version: record.version,\n                            isEmergency: record.isEmergency,\n                            description: record.description\n                        })\n                }[\"useModularToken.useCallback[loadUpgradeInfo]\"]));\n            } catch (error) {\n                console.error('Error loading upgrade info:', error);\n                setError('Failed to load upgrade information');\n            } finally{\n                setLoading(false);\n            }\n        }\n    }[\"useModularToken.useCallback[loadUpgradeInfo]\"], [\n        provider\n    ]);\n    // Mint tokens\n    const mintTokens = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[mintTokens]\": async (address, amount)=>{\n            const contractAddress = tokenAddress || SECURITY_TOKEN_CORE_ADDRESS;\n            if (!signer || !contractAddress) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(contractAddress, SecurityTokenCoreABI, signer);\n            try {\n                const decimals = (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.decimals) || 0;\n                const amountWei = ethers__WEBPACK_IMPORTED_MODULE_5__.parseUnits(amount, decimals);\n                const tx = await contract.mint(address, amountWei, {\n                    gasLimit: 400000\n                });\n                return tx.wait();\n            } catch (error) {\n                console.error('Mint tokens error:', error);\n                if (error.code === 'ACTION_REJECTED') {\n                    throw new Error('Transaction was rejected by user');\n                } else if (error.reason) {\n                    throw new Error(\"Contract error: \".concat(error.reason));\n                } else {\n                    throw new Error(\"Failed to mint tokens: \".concat(error.message));\n                }\n            }\n        }\n    }[\"useModularToken.useCallback[mintTokens]\"], [\n        signer,\n        tokenInfo,\n        tokenAddress\n    ]);\n    // Toggle pause state with multiple retry strategies\n    const togglePause = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[togglePause]\": async ()=>{\n            const contractAddress = tokenAddress || SECURITY_TOKEN_CORE_ADDRESS;\n            if (!signer || !contractAddress) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(contractAddress, SecurityTokenCoreABI, signer);\n            const isPausing = !(tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.paused);\n            const functionName = isPausing ? 'pause' : 'unpause';\n            // Try multiple strategies to handle Amoy testnet issues\n            const strategies = [\n                {\n                    \"useModularToken.useCallback[togglePause]\": // Strategy 1: Standard call with high gas\n                    async ()=>{\n                        const tx = isPausing ? await contract.pause({\n                            gasLimit: 500000,\n                            gasPrice: ethers__WEBPACK_IMPORTED_MODULE_5__.parseUnits('30', 'gwei')\n                        }) : await contract.unpause({\n                            gasLimit: 500000,\n                            gasPrice: ethers__WEBPACK_IMPORTED_MODULE_5__.parseUnits('30', 'gwei')\n                        });\n                        return tx.wait();\n                    }\n                }[\"useModularToken.useCallback[togglePause]\"],\n                {\n                    \"useModularToken.useCallback[togglePause]\": // Strategy 2: Raw transaction approach (like the working script)\n                    async ()=>{\n                        const functionSelector = isPausing ? '0x8456cb59' : '0x3f4ba83a';\n                        const nonce = await signer.getNonce();\n                        const tx = await signer.sendTransaction({\n                            to: contractAddress,\n                            data: functionSelector,\n                            gasLimit: 500000,\n                            gasPrice: ethers__WEBPACK_IMPORTED_MODULE_5__.parseUnits('30', 'gwei'),\n                            nonce: nonce\n                        });\n                        return tx.wait();\n                    }\n                }[\"useModularToken.useCallback[togglePause]\"]\n            ];\n            for(let i = 0; i < strategies.length; i++){\n                try {\n                    console.log(\"Attempting \".concat(functionName, \" strategy \").concat(i + 1, \"...\"));\n                    const result = await strategies[i]();\n                    console.log(\"\".concat(functionName, \" successful with strategy \").concat(i + 1));\n                    return result;\n                } catch (error) {\n                    console.error(\"Strategy \".concat(i + 1, \" failed:\"), error);\n                    if (error.code === 'ACTION_REJECTED') {\n                        throw new Error('Transaction was rejected by user');\n                    }\n                    // If this is the last strategy, throw a comprehensive error\n                    if (i === strategies.length - 1) {\n                        var _error_error;\n                        if (error.code === 'UNKNOWN_ERROR' && ((_error_error = error.error) === null || _error_error === void 0 ? void 0 : _error_error.code) === -32603) {\n                            throw new Error(\"Amoy testnet RPC error: The network is experiencing issues. Please try again in a few minutes, or use a different RPC endpoint.\");\n                        } else if (error.reason) {\n                            throw new Error(\"Contract error: \".concat(error.reason));\n                        } else {\n                            throw new Error(\"Failed to \".concat(functionName, \" token after trying multiple methods: \").concat(error.message));\n                        }\n                    }\n                    // Wait a bit before trying the next strategy\n                    await new Promise({\n                        \"useModularToken.useCallback[togglePause]\": (resolve)=>setTimeout(resolve, 1000)\n                    }[\"useModularToken.useCallback[togglePause]\"]);\n                }\n            }\n        }\n    }[\"useModularToken.useCallback[togglePause]\"], [\n        signer,\n        tokenInfo,\n        tokenAddress\n    ]);\n    // Schedule upgrade\n    const scheduleUpgrade = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[scheduleUpgrade]\": async (implementationAddress, description)=>{\n            if (!signer || !UPGRADE_MANAGER_ADDRESS) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(UPGRADE_MANAGER_ADDRESS, UpgradeManagerABI, signer);\n            const SECURITY_TOKEN_CORE_ID = ethers__WEBPACK_IMPORTED_MODULE_6__.keccak256(ethers__WEBPACK_IMPORTED_MODULE_7__.toUtf8Bytes(\"SECURITY_TOKEN_CORE\"));\n            const tx = await contract.scheduleUpgrade(SECURITY_TOKEN_CORE_ID, implementationAddress, description);\n            return tx.wait();\n        }\n    }[\"useModularToken.useCallback[scheduleUpgrade]\"], [\n        signer\n    ]);\n    // Execute upgrade\n    const executeUpgrade = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[executeUpgrade]\": async (upgradeId)=>{\n            if (!signer || !UPGRADE_MANAGER_ADDRESS) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(UPGRADE_MANAGER_ADDRESS, UpgradeManagerABI, signer);\n            const tx = await contract.executeUpgrade(upgradeId);\n            return tx.wait();\n        }\n    }[\"useModularToken.useCallback[executeUpgrade]\"], [\n        signer\n    ]);\n    // Emergency upgrade\n    const emergencyUpgrade = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[emergencyUpgrade]\": async (implementationAddress, description)=>{\n            if (!signer || !UPGRADE_MANAGER_ADDRESS) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(UPGRADE_MANAGER_ADDRESS, UpgradeManagerABI, signer);\n            const SECURITY_TOKEN_CORE_ID = ethers__WEBPACK_IMPORTED_MODULE_6__.keccak256(ethers__WEBPACK_IMPORTED_MODULE_7__.toUtf8Bytes(\"SECURITY_TOKEN_CORE\"));\n            const tx = await contract.emergencyUpgrade(SECURITY_TOKEN_CORE_ID, implementationAddress, description);\n            return tx.wait();\n        }\n    }[\"useModularToken.useCallback[emergencyUpgrade]\"], [\n        signer\n    ]);\n    // Toggle emergency mode\n    const toggleEmergencyMode = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[toggleEmergencyMode]\": async ()=>{\n            if (!signer || !UPGRADE_MANAGER_ADDRESS) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(UPGRADE_MANAGER_ADDRESS, UpgradeManagerABI, signer);\n            const tx = (upgradeInfo === null || upgradeInfo === void 0 ? void 0 : upgradeInfo.emergencyModeActive) ? await contract.deactivateEmergencyMode() : await contract.activateEmergencyMode();\n            return tx.wait();\n        }\n    }[\"useModularToken.useCallback[toggleEmergencyMode]\"], [\n        signer,\n        upgradeInfo\n    ]);\n    // Cancel upgrade\n    const cancelUpgrade = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[cancelUpgrade]\": async (upgradeId)=>{\n            if (!signer || !UPGRADE_MANAGER_ADDRESS) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(UPGRADE_MANAGER_ADDRESS, UpgradeManagerABI, signer);\n            const tx = await contract.cancelUpgrade(upgradeId);\n            return tx.wait();\n        }\n    }[\"useModularToken.useCallback[cancelUpgrade]\"], [\n        signer\n    ]);\n    // Update token price\n    const updateTokenPrice = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[updateTokenPrice]\": async (newPrice)=>{\n            const contractAddress = tokenAddress || SECURITY_TOKEN_CORE_ADDRESS;\n            if (!signer || !contractAddress) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(contractAddress, SecurityTokenCoreABI, signer);\n            try {\n                const tx = await contract.updateTokenPrice(newPrice, {\n                    gasLimit: 300000\n                });\n                return tx.wait();\n            } catch (error) {\n                console.error('Update token price error:', error);\n                if (error.code === 'ACTION_REJECTED') {\n                    throw new Error('Transaction was rejected by user');\n                } else if (error.reason) {\n                    throw new Error(\"Contract error: \".concat(error.reason));\n                } else {\n                    throw new Error(\"Failed to update token price: \".concat(error.message));\n                }\n            }\n        }\n    }[\"useModularToken.useCallback[updateTokenPrice]\"], [\n        signer,\n        tokenAddress\n    ]);\n    // Update bonus tiers\n    const updateBonusTiers = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[updateBonusTiers]\": async (newBonusTiers)=>{\n            const contractAddress = tokenAddress || SECURITY_TOKEN_CORE_ADDRESS;\n            if (!signer || !contractAddress) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(contractAddress, SecurityTokenCoreABI, signer);\n            try {\n                const tx = await contract.updateBonusTiers(newBonusTiers, {\n                    gasLimit: 300000\n                });\n                return tx.wait();\n            } catch (error) {\n                console.error('Update bonus tiers error:', error);\n                if (error.code === 'ACTION_REJECTED') {\n                    throw new Error('Transaction was rejected by user');\n                } else if (error.reason) {\n                    throw new Error(\"Contract error: \".concat(error.reason));\n                } else {\n                    throw new Error(\"Failed to update bonus tiers: \".concat(error.message));\n                }\n            }\n        }\n    }[\"useModularToken.useCallback[updateBonusTiers]\"], [\n        signer,\n        tokenAddress\n    ]);\n    // Update max supply\n    const updateMaxSupply = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[updateMaxSupply]\": async (newMaxSupply)=>{\n            const contractAddress = tokenAddress || SECURITY_TOKEN_CORE_ADDRESS;\n            if (!signer || !contractAddress) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(contractAddress, SecurityTokenCoreABI, signer);\n            try {\n                const maxSupplyWei = ethers__WEBPACK_IMPORTED_MODULE_5__.parseUnits(newMaxSupply, (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.decimals) || 0);\n                const tx = await contract.updateMaxSupply(maxSupplyWei, {\n                    gasLimit: 300000\n                });\n                return tx.wait();\n            } catch (error) {\n                console.error('Update max supply error:', error);\n                if (error.code === 'ACTION_REJECTED') {\n                    throw new Error('Transaction was rejected by user');\n                } else if (error.reason) {\n                    throw new Error(\"Contract error: \".concat(error.reason));\n                } else {\n                    throw new Error(\"Failed to update max supply: \".concat(error.message));\n                }\n            }\n        }\n    }[\"useModularToken.useCallback[updateMaxSupply]\"], [\n        signer,\n        tokenInfo,\n        tokenAddress\n    ]);\n    // Add to whitelist with identity registration\n    const addToWhitelist = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[addToWhitelist]\": async (address)=>{\n            const contractAddress = tokenAddress || SECURITY_TOKEN_CORE_ADDRESS;\n            if (!signer || !contractAddress) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(contractAddress, SecurityTokenCoreABI, signer);\n            try {\n                // First check if the address is already verified/registered\n                const isVerified = await contract.isVerified(address);\n                if (!isVerified) {\n                    // Need to register identity first - use the API approach for this\n                    throw new Error('Address must be registered first. Please use the API method or register the identity manually.');\n                }\n                // Now try to add to whitelist\n                const tx = await contract.addToWhitelist(address, {\n                    gasLimit: 400000,\n                    gasPrice: ethers__WEBPACK_IMPORTED_MODULE_5__.parseUnits('30', 'gwei')\n                });\n                return tx.wait();\n            } catch (error) {\n                console.error('Add to whitelist error:', error);\n                if (error.code === 'ACTION_REJECTED') {\n                    throw new Error('Transaction was rejected by user');\n                } else if (error.reason) {\n                    throw new Error(\"Contract error: \".concat(error.reason));\n                } else if (error.message.includes('identity not registered')) {\n                    throw new Error('Address must be registered first. Please use the API method which handles registration automatically.');\n                } else {\n                    throw new Error(\"Failed to add address to whitelist: \".concat(error.message));\n                }\n            }\n        }\n    }[\"useModularToken.useCallback[addToWhitelist]\"], [\n        signer,\n        tokenAddress\n    ]);\n    // Remove from whitelist\n    const removeFromWhitelist = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[removeFromWhitelist]\": async (address)=>{\n            const contractAddress = tokenAddress || SECURITY_TOKEN_CORE_ADDRESS;\n            if (!signer || !contractAddress) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(contractAddress, SecurityTokenCoreABI, signer);\n            try {\n                const tx = await contract.removeFromWhitelist(address, {\n                    gasLimit: 300000\n                });\n                return tx.wait();\n            } catch (error) {\n                console.error('Remove from whitelist error:', error);\n                if (error.code === 'ACTION_REJECTED') {\n                    throw new Error('Transaction was rejected by user');\n                } else if (error.reason) {\n                    throw new Error(\"Contract error: \".concat(error.reason));\n                } else {\n                    throw new Error(\"Failed to remove address from whitelist: \".concat(error.message));\n                }\n            }\n        }\n    }[\"useModularToken.useCallback[removeFromWhitelist]\"], [\n        signer,\n        tokenAddress\n    ]);\n    // Refresh all data\n    const refreshData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[refreshData]\": async ()=>{\n            await Promise.all([\n                loadTokenInfo(),\n                loadContractAddresses(),\n                loadUpgradeInfo()\n            ]);\n        }\n    }[\"useModularToken.useCallback[refreshData]\"], [\n        loadTokenInfo,\n        loadContractAddresses,\n        loadUpgradeInfo\n    ]);\n    // Initialize on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useModularToken.useEffect\": ()=>{\n            initializeProvider();\n        }\n    }[\"useModularToken.useEffect\"], [\n        initializeProvider\n    ]);\n    // Load data when provider is ready\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useModularToken.useEffect\": ()=>{\n            if (provider && signer) {\n                refreshData();\n            }\n        }\n    }[\"useModularToken.useEffect\"], [\n        provider,\n        signer,\n        refreshData\n    ]);\n    return {\n        // State\n        provider,\n        signer,\n        tokenInfo,\n        contractAddresses,\n        upgradeInfo,\n        pendingUpgrades,\n        upgradeHistory,\n        loading,\n        error,\n        // Actions\n        initializeProvider,\n        loadTokenInfo,\n        loadContractAddresses,\n        loadUpgradeInfo,\n        mintTokens,\n        togglePause,\n        scheduleUpgrade,\n        executeUpgrade,\n        emergencyUpgrade,\n        toggleEmergencyMode,\n        cancelUpgrade,\n        refreshData,\n        // Admin Functions\n        updateTokenPrice,\n        updateBonusTiers,\n        updateMaxSupply,\n        addToWhitelist,\n        removeFromWhitelist,\n        // Utilities\n        setError,\n        setLoading\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useModularToken.ts\n"));

/***/ })

});