/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/admin/update-price/route";
exports.ids = ["app/api/admin/update-price/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fupdate-price%2Froute&page=%2Fapi%2Fadmin%2Fupdate-price%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fupdate-price%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fupdate-price%2Froute&page=%2Fapi%2Fadmin%2Fupdate-price%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fupdate-price%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_github_tokendev_newroo_admin_panel_src_app_api_admin_update_price_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/admin/update-price/route.ts */ \"(rsc)/./src/app/api/admin/update-price/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/admin/update-price/route\",\n        pathname: \"/api/admin/update-price\",\n        filename: \"route\",\n        bundlePath: \"app/api/admin/update-price/route\"\n    },\n    resolvedPagePath: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\api\\\\admin\\\\update-price\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_github_tokendev_newroo_admin_panel_src_app_api_admin_update_price_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fupdate-price%2Froute&page=%2Fapi%2Fadmin%2Fupdate-price%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fupdate-price%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/admin/update-price/route.ts":
/*!*************************************************!*\
  !*** ./src/app/api/admin/update-price/route.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/address/checks.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/providers/provider-jsonrpc.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/wallet/wallet.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/contract/contract.js\");\n\n\nconst SECURITY_TOKEN_CORE_ABI = [\n    \"function updateTokenPrice(string memory tokenPrice_) external\",\n    \"function getTokenMetadata() external view returns (string memory tokenPrice, string memory bonusTiers, string memory tokenDetails, string memory tokenImageUrl)\",\n    \"function hasRole(bytes32 role, address account) external view returns (bool)\"\n];\nasync function POST(request) {\n    try {\n        const { tokenAddress, newPrice } = await request.json();\n        if (!tokenAddress || !newPrice) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Token address and new price are required'\n            }, {\n                status: 400\n            });\n        }\n        // Validate addresses\n        if (!ethers__WEBPACK_IMPORTED_MODULE_1__.isAddress(tokenAddress)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Invalid token address format'\n            }, {\n                status: 400\n            });\n        }\n        // Get environment variables\n        const rpcUrl = process.env.AMOY_RPC_URL;\n        const privateKey = process.env.CONTRACT_ADMIN_PRIVATE_KEY;\n        if (!rpcUrl || !privateKey) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Server configuration error'\n            }, {\n                status: 500\n            });\n        }\n        // Setup provider and signer\n        const provider = new ethers__WEBPACK_IMPORTED_MODULE_2__.JsonRpcProvider(rpcUrl);\n        const signer = new ethers__WEBPACK_IMPORTED_MODULE_3__.Wallet(privateKey, provider);\n        // Get token contract\n        const tokenContract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(tokenAddress, SECURITY_TOKEN_CORE_ABI, signer);\n        // Check if the API signer has admin role\n        const DEFAULT_ADMIN_ROLE = '0x0000000000000000000000000000000000000000000000000000000000000000';\n        const signerAddress = await signer.getAddress();\n        const hasAdminRole = await tokenContract.hasRole(DEFAULT_ADMIN_ROLE, signerAddress);\n        console.log('API Signer address:', signerAddress);\n        console.log('Has DEFAULT_ADMIN_ROLE:', hasAdminRole);\n        if (!hasAdminRole) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: `API signer (${signerAddress}) does not have DEFAULT_ADMIN_ROLE on token ${tokenAddress}. Contact the token admin to grant this role.`,\n                signerAddress,\n                tokenAddress,\n                hasAdminRole: false\n            }, {\n                status: 403\n            });\n        }\n        // Get current metadata for comparison\n        const currentMetadata = await tokenContract.getTokenMetadata();\n        console.log('Current price:', currentMetadata[0]);\n        console.log('New price:', newPrice);\n        // Update token price\n        const tx = await tokenContract.updateTokenPrice(newPrice);\n        await tx.wait();\n        // Verify update\n        const updatedMetadata = await tokenContract.getTokenMetadata();\n        console.log('Price updated successfully:', tx.hash);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'Token price updated successfully',\n            txHash: tx.hash,\n            oldPrice: currentMetadata[0],\n            newPrice: updatedMetadata[0],\n            tokenAddress\n        });\n    } catch (error) {\n        console.error('Error updating token price:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: `Failed to update token price: ${error.message}`\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS9hZG1pbi91cGRhdGUtcHJpY2Uvcm91dGUudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQXdEO0FBQ3hCO0FBRWhDLE1BQU1FLDBCQUEwQjtJQUM5QjtJQUNBO0lBQ0E7Q0FDRDtBQUVNLGVBQWVDLEtBQUtDLE9BQW9CO0lBQzdDLElBQUk7UUFDRixNQUFNLEVBQUVDLFlBQVksRUFBRUMsUUFBUSxFQUFFLEdBQUcsTUFBTUYsUUFBUUcsSUFBSTtRQUVyRCxJQUFJLENBQUNGLGdCQUFnQixDQUFDQyxVQUFVO1lBQzlCLE9BQU9OLHFEQUFZQSxDQUFDTyxJQUFJLENBQ3RCO2dCQUFFQyxPQUFPO1lBQTJDLEdBQ3BEO2dCQUFFQyxRQUFRO1lBQUk7UUFFbEI7UUFFQSxxQkFBcUI7UUFDckIsSUFBSSxDQUFDUiw2Q0FBZ0IsQ0FBQ0ksZUFBZTtZQUNuQyxPQUFPTCxxREFBWUEsQ0FBQ08sSUFBSSxDQUN0QjtnQkFBRUMsT0FBTztZQUErQixHQUN4QztnQkFBRUMsUUFBUTtZQUFJO1FBRWxCO1FBRUEsNEJBQTRCO1FBQzVCLE1BQU1FLFNBQVNDLFFBQVFDLEdBQUcsQ0FBQ0MsWUFBWTtRQUN2QyxNQUFNQyxhQUFhSCxRQUFRQyxHQUFHLENBQUNHLDBCQUEwQjtRQUV6RCxJQUFJLENBQUNMLFVBQVUsQ0FBQ0ksWUFBWTtZQUMxQixPQUFPZixxREFBWUEsQ0FBQ08sSUFBSSxDQUN0QjtnQkFBRUMsT0FBTztZQUE2QixHQUN0QztnQkFBRUMsUUFBUTtZQUFJO1FBRWxCO1FBRUEsNEJBQTRCO1FBQzVCLE1BQU1RLFdBQVcsSUFBSWhCLG1EQUFzQixDQUFDVTtRQUM1QyxNQUFNUSxTQUFTLElBQUlsQiwwQ0FBYSxDQUFDYyxZQUFZRTtRQUU3QyxxQkFBcUI7UUFDckIsTUFBTUksZ0JBQWdCLElBQUlwQiw0Q0FBZSxDQUFDSSxjQUFjSCx5QkFBeUJpQjtRQUVqRix5Q0FBeUM7UUFDekMsTUFBTUkscUJBQXFCO1FBQzNCLE1BQU1DLGdCQUFnQixNQUFNTCxPQUFPTSxVQUFVO1FBQzdDLE1BQU1DLGVBQWUsTUFBTUwsY0FBY00sT0FBTyxDQUFDSixvQkFBb0JDO1FBRXJFSSxRQUFRQyxHQUFHLENBQUMsdUJBQXVCTDtRQUNuQ0ksUUFBUUMsR0FBRyxDQUFDLDJCQUEyQkg7UUFFdkMsSUFBSSxDQUFDQSxjQUFjO1lBQ2pCLE9BQU8xQixxREFBWUEsQ0FBQ08sSUFBSSxDQUN0QjtnQkFDRUMsT0FBTyxDQUFDLFlBQVksRUFBRWdCLGNBQWMsNENBQTRDLEVBQUVuQixhQUFhLDZDQUE2QyxDQUFDO2dCQUM3SW1CO2dCQUNBbkI7Z0JBQ0FxQixjQUFjO1lBQ2hCLEdBQ0E7Z0JBQUVqQixRQUFRO1lBQUk7UUFFbEI7UUFFQSxzQ0FBc0M7UUFDdEMsTUFBTXFCLGtCQUFrQixNQUFNVCxjQUFjVSxnQkFBZ0I7UUFDNURILFFBQVFDLEdBQUcsQ0FBQyxrQkFBa0JDLGVBQWUsQ0FBQyxFQUFFO1FBQ2hERixRQUFRQyxHQUFHLENBQUMsY0FBY3ZCO1FBRTFCLHFCQUFxQjtRQUNyQixNQUFNMEIsS0FBSyxNQUFNWCxjQUFjWSxnQkFBZ0IsQ0FBQzNCO1FBQ2hELE1BQU0wQixHQUFHRSxJQUFJO1FBRWIsZ0JBQWdCO1FBQ2hCLE1BQU1DLGtCQUFrQixNQUFNZCxjQUFjVSxnQkFBZ0I7UUFFNURILFFBQVFDLEdBQUcsQ0FBQywrQkFBK0JHLEdBQUdJLElBQUk7UUFFbEQsT0FBT3BDLHFEQUFZQSxDQUFDTyxJQUFJLENBQUM7WUFDdkI4QixTQUFTO1lBQ1RDLFNBQVM7WUFDVEMsUUFBUVAsR0FBR0ksSUFBSTtZQUNmSSxVQUFVVixlQUFlLENBQUMsRUFBRTtZQUM1QnhCLFVBQVU2QixlQUFlLENBQUMsRUFBRTtZQUM1QjlCO1FBQ0Y7SUFFRixFQUFFLE9BQU9HLE9BQVk7UUFDbkJvQixRQUFRcEIsS0FBSyxDQUFDLCtCQUErQkE7UUFDN0MsT0FBT1IscURBQVlBLENBQUNPLElBQUksQ0FDdEI7WUFBRUMsT0FBTyxDQUFDLDhCQUE4QixFQUFFQSxNQUFNOEIsT0FBTyxFQUFFO1FBQUMsR0FDMUQ7WUFBRTdCLFFBQVE7UUFBSTtJQUVsQjtBQUNGIiwic291cmNlcyI6WyJEOlxcZ2l0aHViXFx0b2tlbmRldi1uZXdyb29cXGFkbWluLXBhbmVsXFxzcmNcXGFwcFxcYXBpXFxhZG1pblxcdXBkYXRlLXByaWNlXFxyb3V0ZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBOZXh0UmVxdWVzdCwgTmV4dFJlc3BvbnNlIH0gZnJvbSAnbmV4dC9zZXJ2ZXInO1xuaW1wb3J0IHsgZXRoZXJzIH0gZnJvbSAnZXRoZXJzJztcblxuY29uc3QgU0VDVVJJVFlfVE9LRU5fQ09SRV9BQkkgPSBbXG4gIFwiZnVuY3Rpb24gdXBkYXRlVG9rZW5QcmljZShzdHJpbmcgbWVtb3J5IHRva2VuUHJpY2VfKSBleHRlcm5hbFwiLFxuICBcImZ1bmN0aW9uIGdldFRva2VuTWV0YWRhdGEoKSBleHRlcm5hbCB2aWV3IHJldHVybnMgKHN0cmluZyBtZW1vcnkgdG9rZW5QcmljZSwgc3RyaW5nIG1lbW9yeSBib251c1RpZXJzLCBzdHJpbmcgbWVtb3J5IHRva2VuRGV0YWlscywgc3RyaW5nIG1lbW9yeSB0b2tlbkltYWdlVXJsKVwiLFxuICBcImZ1bmN0aW9uIGhhc1JvbGUoYnl0ZXMzMiByb2xlLCBhZGRyZXNzIGFjY291bnQpIGV4dGVybmFsIHZpZXcgcmV0dXJucyAoYm9vbClcIlxuXTtcblxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIFBPU1QocmVxdWVzdDogTmV4dFJlcXVlc3QpIHtcbiAgdHJ5IHtcbiAgICBjb25zdCB7IHRva2VuQWRkcmVzcywgbmV3UHJpY2UgfSA9IGF3YWl0IHJlcXVlc3QuanNvbigpO1xuXG4gICAgaWYgKCF0b2tlbkFkZHJlc3MgfHwgIW5ld1ByaWNlKSB7XG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXG4gICAgICAgIHsgZXJyb3I6ICdUb2tlbiBhZGRyZXNzIGFuZCBuZXcgcHJpY2UgYXJlIHJlcXVpcmVkJyB9LFxuICAgICAgICB7IHN0YXR1czogNDAwIH1cbiAgICAgICk7XG4gICAgfVxuXG4gICAgLy8gVmFsaWRhdGUgYWRkcmVzc2VzXG4gICAgaWYgKCFldGhlcnMuaXNBZGRyZXNzKHRva2VuQWRkcmVzcykpIHtcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgICAgeyBlcnJvcjogJ0ludmFsaWQgdG9rZW4gYWRkcmVzcyBmb3JtYXQnIH0sXG4gICAgICAgIHsgc3RhdHVzOiA0MDAgfVxuICAgICAgKTtcbiAgICB9XG5cbiAgICAvLyBHZXQgZW52aXJvbm1lbnQgdmFyaWFibGVzXG4gICAgY29uc3QgcnBjVXJsID0gcHJvY2Vzcy5lbnYuQU1PWV9SUENfVVJMO1xuICAgIGNvbnN0IHByaXZhdGVLZXkgPSBwcm9jZXNzLmVudi5DT05UUkFDVF9BRE1JTl9QUklWQVRFX0tFWTtcblxuICAgIGlmICghcnBjVXJsIHx8ICFwcml2YXRlS2V5KSB7XG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXG4gICAgICAgIHsgZXJyb3I6ICdTZXJ2ZXIgY29uZmlndXJhdGlvbiBlcnJvcicgfSxcbiAgICAgICAgeyBzdGF0dXM6IDUwMCB9XG4gICAgICApO1xuICAgIH1cblxuICAgIC8vIFNldHVwIHByb3ZpZGVyIGFuZCBzaWduZXJcbiAgICBjb25zdCBwcm92aWRlciA9IG5ldyBldGhlcnMuSnNvblJwY1Byb3ZpZGVyKHJwY1VybCk7XG4gICAgY29uc3Qgc2lnbmVyID0gbmV3IGV0aGVycy5XYWxsZXQocHJpdmF0ZUtleSwgcHJvdmlkZXIpO1xuXG4gICAgLy8gR2V0IHRva2VuIGNvbnRyYWN0XG4gICAgY29uc3QgdG9rZW5Db250cmFjdCA9IG5ldyBldGhlcnMuQ29udHJhY3QodG9rZW5BZGRyZXNzLCBTRUNVUklUWV9UT0tFTl9DT1JFX0FCSSwgc2lnbmVyKTtcblxuICAgIC8vIENoZWNrIGlmIHRoZSBBUEkgc2lnbmVyIGhhcyBhZG1pbiByb2xlXG4gICAgY29uc3QgREVGQVVMVF9BRE1JTl9ST0xFID0gJzB4MDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMCc7XG4gICAgY29uc3Qgc2lnbmVyQWRkcmVzcyA9IGF3YWl0IHNpZ25lci5nZXRBZGRyZXNzKCk7XG4gICAgY29uc3QgaGFzQWRtaW5Sb2xlID0gYXdhaXQgdG9rZW5Db250cmFjdC5oYXNSb2xlKERFRkFVTFRfQURNSU5fUk9MRSwgc2lnbmVyQWRkcmVzcyk7XG5cbiAgICBjb25zb2xlLmxvZygnQVBJIFNpZ25lciBhZGRyZXNzOicsIHNpZ25lckFkZHJlc3MpO1xuICAgIGNvbnNvbGUubG9nKCdIYXMgREVGQVVMVF9BRE1JTl9ST0xFOicsIGhhc0FkbWluUm9sZSk7XG5cbiAgICBpZiAoIWhhc0FkbWluUm9sZSkge1xuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgICB7XG4gICAgICAgICAgZXJyb3I6IGBBUEkgc2lnbmVyICgke3NpZ25lckFkZHJlc3N9KSBkb2VzIG5vdCBoYXZlIERFRkFVTFRfQURNSU5fUk9MRSBvbiB0b2tlbiAke3Rva2VuQWRkcmVzc30uIENvbnRhY3QgdGhlIHRva2VuIGFkbWluIHRvIGdyYW50IHRoaXMgcm9sZS5gLFxuICAgICAgICAgIHNpZ25lckFkZHJlc3MsXG4gICAgICAgICAgdG9rZW5BZGRyZXNzLFxuICAgICAgICAgIGhhc0FkbWluUm9sZTogZmFsc2VcbiAgICAgICAgfSxcbiAgICAgICAgeyBzdGF0dXM6IDQwMyB9XG4gICAgICApO1xuICAgIH1cblxuICAgIC8vIEdldCBjdXJyZW50IG1ldGFkYXRhIGZvciBjb21wYXJpc29uXG4gICAgY29uc3QgY3VycmVudE1ldGFkYXRhID0gYXdhaXQgdG9rZW5Db250cmFjdC5nZXRUb2tlbk1ldGFkYXRhKCk7XG4gICAgY29uc29sZS5sb2coJ0N1cnJlbnQgcHJpY2U6JywgY3VycmVudE1ldGFkYXRhWzBdKTtcbiAgICBjb25zb2xlLmxvZygnTmV3IHByaWNlOicsIG5ld1ByaWNlKTtcblxuICAgIC8vIFVwZGF0ZSB0b2tlbiBwcmljZVxuICAgIGNvbnN0IHR4ID0gYXdhaXQgdG9rZW5Db250cmFjdC51cGRhdGVUb2tlblByaWNlKG5ld1ByaWNlKTtcbiAgICBhd2FpdCB0eC53YWl0KCk7XG5cbiAgICAvLyBWZXJpZnkgdXBkYXRlXG4gICAgY29uc3QgdXBkYXRlZE1ldGFkYXRhID0gYXdhaXQgdG9rZW5Db250cmFjdC5nZXRUb2tlbk1ldGFkYXRhKCk7XG5cbiAgICBjb25zb2xlLmxvZygnUHJpY2UgdXBkYXRlZCBzdWNjZXNzZnVsbHk6JywgdHguaGFzaCk7XG5cbiAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oe1xuICAgICAgc3VjY2VzczogdHJ1ZSxcbiAgICAgIG1lc3NhZ2U6ICdUb2tlbiBwcmljZSB1cGRhdGVkIHN1Y2Nlc3NmdWxseScsXG4gICAgICB0eEhhc2g6IHR4Lmhhc2gsXG4gICAgICBvbGRQcmljZTogY3VycmVudE1ldGFkYXRhWzBdLFxuICAgICAgbmV3UHJpY2U6IHVwZGF0ZWRNZXRhZGF0YVswXSxcbiAgICAgIHRva2VuQWRkcmVzc1xuICAgIH0pO1xuXG4gIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcbiAgICBjb25zb2xlLmVycm9yKCdFcnJvciB1cGRhdGluZyB0b2tlbiBwcmljZTonLCBlcnJvcik7XG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgeyBlcnJvcjogYEZhaWxlZCB0byB1cGRhdGUgdG9rZW4gcHJpY2U6ICR7ZXJyb3IubWVzc2FnZX1gIH0sXG4gICAgICB7IHN0YXR1czogNTAwIH1cbiAgICApO1xuICB9XG59XG4iXSwibmFtZXMiOlsiTmV4dFJlc3BvbnNlIiwiZXRoZXJzIiwiU0VDVVJJVFlfVE9LRU5fQ09SRV9BQkkiLCJQT1NUIiwicmVxdWVzdCIsInRva2VuQWRkcmVzcyIsIm5ld1ByaWNlIiwianNvbiIsImVycm9yIiwic3RhdHVzIiwiaXNBZGRyZXNzIiwicnBjVXJsIiwicHJvY2VzcyIsImVudiIsIkFNT1lfUlBDX1VSTCIsInByaXZhdGVLZXkiLCJDT05UUkFDVF9BRE1JTl9QUklWQVRFX0tFWSIsInByb3ZpZGVyIiwiSnNvblJwY1Byb3ZpZGVyIiwic2lnbmVyIiwiV2FsbGV0IiwidG9rZW5Db250cmFjdCIsIkNvbnRyYWN0IiwiREVGQVVMVF9BRE1JTl9ST0xFIiwic2lnbmVyQWRkcmVzcyIsImdldEFkZHJlc3MiLCJoYXNBZG1pblJvbGUiLCJoYXNSb2xlIiwiY29uc29sZSIsImxvZyIsImN1cnJlbnRNZXRhZGF0YSIsImdldFRva2VuTWV0YWRhdGEiLCJ0eCIsInVwZGF0ZVRva2VuUHJpY2UiLCJ3YWl0IiwidXBkYXRlZE1ldGFkYXRhIiwiaGFzaCIsInN1Y2Nlc3MiLCJtZXNzYWdlIiwidHhIYXNoIiwib2xkUHJpY2UiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/admin/update-price/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/ethers","vendor-chunks/@noble","vendor-chunks/@adraffy","vendor-chunks/aes-js"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fupdate-price%2Froute&page=%2Fapi%2Fadmin%2Fupdate-price%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fupdate-price%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();