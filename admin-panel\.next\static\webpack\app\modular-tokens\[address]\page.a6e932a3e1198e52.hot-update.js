"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/modular-tokens/[address]/page",{

/***/ "(app-pages-browser)/./src/hooks/useModularToken.ts":
/*!**************************************!*\
  !*** ./src/hooks/useModularToken.ts ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useModularToken: () => (/* binding */ useModularToken)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/providers/provider-browser.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/contract/contract.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/utils/units.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/crypto/keccak.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/utils/utf8.js\");\n/* harmony import */ var _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/contracts/SecurityTokenCore.json */ \"(app-pages-browser)/./src/contracts/SecurityTokenCore.json\");\n/* harmony import */ var _contracts_UpgradeManager_json__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contracts/UpgradeManager.json */ \"(app-pages-browser)/./src/contracts/UpgradeManager.json\");\n/* __next_internal_client_entry_do_not_use__ useModularToken auto */ \n\n// Import ABIs - extract the abi property from the JSON artifacts\n\n\n// Extract ABIs from artifacts\nconst SecurityTokenCoreABI = _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_1__.abi;\nconst UpgradeManagerABI = _contracts_UpgradeManager_json__WEBPACK_IMPORTED_MODULE_2__.abi;\n// Contract addresses from environment\nconst SECURITY_TOKEN_CORE_ADDRESS = \"******************************************\";\nconst UPGRADE_MANAGER_ADDRESS = \"0xb7a53dC340C2E5e823002B174629e2a44B8ed815\";\nfunction useModularToken(tokenAddress) {\n    const [provider, setProvider] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [signer, setSigner] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [tokenInfo, setTokenInfo] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [upgradeInfo, setUpgradeInfo] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [pendingUpgrades, setPendingUpgrades] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [upgradeHistory, setUpgradeHistory] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    // Initialize provider and signer\n    const initializeProvider = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[initializeProvider]\": async ()=>{\n            try {\n                if ( true && window.ethereum) {\n                    const provider = new ethers__WEBPACK_IMPORTED_MODULE_3__.BrowserProvider(window.ethereum);\n                    await provider.send('eth_requestAccounts', []);\n                    const signer = await provider.getSigner();\n                    setProvider(provider);\n                    setSigner(signer);\n                    return {\n                        provider,\n                        signer\n                    };\n                } else {\n                    throw new Error('MetaMask not found');\n                }\n            } catch (error) {\n                console.error('Error initializing provider:', error);\n                setError('Failed to connect to wallet');\n                return null;\n            }\n        }\n    }[\"useModularToken.useCallback[initializeProvider]\"], []);\n    // Load token information\n    const loadTokenInfo = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[loadTokenInfo]\": async ()=>{\n            const contractAddress = tokenAddress || SECURITY_TOKEN_CORE_ADDRESS;\n            if (!provider || !contractAddress) return;\n            try {\n                setLoading(true);\n                const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(SECURITY_TOKEN_CORE_ADDRESS, SecurityTokenCoreABI, provider);\n                const [name, symbol, version, totalSupply, maxSupply, decimals, paused, metadata] = await Promise.all([\n                    contract.name(),\n                    contract.symbol(),\n                    contract.version(),\n                    contract.totalSupply(),\n                    contract.maxSupply(),\n                    contract.decimals(),\n                    contract.paused(),\n                    contract.getTokenMetadata()\n                ]);\n                setTokenInfo({\n                    name,\n                    symbol,\n                    version,\n                    totalSupply: ethers__WEBPACK_IMPORTED_MODULE_5__.formatUnits(totalSupply, decimals),\n                    maxSupply: ethers__WEBPACK_IMPORTED_MODULE_5__.formatUnits(maxSupply, decimals),\n                    decimals,\n                    paused,\n                    metadata: {\n                        tokenPrice: metadata[0],\n                        bonusTiers: metadata[1],\n                        tokenDetails: metadata[2],\n                        tokenImageUrl: metadata[3]\n                    }\n                });\n            } catch (error) {\n                console.error('Error loading token info:', error);\n                setError('Failed to load token information');\n            } finally{\n                setLoading(false);\n            }\n        }\n    }[\"useModularToken.useCallback[loadTokenInfo]\"], [\n        provider\n    ]);\n    // Load upgrade information\n    const loadUpgradeInfo = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[loadUpgradeInfo]\": async ()=>{\n            if (!provider || !UPGRADE_MANAGER_ADDRESS) return;\n            try {\n                setLoading(true);\n                const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(UPGRADE_MANAGER_ADDRESS, UpgradeManagerABI, provider);\n                const [emergencyModeActive, registeredModules, upgradeDelay, emergencyModeDuration, pendingUpgradeIds] = await Promise.all([\n                    contract.isEmergencyModeActive(),\n                    contract.getRegisteredModules(),\n                    contract.UPGRADE_DELAY(),\n                    contract.EMERGENCY_MODE_DURATION(),\n                    contract.getPendingUpgradeIds()\n                ]);\n                setUpgradeInfo({\n                    emergencyModeActive,\n                    registeredModules,\n                    upgradeDelay: Number(upgradeDelay),\n                    emergencyModeDuration: Number(emergencyModeDuration)\n                });\n                // Load pending upgrades\n                const pendingUpgradesData = await Promise.all(pendingUpgradeIds.map({\n                    \"useModularToken.useCallback[loadUpgradeInfo]\": async (id)=>{\n                        const upgrade = await contract.pendingUpgrades(id);\n                        return {\n                            upgradeId: id,\n                            moduleId: upgrade.moduleId,\n                            proxy: upgrade.proxy,\n                            newImplementation: upgrade.newImplementation,\n                            executeTime: Number(upgrade.executeTime),\n                            executed: upgrade.executed,\n                            cancelled: upgrade.cancelled,\n                            description: upgrade.description\n                        };\n                    }\n                }[\"useModularToken.useCallback[loadUpgradeInfo]\"]));\n                setPendingUpgrades(pendingUpgradesData);\n                // Load upgrade history for SecurityTokenCore\n                const SECURITY_TOKEN_CORE_ID = ethers__WEBPACK_IMPORTED_MODULE_6__.keccak256(ethers__WEBPACK_IMPORTED_MODULE_7__.toUtf8Bytes(\"SECURITY_TOKEN_CORE\"));\n                const history = await contract.getUpgradeHistory(SECURITY_TOKEN_CORE_ID);\n                setUpgradeHistory(history.map({\n                    \"useModularToken.useCallback[loadUpgradeInfo]\": (record)=>({\n                            oldImplementation: record.oldImplementation,\n                            newImplementation: record.newImplementation,\n                            timestamp: Number(record.timestamp),\n                            executor: record.executor,\n                            version: record.version,\n                            isEmergency: record.isEmergency,\n                            description: record.description\n                        })\n                }[\"useModularToken.useCallback[loadUpgradeInfo]\"]));\n            } catch (error) {\n                console.error('Error loading upgrade info:', error);\n                setError('Failed to load upgrade information');\n            } finally{\n                setLoading(false);\n            }\n        }\n    }[\"useModularToken.useCallback[loadUpgradeInfo]\"], [\n        provider\n    ]);\n    // Mint tokens\n    const mintTokens = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[mintTokens]\": async (address, amount)=>{\n            if (!signer || !SECURITY_TOKEN_CORE_ADDRESS) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(SECURITY_TOKEN_CORE_ADDRESS, SecurityTokenCoreABI, signer);\n            try {\n                const decimals = (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.decimals) || 0;\n                const amountWei = ethers__WEBPACK_IMPORTED_MODULE_5__.parseUnits(amount, decimals);\n                const tx = await contract.mint(address, amountWei, {\n                    gasLimit: 400000\n                });\n                return tx.wait();\n            } catch (error) {\n                console.error('Mint tokens error:', error);\n                if (error.code === 'ACTION_REJECTED') {\n                    throw new Error('Transaction was rejected by user');\n                } else if (error.reason) {\n                    throw new Error(\"Contract error: \".concat(error.reason));\n                } else {\n                    throw new Error(\"Failed to mint tokens: \".concat(error.message));\n                }\n            }\n        }\n    }[\"useModularToken.useCallback[mintTokens]\"], [\n        signer,\n        tokenInfo\n    ]);\n    // Toggle pause state with multiple retry strategies\n    const togglePause = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[togglePause]\": async ()=>{\n            if (!signer || !SECURITY_TOKEN_CORE_ADDRESS) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(SECURITY_TOKEN_CORE_ADDRESS, SecurityTokenCoreABI, signer);\n            const isPausing = !(tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.paused);\n            const functionName = isPausing ? 'pause' : 'unpause';\n            // Try multiple strategies to handle Amoy testnet issues\n            const strategies = [\n                {\n                    \"useModularToken.useCallback[togglePause]\": // Strategy 1: Standard call with high gas\n                    async ()=>{\n                        const tx = isPausing ? await contract.pause({\n                            gasLimit: 500000,\n                            gasPrice: ethers__WEBPACK_IMPORTED_MODULE_5__.parseUnits('30', 'gwei')\n                        }) : await contract.unpause({\n                            gasLimit: 500000,\n                            gasPrice: ethers__WEBPACK_IMPORTED_MODULE_5__.parseUnits('30', 'gwei')\n                        });\n                        return tx.wait();\n                    }\n                }[\"useModularToken.useCallback[togglePause]\"],\n                {\n                    \"useModularToken.useCallback[togglePause]\": // Strategy 2: Raw transaction approach (like the working script)\n                    async ()=>{\n                        const functionSelector = isPausing ? '0x8456cb59' : '0x3f4ba83a';\n                        const nonce = await signer.getNonce();\n                        const tx = await signer.sendTransaction({\n                            to: SECURITY_TOKEN_CORE_ADDRESS,\n                            data: functionSelector,\n                            gasLimit: 500000,\n                            gasPrice: ethers__WEBPACK_IMPORTED_MODULE_5__.parseUnits('30', 'gwei'),\n                            nonce: nonce\n                        });\n                        return tx.wait();\n                    }\n                }[\"useModularToken.useCallback[togglePause]\"]\n            ];\n            for(let i = 0; i < strategies.length; i++){\n                try {\n                    console.log(\"Attempting \".concat(functionName, \" strategy \").concat(i + 1, \"...\"));\n                    const result = await strategies[i]();\n                    console.log(\"\".concat(functionName, \" successful with strategy \").concat(i + 1));\n                    return result;\n                } catch (error) {\n                    console.error(\"Strategy \".concat(i + 1, \" failed:\"), error);\n                    if (error.code === 'ACTION_REJECTED') {\n                        throw new Error('Transaction was rejected by user');\n                    }\n                    // If this is the last strategy, throw a comprehensive error\n                    if (i === strategies.length - 1) {\n                        var _error_error;\n                        if (error.code === 'UNKNOWN_ERROR' && ((_error_error = error.error) === null || _error_error === void 0 ? void 0 : _error_error.code) === -32603) {\n                            throw new Error(\"Amoy testnet RPC error: The network is experiencing issues. Please try again in a few minutes, or use a different RPC endpoint.\");\n                        } else if (error.reason) {\n                            throw new Error(\"Contract error: \".concat(error.reason));\n                        } else {\n                            throw new Error(\"Failed to \".concat(functionName, \" token after trying multiple methods: \").concat(error.message));\n                        }\n                    }\n                    // Wait a bit before trying the next strategy\n                    await new Promise({\n                        \"useModularToken.useCallback[togglePause]\": (resolve)=>setTimeout(resolve, 1000)\n                    }[\"useModularToken.useCallback[togglePause]\"]);\n                }\n            }\n        }\n    }[\"useModularToken.useCallback[togglePause]\"], [\n        signer,\n        tokenInfo,\n        SECURITY_TOKEN_CORE_ADDRESS\n    ]);\n    // Schedule upgrade\n    const scheduleUpgrade = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[scheduleUpgrade]\": async (implementationAddress, description)=>{\n            if (!signer || !UPGRADE_MANAGER_ADDRESS) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(UPGRADE_MANAGER_ADDRESS, UpgradeManagerABI, signer);\n            const SECURITY_TOKEN_CORE_ID = ethers__WEBPACK_IMPORTED_MODULE_6__.keccak256(ethers__WEBPACK_IMPORTED_MODULE_7__.toUtf8Bytes(\"SECURITY_TOKEN_CORE\"));\n            const tx = await contract.scheduleUpgrade(SECURITY_TOKEN_CORE_ID, implementationAddress, description);\n            return tx.wait();\n        }\n    }[\"useModularToken.useCallback[scheduleUpgrade]\"], [\n        signer\n    ]);\n    // Execute upgrade\n    const executeUpgrade = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[executeUpgrade]\": async (upgradeId)=>{\n            if (!signer || !UPGRADE_MANAGER_ADDRESS) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(UPGRADE_MANAGER_ADDRESS, UpgradeManagerABI, signer);\n            const tx = await contract.executeUpgrade(upgradeId);\n            return tx.wait();\n        }\n    }[\"useModularToken.useCallback[executeUpgrade]\"], [\n        signer\n    ]);\n    // Emergency upgrade\n    const emergencyUpgrade = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[emergencyUpgrade]\": async (implementationAddress, description)=>{\n            if (!signer || !UPGRADE_MANAGER_ADDRESS) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(UPGRADE_MANAGER_ADDRESS, UpgradeManagerABI, signer);\n            const SECURITY_TOKEN_CORE_ID = ethers__WEBPACK_IMPORTED_MODULE_6__.keccak256(ethers__WEBPACK_IMPORTED_MODULE_7__.toUtf8Bytes(\"SECURITY_TOKEN_CORE\"));\n            const tx = await contract.emergencyUpgrade(SECURITY_TOKEN_CORE_ID, implementationAddress, description);\n            return tx.wait();\n        }\n    }[\"useModularToken.useCallback[emergencyUpgrade]\"], [\n        signer\n    ]);\n    // Toggle emergency mode\n    const toggleEmergencyMode = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[toggleEmergencyMode]\": async ()=>{\n            if (!signer || !UPGRADE_MANAGER_ADDRESS) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(UPGRADE_MANAGER_ADDRESS, UpgradeManagerABI, signer);\n            const tx = (upgradeInfo === null || upgradeInfo === void 0 ? void 0 : upgradeInfo.emergencyModeActive) ? await contract.deactivateEmergencyMode() : await contract.activateEmergencyMode();\n            return tx.wait();\n        }\n    }[\"useModularToken.useCallback[toggleEmergencyMode]\"], [\n        signer,\n        upgradeInfo\n    ]);\n    // Cancel upgrade\n    const cancelUpgrade = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[cancelUpgrade]\": async (upgradeId)=>{\n            if (!signer || !UPGRADE_MANAGER_ADDRESS) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(UPGRADE_MANAGER_ADDRESS, UpgradeManagerABI, signer);\n            const tx = await contract.cancelUpgrade(upgradeId);\n            return tx.wait();\n        }\n    }[\"useModularToken.useCallback[cancelUpgrade]\"], [\n        signer\n    ]);\n    // Update token price\n    const updateTokenPrice = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[updateTokenPrice]\": async (newPrice)=>{\n            if (!signer || !SECURITY_TOKEN_CORE_ADDRESS) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(SECURITY_TOKEN_CORE_ADDRESS, SecurityTokenCoreABI, signer);\n            try {\n                const tx = await contract.updateTokenPrice(newPrice, {\n                    gasLimit: 300000\n                });\n                return tx.wait();\n            } catch (error) {\n                console.error('Update token price error:', error);\n                if (error.code === 'ACTION_REJECTED') {\n                    throw new Error('Transaction was rejected by user');\n                } else if (error.reason) {\n                    throw new Error(\"Contract error: \".concat(error.reason));\n                } else {\n                    throw new Error(\"Failed to update token price: \".concat(error.message));\n                }\n            }\n        }\n    }[\"useModularToken.useCallback[updateTokenPrice]\"], [\n        signer\n    ]);\n    // Update bonus tiers\n    const updateBonusTiers = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[updateBonusTiers]\": async (newBonusTiers)=>{\n            if (!signer || !SECURITY_TOKEN_CORE_ADDRESS) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(SECURITY_TOKEN_CORE_ADDRESS, SecurityTokenCoreABI, signer);\n            try {\n                const tx = await contract.updateBonusTiers(newBonusTiers, {\n                    gasLimit: 300000\n                });\n                return tx.wait();\n            } catch (error) {\n                console.error('Update bonus tiers error:', error);\n                if (error.code === 'ACTION_REJECTED') {\n                    throw new Error('Transaction was rejected by user');\n                } else if (error.reason) {\n                    throw new Error(\"Contract error: \".concat(error.reason));\n                } else {\n                    throw new Error(\"Failed to update bonus tiers: \".concat(error.message));\n                }\n            }\n        }\n    }[\"useModularToken.useCallback[updateBonusTiers]\"], [\n        signer\n    ]);\n    // Update max supply\n    const updateMaxSupply = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[updateMaxSupply]\": async (newMaxSupply)=>{\n            if (!signer || !SECURITY_TOKEN_CORE_ADDRESS) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(SECURITY_TOKEN_CORE_ADDRESS, SecurityTokenCoreABI, signer);\n            try {\n                const maxSupplyWei = ethers__WEBPACK_IMPORTED_MODULE_5__.parseUnits(newMaxSupply, (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.decimals) || 0);\n                const tx = await contract.updateMaxSupply(maxSupplyWei, {\n                    gasLimit: 300000\n                });\n                return tx.wait();\n            } catch (error) {\n                console.error('Update max supply error:', error);\n                if (error.code === 'ACTION_REJECTED') {\n                    throw new Error('Transaction was rejected by user');\n                } else if (error.reason) {\n                    throw new Error(\"Contract error: \".concat(error.reason));\n                } else {\n                    throw new Error(\"Failed to update max supply: \".concat(error.message));\n                }\n            }\n        }\n    }[\"useModularToken.useCallback[updateMaxSupply]\"], [\n        signer,\n        tokenInfo\n    ]);\n    // Add to whitelist\n    const addToWhitelist = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[addToWhitelist]\": async (address)=>{\n            if (!signer || !SECURITY_TOKEN_CORE_ADDRESS) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(SECURITY_TOKEN_CORE_ADDRESS, SecurityTokenCoreABI, signer);\n            try {\n                const tx = await contract.addToWhitelist(address, {\n                    gasLimit: 300000\n                });\n                return tx.wait();\n            } catch (error) {\n                console.error('Add to whitelist error:', error);\n                if (error.code === 'ACTION_REJECTED') {\n                    throw new Error('Transaction was rejected by user');\n                } else if (error.reason) {\n                    throw new Error(\"Contract error: \".concat(error.reason));\n                } else {\n                    throw new Error(\"Failed to add address to whitelist: \".concat(error.message));\n                }\n            }\n        }\n    }[\"useModularToken.useCallback[addToWhitelist]\"], [\n        signer\n    ]);\n    // Remove from whitelist\n    const removeFromWhitelist = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[removeFromWhitelist]\": async (address)=>{\n            if (!signer || !SECURITY_TOKEN_CORE_ADDRESS) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(SECURITY_TOKEN_CORE_ADDRESS, SecurityTokenCoreABI, signer);\n            try {\n                const tx = await contract.removeFromWhitelist(address, {\n                    gasLimit: 300000\n                });\n                return tx.wait();\n            } catch (error) {\n                console.error('Remove from whitelist error:', error);\n                if (error.code === 'ACTION_REJECTED') {\n                    throw new Error('Transaction was rejected by user');\n                } else if (error.reason) {\n                    throw new Error(\"Contract error: \".concat(error.reason));\n                } else {\n                    throw new Error(\"Failed to remove address from whitelist: \".concat(error.message));\n                }\n            }\n        }\n    }[\"useModularToken.useCallback[removeFromWhitelist]\"], [\n        signer\n    ]);\n    // Refresh all data\n    const refreshData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[refreshData]\": async ()=>{\n            await Promise.all([\n                loadTokenInfo(),\n                loadUpgradeInfo()\n            ]);\n        }\n    }[\"useModularToken.useCallback[refreshData]\"], [\n        loadTokenInfo,\n        loadUpgradeInfo\n    ]);\n    // Initialize on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useModularToken.useEffect\": ()=>{\n            initializeProvider();\n        }\n    }[\"useModularToken.useEffect\"], [\n        initializeProvider\n    ]);\n    // Load data when provider is ready\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useModularToken.useEffect\": ()=>{\n            if (provider && signer) {\n                refreshData();\n            }\n        }\n    }[\"useModularToken.useEffect\"], [\n        provider,\n        signer,\n        refreshData\n    ]);\n    return {\n        // State\n        provider,\n        signer,\n        tokenInfo,\n        upgradeInfo,\n        pendingUpgrades,\n        upgradeHistory,\n        loading,\n        error,\n        // Actions\n        initializeProvider,\n        loadTokenInfo,\n        loadUpgradeInfo,\n        mintTokens,\n        togglePause,\n        scheduleUpgrade,\n        executeUpgrade,\n        emergencyUpgrade,\n        toggleEmergencyMode,\n        cancelUpgrade,\n        refreshData,\n        // Admin Functions\n        updateTokenPrice,\n        updateBonusTiers,\n        updateMaxSupply,\n        addToWhitelist,\n        removeFromWhitelist,\n        // Utilities\n        setError,\n        setLoading\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useModularToken.ts\n"));

/***/ })

});