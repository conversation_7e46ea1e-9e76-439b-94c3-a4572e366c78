import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '../../../../lib/prisma';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { tokenAddress, userAddress, amount, operation, txHash } = body;

    if (!tokenAddress || !userAddress || !amount || !operation || !txHash) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Store the freeze/unfreeze operation
    const freezeOperation = await prisma.freezeOperation.create({
      data: {
        tokenAddress: tokenAddress.toLowerCase(),
        userAddress: userAddress.toLowerCase(),
        amount: parseFloat(amount),
        operation: operation, // 'freeze' or 'unfreeze'
        txHash: txHash,
        timestamp: new Date()
      }
    });

    return NextResponse.json({
      success: true,
      operationId: freezeOperation.id
    });

  } catch (error: any) {
    console.error('Error storing freeze operation:', error);
    return NextResponse.json(
      { 
        error: 'Failed to store freeze operation',
        details: error.message 
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const tokenAddress = searchParams.get('tokenAddress');
    const userAddress = searchParams.get('userAddress');

    if (!tokenAddress) {
      return NextResponse.json(
        { error: 'tokenAddress is required' },
        { status: 400 }
      );
    }

    // Build where clause
    const whereClause: any = {
      tokenAddress: tokenAddress.toLowerCase()
    };

    if (userAddress) {
      whereClause.userAddress = userAddress.toLowerCase();
    }

    // Get all freeze/unfreeze operations
    const operations = await prisma.freezeOperation.findMany({
      where: whereClause,
      orderBy: {
        timestamp: 'desc'
      }
    });

    // Calculate net frozen amount per user
    const userFrozenAmounts: { [key: string]: number } = {};

    operations.forEach(op => {
      if (!userFrozenAmounts[op.userAddress]) {
        userFrozenAmounts[op.userAddress] = 0;
      }

      if (op.operation === 'freeze') {
        userFrozenAmounts[op.userAddress] += op.amount;
      } else if (op.operation === 'unfreeze') {
        userFrozenAmounts[op.userAddress] -= op.amount;
      }
    });

    // Ensure no negative frozen amounts
    Object.keys(userFrozenAmounts).forEach(addr => {
      if (userFrozenAmounts[addr] < 0) {
        userFrozenAmounts[addr] = 0;
      }
    });

    return NextResponse.json({
      success: true,
      operations: operations,
      userFrozenAmounts: userFrozenAmounts
    });

  } catch (error: any) {
    console.error('Error fetching freeze operations:', error);
    return NextResponse.json(
      { 
        error: 'Failed to fetch freeze operations',
        details: error.message 
      },
      { status: 500 }
    );
  }
}
