"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/modular-tokens/[address]/page",{

/***/ "(app-pages-browser)/./src/hooks/useModularToken.ts":
/*!**************************************!*\
  !*** ./src/hooks/useModularToken.ts ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useModularToken: () => (/* binding */ useModularToken)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/providers/provider-browser.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/contract/contract.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/utils/units.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/crypto/keccak.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/utils/utf8.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/constants/addresses.js\");\n/* harmony import */ var _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/contracts/SecurityTokenCore.json */ \"(app-pages-browser)/./src/contracts/SecurityTokenCore.json\");\n/* harmony import */ var _contracts_UpgradeManager_json__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contracts/UpgradeManager.json */ \"(app-pages-browser)/./src/contracts/UpgradeManager.json\");\n/* __next_internal_client_entry_do_not_use__ useModularToken auto */ \n\n// Import ABIs - extract the abi property from the JSON artifacts\n\n\n// Extract ABIs from artifacts\nconst SecurityTokenCoreABI = _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_1__.abi;\nconst UpgradeManagerABI = _contracts_UpgradeManager_json__WEBPACK_IMPORTED_MODULE_2__.abi;\n// Contract addresses from environment\nconst SECURITY_TOKEN_CORE_ADDRESS = \"******************************************\";\nconst UPGRADE_MANAGER_ADDRESS = \"0xb7a53dC340C2E5e823002B174629e2a44B8ed815\";\nfunction useModularToken(tokenAddress) {\n    const [provider, setProvider] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [signer, setSigner] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [tokenInfo, setTokenInfo] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [contractAddresses, setContractAddresses] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [userRoles, setUserRoles] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [upgradeInfo, setUpgradeInfo] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [pendingUpgrades, setPendingUpgrades] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [upgradeHistory, setUpgradeHistory] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    // Initialize provider and signer\n    const initializeProvider = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[initializeProvider]\": async ()=>{\n            try {\n                if ( true && window.ethereum) {\n                    const provider = new ethers__WEBPACK_IMPORTED_MODULE_3__.BrowserProvider(window.ethereum);\n                    await provider.send('eth_requestAccounts', []);\n                    const signer = await provider.getSigner();\n                    setProvider(provider);\n                    setSigner(signer);\n                    return {\n                        provider,\n                        signer\n                    };\n                } else {\n                    throw new Error('MetaMask not found');\n                }\n            } catch (error) {\n                console.error('Error initializing provider:', error);\n                setError('Failed to connect to wallet');\n                return null;\n            }\n        }\n    }[\"useModularToken.useCallback[initializeProvider]\"], []);\n    // Load token information\n    const loadTokenInfo = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[loadTokenInfo]\": async ()=>{\n            const contractAddress = tokenAddress || SECURITY_TOKEN_CORE_ADDRESS;\n            if (!provider || !contractAddress) return;\n            try {\n                setLoading(true);\n                const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(contractAddress, SecurityTokenCoreABI, provider);\n                const [name, symbol, version, totalSupply, maxSupply, decimals, paused, metadata] = await Promise.all([\n                    contract.name(),\n                    contract.symbol(),\n                    contract.version(),\n                    contract.totalSupply(),\n                    contract.maxSupply(),\n                    contract.decimals(),\n                    contract.paused(),\n                    contract.getTokenMetadata()\n                ]);\n                console.log('Token contract data:');\n                console.log('- Name:', name);\n                console.log('- Symbol:', symbol);\n                console.log('- Decimals (raw):', decimals);\n                console.log('- Decimals (number):', Number(decimals));\n                console.log('- Total Supply (raw):', totalSupply.toString());\n                console.log('- Max Supply (raw):', maxSupply.toString());\n                console.log('- Metadata (raw):', metadata);\n                console.log('- Token Price:', metadata[0]);\n                console.log('- Bonus Tiers:', metadata[1]);\n                console.log('- Token Details:', metadata[2]);\n                console.log('- Token Image URL:', metadata[3]);\n                const decimalsNumber = Number(decimals);\n                // Format with proper blockchain decimal precision\n                let formattedTotalSupply;\n                let formattedMaxSupply;\n                if (decimalsNumber === 0) {\n                    // For 0 decimals, show as whole numbers\n                    formattedTotalSupply = totalSupply.toString();\n                    formattedMaxSupply = maxSupply.toString();\n                } else {\n                    // For tokens with decimals, use ethers.formatUnits and preserve decimal precision\n                    const totalSupplyFormatted = ethers__WEBPACK_IMPORTED_MODULE_5__.formatUnits(totalSupply, decimalsNumber);\n                    const maxSupplyFormatted = ethers__WEBPACK_IMPORTED_MODULE_5__.formatUnits(maxSupply, decimalsNumber);\n                    // For blockchain tokens, show with appropriate decimal places\n                    // Remove excessive trailing zeros but keep meaningful precision\n                    formattedTotalSupply = parseFloat(totalSupplyFormatted).toFixed(Math.min(decimalsNumber, 6));\n                    formattedMaxSupply = parseFloat(maxSupplyFormatted).toFixed(Math.min(decimalsNumber, 6));\n                    // Remove trailing zeros after decimal point\n                    formattedTotalSupply = formattedTotalSupply.replace(/\\.?0+$/, '');\n                    formattedMaxSupply = formattedMaxSupply.replace(/\\.?0+$/, '');\n                    // Ensure at least .0 for tokens with decimals\n                    if (!formattedTotalSupply.includes('.')) formattedTotalSupply += '.0';\n                    if (!formattedMaxSupply.includes('.')) formattedMaxSupply += '.0';\n                }\n                console.log('- Total Supply (formatted):', formattedTotalSupply);\n                console.log('- Max Supply (formatted):', formattedMaxSupply);\n                setTokenInfo({\n                    name,\n                    symbol,\n                    version,\n                    totalSupply: formattedTotalSupply,\n                    maxSupply: formattedMaxSupply,\n                    decimals: decimalsNumber,\n                    paused,\n                    metadata: {\n                        tokenPrice: metadata[0],\n                        bonusTiers: metadata[1],\n                        tokenDetails: metadata[2],\n                        tokenImageUrl: metadata[3]\n                    }\n                });\n            } catch (error) {\n                console.error('Error loading token info:', error);\n                setError('Failed to load token information');\n            } finally{\n                setLoading(false);\n            }\n        }\n    }[\"useModularToken.useCallback[loadTokenInfo]\"], [\n        provider,\n        tokenAddress\n    ]);\n    // Load contract addresses\n    const loadContractAddresses = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[loadContractAddresses]\": async ()=>{\n            const contractAddress = tokenAddress || SECURITY_TOKEN_CORE_ADDRESS;\n            if (!provider || !contractAddress) return;\n            try {\n                const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(contractAddress, SecurityTokenCoreABI, provider);\n                // Define module IDs (these are keccak256 hashes of the module names)\n                const moduleIds = {\n                    'Identity Manager': ethers__WEBPACK_IMPORTED_MODULE_6__.keccak256(ethers__WEBPACK_IMPORTED_MODULE_7__.toUtf8Bytes(\"IDENTITY_MANAGER\")),\n                    'Compliance Engine': ethers__WEBPACK_IMPORTED_MODULE_6__.keccak256(ethers__WEBPACK_IMPORTED_MODULE_7__.toUtf8Bytes(\"COMPLIANCE_ENGINE\")),\n                    'Transfer Controller': ethers__WEBPACK_IMPORTED_MODULE_6__.keccak256(ethers__WEBPACK_IMPORTED_MODULE_7__.toUtf8Bytes(\"TRANSFER_CONTROLLER\")),\n                    'Agent Manager': ethers__WEBPACK_IMPORTED_MODULE_6__.keccak256(ethers__WEBPACK_IMPORTED_MODULE_7__.toUtf8Bytes(\"AGENT_MANAGER\")),\n                    'Emergency Manager': ethers__WEBPACK_IMPORTED_MODULE_6__.keccak256(ethers__WEBPACK_IMPORTED_MODULE_7__.toUtf8Bytes(\"EMERGENCY_MANAGER\")),\n                    'KYC Claims Module': ethers__WEBPACK_IMPORTED_MODULE_6__.keccak256(ethers__WEBPACK_IMPORTED_MODULE_7__.toUtf8Bytes(\"KYC_CLAIMS_MODULE\"))\n                };\n                console.log('Fetching module addresses...');\n                // Fetch all module addresses\n                const addresses = {\n                    'Token Contract': contractAddress\n                };\n                for (const [moduleName, moduleId] of Object.entries(moduleIds)){\n                    try {\n                        const moduleAddress = await contract.getModule(moduleId);\n                        if (moduleAddress && moduleAddress !== ethers__WEBPACK_IMPORTED_MODULE_8__.ZeroAddress) {\n                            addresses[moduleName] = moduleAddress;\n                            console.log(\"\".concat(moduleName, \": \").concat(moduleAddress));\n                        } else {\n                            console.log(\"\".concat(moduleName, \": Not registered\"));\n                        }\n                    } catch (error) {\n                        console.warn(\"Error fetching \".concat(moduleName, \" address:\"), error);\n                    }\n                }\n                setContractAddresses(addresses);\n            } catch (error) {\n                console.error('Error loading contract addresses:', error);\n            }\n        }\n    }[\"useModularToken.useCallback[loadContractAddresses]\"], [\n        provider,\n        tokenAddress\n    ]);\n    // Load user roles\n    const loadUserRoles = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[loadUserRoles]\": async ()=>{\n            const contractAddress = tokenAddress || SECURITY_TOKEN_CORE_ADDRESS;\n            if (!provider || !signer || !contractAddress) return;\n            try {\n                const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(contractAddress, SecurityTokenCoreABI, provider);\n                const userAddress = await signer.getAddress();\n                // Define role hashes\n                const DEFAULT_ADMIN_ROLE = '0x0000000000000000000000000000000000000000000000000000000000000000';\n                const AGENT_ROLE = ethers__WEBPACK_IMPORTED_MODULE_6__.keccak256(ethers__WEBPACK_IMPORTED_MODULE_7__.toUtf8Bytes(\"AGENT_ROLE\"));\n                const TRANSFER_MANAGER_ROLE = ethers__WEBPACK_IMPORTED_MODULE_6__.keccak256(ethers__WEBPACK_IMPORTED_MODULE_7__.toUtf8Bytes(\"TRANSFER_MANAGER_ROLE\"));\n                const MODULE_MANAGER_ROLE = ethers__WEBPACK_IMPORTED_MODULE_6__.keccak256(ethers__WEBPACK_IMPORTED_MODULE_7__.toUtf8Bytes(\"MODULE_MANAGER_ROLE\"));\n                console.log('Checking roles for user:', userAddress);\n                // Check all roles\n                const roles = {};\n                try {\n                    roles['DEFAULT_ADMIN_ROLE'] = await contract.hasRole(DEFAULT_ADMIN_ROLE, userAddress);\n                    console.log('DEFAULT_ADMIN_ROLE:', roles['DEFAULT_ADMIN_ROLE']);\n                } catch (error) {\n                    console.warn('Error checking DEFAULT_ADMIN_ROLE:', error);\n                    roles['DEFAULT_ADMIN_ROLE'] = false;\n                }\n                try {\n                    roles['AGENT_ROLE'] = await contract.hasRole(AGENT_ROLE, userAddress);\n                    console.log('AGENT_ROLE:', roles['AGENT_ROLE']);\n                } catch (error) {\n                    console.warn('Error checking AGENT_ROLE:', error);\n                    roles['AGENT_ROLE'] = false;\n                }\n                try {\n                    roles['TRANSFER_MANAGER_ROLE'] = await contract.hasRole(TRANSFER_MANAGER_ROLE, userAddress);\n                    console.log('TRANSFER_MANAGER_ROLE:', roles['TRANSFER_MANAGER_ROLE']);\n                } catch (error) {\n                    console.warn('Error checking TRANSFER_MANAGER_ROLE:', error);\n                    roles['TRANSFER_MANAGER_ROLE'] = false;\n                }\n                try {\n                    roles['MODULE_MANAGER_ROLE'] = await contract.hasRole(MODULE_MANAGER_ROLE, userAddress);\n                    console.log('MODULE_MANAGER_ROLE:', roles['MODULE_MANAGER_ROLE']);\n                } catch (error) {\n                    console.warn('Error checking MODULE_MANAGER_ROLE:', error);\n                    roles['MODULE_MANAGER_ROLE'] = false;\n                }\n                setUserRoles(roles);\n            } catch (error) {\n                console.error('Error loading user roles:', error);\n            }\n        }\n    }[\"useModularToken.useCallback[loadUserRoles]\"], [\n        provider,\n        signer,\n        tokenAddress\n    ]);\n    // Load upgrade information\n    const loadUpgradeInfo = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[loadUpgradeInfo]\": async ()=>{\n            if (!provider || !UPGRADE_MANAGER_ADDRESS) return;\n            try {\n                setLoading(true);\n                const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(UPGRADE_MANAGER_ADDRESS, UpgradeManagerABI, provider);\n                const [emergencyModeActive, registeredModules, upgradeDelay, emergencyModeDuration, pendingUpgradeIds] = await Promise.all([\n                    contract.isEmergencyModeActive(),\n                    contract.getRegisteredModules(),\n                    contract.UPGRADE_DELAY(),\n                    contract.EMERGENCY_MODE_DURATION(),\n                    contract.getPendingUpgradeIds()\n                ]);\n                setUpgradeInfo({\n                    emergencyModeActive,\n                    registeredModules,\n                    upgradeDelay: Number(upgradeDelay),\n                    emergencyModeDuration: Number(emergencyModeDuration)\n                });\n                // Load pending upgrades\n                const pendingUpgradesData = await Promise.all(pendingUpgradeIds.map({\n                    \"useModularToken.useCallback[loadUpgradeInfo]\": async (id)=>{\n                        const upgrade = await contract.pendingUpgrades(id);\n                        return {\n                            upgradeId: id,\n                            moduleId: upgrade.moduleId,\n                            proxy: upgrade.proxy,\n                            newImplementation: upgrade.newImplementation,\n                            executeTime: Number(upgrade.executeTime),\n                            executed: upgrade.executed,\n                            cancelled: upgrade.cancelled,\n                            description: upgrade.description\n                        };\n                    }\n                }[\"useModularToken.useCallback[loadUpgradeInfo]\"]));\n                setPendingUpgrades(pendingUpgradesData);\n                // Load upgrade history for SecurityTokenCore\n                const SECURITY_TOKEN_CORE_ID = ethers__WEBPACK_IMPORTED_MODULE_6__.keccak256(ethers__WEBPACK_IMPORTED_MODULE_7__.toUtf8Bytes(\"SECURITY_TOKEN_CORE\"));\n                const history = await contract.getUpgradeHistory(SECURITY_TOKEN_CORE_ID);\n                setUpgradeHistory(history.map({\n                    \"useModularToken.useCallback[loadUpgradeInfo]\": (record)=>({\n                            oldImplementation: record.oldImplementation,\n                            newImplementation: record.newImplementation,\n                            timestamp: Number(record.timestamp),\n                            executor: record.executor,\n                            version: record.version,\n                            isEmergency: record.isEmergency,\n                            description: record.description\n                        })\n                }[\"useModularToken.useCallback[loadUpgradeInfo]\"]));\n            } catch (error) {\n                console.error('Error loading upgrade info:', error);\n                setError('Failed to load upgrade information');\n            } finally{\n                setLoading(false);\n            }\n        }\n    }[\"useModularToken.useCallback[loadUpgradeInfo]\"], [\n        provider\n    ]);\n    // Mint tokens\n    const mintTokens = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[mintTokens]\": async (address, amount)=>{\n            const contractAddress = tokenAddress || SECURITY_TOKEN_CORE_ADDRESS;\n            if (!signer || !contractAddress) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(contractAddress, SecurityTokenCoreABI, signer);\n            try {\n                const decimals = (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.decimals) || 0;\n                const amountWei = ethers__WEBPACK_IMPORTED_MODULE_5__.parseUnits(amount, decimals);\n                const tx = await contract.mint(address, amountWei, {\n                    gasLimit: 400000\n                });\n                return tx.wait();\n            } catch (error) {\n                console.error('Mint tokens error:', error);\n                if (error.code === 'ACTION_REJECTED') {\n                    throw new Error('Transaction was rejected by user');\n                } else if (error.reason) {\n                    throw new Error(\"Contract error: \".concat(error.reason));\n                } else {\n                    throw new Error(\"Failed to mint tokens: \".concat(error.message));\n                }\n            }\n        }\n    }[\"useModularToken.useCallback[mintTokens]\"], [\n        signer,\n        tokenInfo,\n        tokenAddress\n    ]);\n    // Toggle pause state with multiple retry strategies\n    const togglePause = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[togglePause]\": async ()=>{\n            const contractAddress = tokenAddress || SECURITY_TOKEN_CORE_ADDRESS;\n            if (!signer || !contractAddress) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(contractAddress, SecurityTokenCoreABI, signer);\n            const isPausing = !(tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.paused);\n            const functionName = isPausing ? 'pause' : 'unpause';\n            // Try multiple strategies to handle Amoy testnet issues\n            const strategies = [\n                {\n                    \"useModularToken.useCallback[togglePause]\": // Strategy 1: Standard call with high gas\n                    async ()=>{\n                        const tx = isPausing ? await contract.pause({\n                            gasLimit: 500000,\n                            gasPrice: ethers__WEBPACK_IMPORTED_MODULE_5__.parseUnits('30', 'gwei')\n                        }) : await contract.unpause({\n                            gasLimit: 500000,\n                            gasPrice: ethers__WEBPACK_IMPORTED_MODULE_5__.parseUnits('30', 'gwei')\n                        });\n                        return tx.wait();\n                    }\n                }[\"useModularToken.useCallback[togglePause]\"],\n                {\n                    \"useModularToken.useCallback[togglePause]\": // Strategy 2: Raw transaction approach (like the working script)\n                    async ()=>{\n                        const functionSelector = isPausing ? '0x8456cb59' : '0x3f4ba83a';\n                        const nonce = await signer.getNonce();\n                        const tx = await signer.sendTransaction({\n                            to: contractAddress,\n                            data: functionSelector,\n                            gasLimit: 500000,\n                            gasPrice: ethers__WEBPACK_IMPORTED_MODULE_5__.parseUnits('30', 'gwei'),\n                            nonce: nonce\n                        });\n                        return tx.wait();\n                    }\n                }[\"useModularToken.useCallback[togglePause]\"]\n            ];\n            for(let i = 0; i < strategies.length; i++){\n                try {\n                    console.log(\"Attempting \".concat(functionName, \" strategy \").concat(i + 1, \"...\"));\n                    const result = await strategies[i]();\n                    console.log(\"\".concat(functionName, \" successful with strategy \").concat(i + 1));\n                    return result;\n                } catch (error) {\n                    console.error(\"Strategy \".concat(i + 1, \" failed:\"), error);\n                    if (error.code === 'ACTION_REJECTED') {\n                        throw new Error('Transaction was rejected by user');\n                    }\n                    // If this is the last strategy, throw a comprehensive error\n                    if (i === strategies.length - 1) {\n                        var _error_error;\n                        if (error.code === 'UNKNOWN_ERROR' && ((_error_error = error.error) === null || _error_error === void 0 ? void 0 : _error_error.code) === -32603) {\n                            throw new Error(\"Amoy testnet RPC error: The network is experiencing issues. Please try again in a few minutes, or use a different RPC endpoint.\");\n                        } else if (error.reason) {\n                            throw new Error(\"Contract error: \".concat(error.reason));\n                        } else {\n                            throw new Error(\"Failed to \".concat(functionName, \" token after trying multiple methods: \").concat(error.message));\n                        }\n                    }\n                    // Wait a bit before trying the next strategy\n                    await new Promise({\n                        \"useModularToken.useCallback[togglePause]\": (resolve)=>setTimeout(resolve, 1000)\n                    }[\"useModularToken.useCallback[togglePause]\"]);\n                }\n            }\n        }\n    }[\"useModularToken.useCallback[togglePause]\"], [\n        signer,\n        tokenInfo,\n        tokenAddress\n    ]);\n    // Schedule upgrade\n    const scheduleUpgrade = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[scheduleUpgrade]\": async (implementationAddress, description)=>{\n            if (!signer || !UPGRADE_MANAGER_ADDRESS) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(UPGRADE_MANAGER_ADDRESS, UpgradeManagerABI, signer);\n            const SECURITY_TOKEN_CORE_ID = ethers__WEBPACK_IMPORTED_MODULE_6__.keccak256(ethers__WEBPACK_IMPORTED_MODULE_7__.toUtf8Bytes(\"SECURITY_TOKEN_CORE\"));\n            const tx = await contract.scheduleUpgrade(SECURITY_TOKEN_CORE_ID, implementationAddress, description);\n            return tx.wait();\n        }\n    }[\"useModularToken.useCallback[scheduleUpgrade]\"], [\n        signer\n    ]);\n    // Execute upgrade\n    const executeUpgrade = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[executeUpgrade]\": async (upgradeId)=>{\n            if (!signer || !UPGRADE_MANAGER_ADDRESS) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(UPGRADE_MANAGER_ADDRESS, UpgradeManagerABI, signer);\n            const tx = await contract.executeUpgrade(upgradeId);\n            return tx.wait();\n        }\n    }[\"useModularToken.useCallback[executeUpgrade]\"], [\n        signer\n    ]);\n    // Emergency upgrade\n    const emergencyUpgrade = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[emergencyUpgrade]\": async (implementationAddress, description)=>{\n            if (!signer || !UPGRADE_MANAGER_ADDRESS) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(UPGRADE_MANAGER_ADDRESS, UpgradeManagerABI, signer);\n            const SECURITY_TOKEN_CORE_ID = ethers__WEBPACK_IMPORTED_MODULE_6__.keccak256(ethers__WEBPACK_IMPORTED_MODULE_7__.toUtf8Bytes(\"SECURITY_TOKEN_CORE\"));\n            const tx = await contract.emergencyUpgrade(SECURITY_TOKEN_CORE_ID, implementationAddress, description);\n            return tx.wait();\n        }\n    }[\"useModularToken.useCallback[emergencyUpgrade]\"], [\n        signer\n    ]);\n    // Toggle emergency mode\n    const toggleEmergencyMode = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[toggleEmergencyMode]\": async ()=>{\n            if (!signer || !UPGRADE_MANAGER_ADDRESS) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(UPGRADE_MANAGER_ADDRESS, UpgradeManagerABI, signer);\n            const tx = (upgradeInfo === null || upgradeInfo === void 0 ? void 0 : upgradeInfo.emergencyModeActive) ? await contract.deactivateEmergencyMode() : await contract.activateEmergencyMode();\n            return tx.wait();\n        }\n    }[\"useModularToken.useCallback[toggleEmergencyMode]\"], [\n        signer,\n        upgradeInfo\n    ]);\n    // Cancel upgrade\n    const cancelUpgrade = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[cancelUpgrade]\": async (upgradeId)=>{\n            if (!signer || !UPGRADE_MANAGER_ADDRESS) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(UPGRADE_MANAGER_ADDRESS, UpgradeManagerABI, signer);\n            const tx = await contract.cancelUpgrade(upgradeId);\n            return tx.wait();\n        }\n    }[\"useModularToken.useCallback[cancelUpgrade]\"], [\n        signer\n    ]);\n    // Update token price\n    const updateTokenPrice = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[updateTokenPrice]\": async (newPrice)=>{\n            const contractAddress = tokenAddress || SECURITY_TOKEN_CORE_ADDRESS;\n            if (!signer || !contractAddress) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(contractAddress, SecurityTokenCoreABI, signer);\n            try {\n                const tx = await contract.updateTokenPrice(newPrice, {\n                    gasLimit: 300000\n                });\n                return tx.wait();\n            } catch (error) {\n                console.error('Update token price error:', error);\n                if (error.code === 'ACTION_REJECTED') {\n                    throw new Error('Transaction was rejected by user');\n                } else if (error.reason) {\n                    throw new Error(\"Contract error: \".concat(error.reason));\n                } else {\n                    throw new Error(\"Failed to update token price: \".concat(error.message));\n                }\n            }\n        }\n    }[\"useModularToken.useCallback[updateTokenPrice]\"], [\n        signer,\n        tokenAddress\n    ]);\n    // Update bonus tiers\n    const updateBonusTiers = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[updateBonusTiers]\": async (newBonusTiers)=>{\n            const contractAddress = tokenAddress || SECURITY_TOKEN_CORE_ADDRESS;\n            if (!signer || !contractAddress) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(contractAddress, SecurityTokenCoreABI, signer);\n            try {\n                const tx = await contract.updateBonusTiers(newBonusTiers, {\n                    gasLimit: 300000\n                });\n                return tx.wait();\n            } catch (error) {\n                console.error('Update bonus tiers error:', error);\n                if (error.code === 'ACTION_REJECTED') {\n                    throw new Error('Transaction was rejected by user');\n                } else if (error.reason) {\n                    throw new Error(\"Contract error: \".concat(error.reason));\n                } else {\n                    throw new Error(\"Failed to update bonus tiers: \".concat(error.message));\n                }\n            }\n        }\n    }[\"useModularToken.useCallback[updateBonusTiers]\"], [\n        signer,\n        tokenAddress\n    ]);\n    // Update max supply\n    const updateMaxSupply = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[updateMaxSupply]\": async (newMaxSupply)=>{\n            const contractAddress = tokenAddress || SECURITY_TOKEN_CORE_ADDRESS;\n            if (!signer || !contractAddress) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(contractAddress, SecurityTokenCoreABI, signer);\n            try {\n                const maxSupplyWei = ethers__WEBPACK_IMPORTED_MODULE_5__.parseUnits(newMaxSupply, (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.decimals) || 0);\n                const tx = await contract.updateMaxSupply(maxSupplyWei, {\n                    gasLimit: 300000\n                });\n                return tx.wait();\n            } catch (error) {\n                console.error('Update max supply error:', error);\n                if (error.code === 'ACTION_REJECTED') {\n                    throw new Error('Transaction was rejected by user');\n                } else if (error.reason) {\n                    throw new Error(\"Contract error: \".concat(error.reason));\n                } else {\n                    throw new Error(\"Failed to update max supply: \".concat(error.message));\n                }\n            }\n        }\n    }[\"useModularToken.useCallback[updateMaxSupply]\"], [\n        signer,\n        tokenInfo,\n        tokenAddress\n    ]);\n    // Add to whitelist with identity registration\n    const addToWhitelist = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[addToWhitelist]\": async (address)=>{\n            const contractAddress = tokenAddress || SECURITY_TOKEN_CORE_ADDRESS;\n            if (!signer || !contractAddress) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(contractAddress, SecurityTokenCoreABI, signer);\n            try {\n                // First check if the address is already verified/registered\n                const isVerified = await contract.isVerified(address);\n                if (!isVerified) {\n                    // Need to register identity first - use the API approach for this\n                    throw new Error('Address must be registered first. Please use the API method or register the identity manually.');\n                }\n                // Now try to add to whitelist\n                const tx = await contract.addToWhitelist(address, {\n                    gasLimit: 400000,\n                    gasPrice: ethers__WEBPACK_IMPORTED_MODULE_5__.parseUnits('30', 'gwei')\n                });\n                return tx.wait();\n            } catch (error) {\n                console.error('Add to whitelist error:', error);\n                if (error.code === 'ACTION_REJECTED') {\n                    throw new Error('Transaction was rejected by user');\n                } else if (error.reason) {\n                    throw new Error(\"Contract error: \".concat(error.reason));\n                } else if (error.message.includes('identity not registered')) {\n                    throw new Error('Address must be registered first. Please use the API method which handles registration automatically.');\n                } else {\n                    throw new Error(\"Failed to add address to whitelist: \".concat(error.message));\n                }\n            }\n        }\n    }[\"useModularToken.useCallback[addToWhitelist]\"], [\n        signer,\n        tokenAddress\n    ]);\n    // Remove from whitelist\n    const removeFromWhitelist = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[removeFromWhitelist]\": async (address)=>{\n            const contractAddress = tokenAddress || SECURITY_TOKEN_CORE_ADDRESS;\n            if (!signer || !contractAddress) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(contractAddress, SecurityTokenCoreABI, signer);\n            try {\n                const tx = await contract.removeFromWhitelist(address, {\n                    gasLimit: 300000\n                });\n                return tx.wait();\n            } catch (error) {\n                console.error('Remove from whitelist error:', error);\n                if (error.code === 'ACTION_REJECTED') {\n                    throw new Error('Transaction was rejected by user');\n                } else if (error.reason) {\n                    throw new Error(\"Contract error: \".concat(error.reason));\n                } else {\n                    throw new Error(\"Failed to remove address from whitelist: \".concat(error.message));\n                }\n            }\n        }\n    }[\"useModularToken.useCallback[removeFromWhitelist]\"], [\n        signer,\n        tokenAddress\n    ]);\n    // Refresh all data\n    const refreshData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[refreshData]\": async ()=>{\n            await Promise.all([\n                loadTokenInfo(),\n                loadContractAddresses(),\n                loadUpgradeInfo()\n            ]);\n        }\n    }[\"useModularToken.useCallback[refreshData]\"], [\n        loadTokenInfo,\n        loadContractAddresses,\n        loadUpgradeInfo\n    ]);\n    // Initialize on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useModularToken.useEffect\": ()=>{\n            initializeProvider();\n        }\n    }[\"useModularToken.useEffect\"], [\n        initializeProvider\n    ]);\n    // Load data when provider is ready\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useModularToken.useEffect\": ()=>{\n            if (provider && signer) {\n                refreshData();\n            }\n        }\n    }[\"useModularToken.useEffect\"], [\n        provider,\n        signer,\n        refreshData\n    ]);\n    return {\n        // State\n        provider,\n        signer,\n        tokenInfo,\n        contractAddresses,\n        upgradeInfo,\n        pendingUpgrades,\n        upgradeHistory,\n        loading,\n        error,\n        // Actions\n        initializeProvider,\n        loadTokenInfo,\n        loadContractAddresses,\n        loadUpgradeInfo,\n        mintTokens,\n        togglePause,\n        scheduleUpgrade,\n        executeUpgrade,\n        emergencyUpgrade,\n        toggleEmergencyMode,\n        cancelUpgrade,\n        refreshData,\n        // Admin Functions\n        updateTokenPrice,\n        updateBonusTiers,\n        updateMaxSupply,\n        addToWhitelist,\n        removeFromWhitelist,\n        // Utilities\n        setError,\n        setLoading\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useModularToken.ts\n"));

/***/ })

});