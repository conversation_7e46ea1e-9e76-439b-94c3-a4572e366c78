"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/modular-tokens/[address]/page",{

/***/ "(app-pages-browser)/./src/app/modular-tokens/[address]/page.tsx":
/*!***************************************************!*\
  !*** ./src/app/modular-tokens/[address]/page.tsx ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ModularTokenDetailsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _hooks_useModularToken__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../hooks/useModularToken */ \"(app-pages-browser)/./src/hooks/useModularToken.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n// Import custom hook\n\nfunction ModularTokenDetailsPage() {\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const tokenAddress = params.address;\n    // Use the custom hook for modular token functionality\n    const { provider, signer, tokenInfo, upgradeInfo, pendingUpgrades, upgradeHistory, loading, error, initializeProvider, mintTokens, togglePause, scheduleUpgrade, toggleEmergencyMode, refreshData, setError, updateTokenPrice, updateBonusTiers, updateMaxSupply, addToWhitelist, removeFromWhitelist } = (0,_hooks_useModularToken__WEBPACK_IMPORTED_MODULE_3__.useModularToken)(tokenAddress); // Pass the token address to the hook\n    // Local state for UI\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [mintAmount, setMintAmount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [mintAddress, setMintAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [upgradeDescription, setUpgradeDescription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [newImplementationAddress, setNewImplementationAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [actionLoading, setActionLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('overview');\n    // KYC & Claims state\n    const [kycUserAddress, setKycUserAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [claimUserAddress, setClaimUserAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [claimTopicId, setClaimTopicId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('10101010000001');\n    const [claimData, setClaimData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [checkUserAddress, setCheckUserAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [verificationStatus, setVerificationStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Admin Controls form states\n    const [newTokenPrice, setNewTokenPrice] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [newBonusTiers, setNewBonusTiers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [newMaxSupply, setNewMaxSupply] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [whitelistAddress, setWhitelistAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Clear success message after 5 seconds\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect({\n        \"ModularTokenDetailsPage.useEffect\": ()=>{\n            if (success) {\n                const timer = setTimeout({\n                    \"ModularTokenDetailsPage.useEffect.timer\": ()=>setSuccess(null)\n                }[\"ModularTokenDetailsPage.useEffect.timer\"], 5000);\n                return ({\n                    \"ModularTokenDetailsPage.useEffect\": ()=>clearTimeout(timer)\n                })[\"ModularTokenDetailsPage.useEffect\"];\n            }\n        }\n    }[\"ModularTokenDetailsPage.useEffect\"], [\n        success\n    ]);\n    // Clear error message after 10 seconds\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect({\n        \"ModularTokenDetailsPage.useEffect\": ()=>{\n            if (error) {\n                const timer = setTimeout({\n                    \"ModularTokenDetailsPage.useEffect.timer\": ()=>setError(null)\n                }[\"ModularTokenDetailsPage.useEffect.timer\"], 10000);\n                return ({\n                    \"ModularTokenDetailsPage.useEffect\": ()=>clearTimeout(timer)\n                })[\"ModularTokenDetailsPage.useEffect\"];\n            }\n        }\n    }[\"ModularTokenDetailsPage.useEffect\"], [\n        error,\n        setError\n    ]);\n    const handleMint = async ()=>{\n        if (!mintAddress || !mintAmount) return;\n        setActionLoading(true);\n        setError(null);\n        setSuccess(null);\n        try {\n            await mintTokens(mintAddress, mintAmount);\n            setSuccess(\"Successfully minted \".concat(mintAmount, \" tokens to \").concat(mintAddress));\n            setMintAmount('');\n            setMintAddress('');\n            await refreshData(); // Refresh token info\n        } catch (error) {\n            console.error('Error minting tokens:', error);\n            setError(\"Failed to mint tokens: \".concat(error.message));\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handleMintAPI = async ()=>{\n        if (!mintAddress || !mintAmount) return;\n        setActionLoading(true);\n        setError(null);\n        setSuccess(null);\n        try {\n            const response = await fetch('/api/admin/mint-tokens', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    tokenAddress: tokenAddress,\n                    toAddress: mintAddress,\n                    amount: mintAmount\n                })\n            });\n            const result = await response.json();\n            if (!response.ok) {\n                throw new Error(result.error || 'API request failed');\n            }\n            setSuccess(\"API Mint: \".concat(result.message, \" (Tx: \").concat(result.txHash, \")\"));\n            setMintAddress('');\n            setMintAmount('');\n            await refreshData();\n        } catch (error) {\n            console.error('Error with API mint:', error);\n            setError(\"API mint failed: \".concat(error.message));\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handlePauseToggle = async ()=>{\n        setActionLoading(true);\n        setError(null);\n        setSuccess(null);\n        try {\n            await togglePause();\n            setSuccess(\"Token \".concat((tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.paused) ? 'unpaused' : 'paused', \" successfully\"));\n            await refreshData(); // Refresh token info\n        } catch (error) {\n            console.error('Error toggling pause:', error);\n            setError(\"Failed to \".concat((tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.paused) ? 'unpause' : 'pause', \" token: \").concat(error.message));\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handlePauseToggleAPI = async ()=>{\n        setActionLoading(true);\n        setError(null);\n        setSuccess(null);\n        try {\n            const action = (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.paused) ? 'unpause' : 'pause';\n            const response = await fetch('/api/admin/toggle-pause', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    tokenAddress: tokenAddress,\n                    action: action\n                })\n            });\n            const result = await response.json();\n            if (!response.ok) {\n                throw new Error(result.error || 'API request failed');\n            }\n            if (result.alreadyInDesiredState) {\n                setSuccess(result.message);\n            } else {\n                setSuccess(\"\".concat(result.message, \" (Tx: \").concat(result.transactionHash, \")\"));\n            }\n            await refreshData(); // Refresh token info\n        } catch (error) {\n            console.error('Error with API pause toggle:', error);\n            setError(\"API \".concat((tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.paused) ? 'unpause' : 'pause', \" failed: \").concat(error.message));\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handleScheduleUpgrade = async ()=>{\n        if (!newImplementationAddress || !upgradeDescription) return;\n        setActionLoading(true);\n        setError(null);\n        setSuccess(null);\n        try {\n            await scheduleUpgrade(newImplementationAddress, upgradeDescription);\n            setSuccess('Upgrade scheduled successfully! It will be executable after the 48-hour timelock.');\n            setNewImplementationAddress('');\n            setUpgradeDescription('');\n            await refreshData(); // Refresh upgrade info\n        } catch (error) {\n            console.error('Error scheduling upgrade:', error);\n            setError(\"Failed to schedule upgrade: \".concat(error.message));\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handleEmergencyModeToggle = async ()=>{\n        setActionLoading(true);\n        setError(null);\n        setSuccess(null);\n        try {\n            await toggleEmergencyMode();\n            setSuccess(\"Emergency mode \".concat((upgradeInfo === null || upgradeInfo === void 0 ? void 0 : upgradeInfo.emergencyModeActive) ? 'deactivated' : 'activated', \" successfully\"));\n            await refreshData(); // Refresh upgrade info\n        } catch (error) {\n            console.error('Error toggling emergency mode:', error);\n            setError(\"Failed to \".concat((upgradeInfo === null || upgradeInfo === void 0 ? void 0 : upgradeInfo.emergencyModeActive) ? 'deactivate' : 'activate', \" emergency mode: \").concat(error.message));\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    // Admin Controls Handlers\n    const handleUpdatePrice = async ()=>{\n        if (!newTokenPrice) return;\n        setActionLoading(true);\n        try {\n            await updateTokenPrice(newTokenPrice);\n            setSuccess(\"Token price updated to \".concat(newTokenPrice));\n            setNewTokenPrice('');\n            await refreshData();\n        } catch (err) {\n            setError(err.message);\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handleUpdateBonusTiers = async ()=>{\n        if (!newBonusTiers) return;\n        setActionLoading(true);\n        try {\n            await updateBonusTiers(newBonusTiers);\n            setSuccess(\"Bonus tiers updated to: \".concat(newBonusTiers));\n            setNewBonusTiers('');\n            await refreshData();\n        } catch (err) {\n            setError(err.message);\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handleUpdateMaxSupply = async ()=>{\n        if (!newMaxSupply) return;\n        setActionLoading(true);\n        try {\n            await updateMaxSupply(newMaxSupply);\n            setSuccess(\"Max supply updated to \".concat(newMaxSupply));\n            setNewMaxSupply('');\n            await refreshData();\n        } catch (err) {\n            setError(err.message);\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handleAddToWhitelistAdmin = async ()=>{\n        if (!whitelistAddress) return;\n        setActionLoading(true);\n        try {\n            await addToWhitelist(whitelistAddress);\n            setSuccess(\"Address \".concat(whitelistAddress, \" added to whitelist\"));\n            setWhitelistAddress('');\n        } catch (err) {\n            setError(err.message);\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handleRemoveFromWhitelist = async ()=>{\n        if (!whitelistAddress) return;\n        setActionLoading(true);\n        try {\n            await removeFromWhitelist(whitelistAddress);\n            setSuccess(\"Address \".concat(whitelistAddress, \" removed from whitelist\"));\n            setWhitelistAddress('');\n        } catch (err) {\n            setError(err.message);\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    // KYC & Claims handlers\n    const handleApproveKYC = async ()=>{\n        if (!kycUserAddress) return;\n        setActionLoading(true);\n        setError(null);\n        setSuccess(null);\n        try {\n            // Call the KYC approval API\n            const response = await fetch('/api/kyc/approve', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    tokenAddress: tokenAddress,\n                    userAddress: kycUserAddress\n                })\n            });\n            if (!response.ok) {\n                throw new Error('Failed to approve KYC');\n            }\n            setSuccess(\"KYC approved successfully for \".concat(kycUserAddress));\n            setKycUserAddress('');\n        } catch (error) {\n            console.error('Error approving KYC:', error);\n            setError(\"Failed to approve KYC: \".concat(error.message));\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handleAddToWhitelistKYC = async ()=>{\n        if (!kycUserAddress) return;\n        setActionLoading(true);\n        setError(null);\n        setSuccess(null);\n        try {\n            // Call the whitelist API\n            const response = await fetch('/api/kyc/whitelist', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    tokenAddress: tokenAddress,\n                    userAddress: kycUserAddress\n                })\n            });\n            if (!response.ok) {\n                throw new Error('Failed to add to whitelist');\n            }\n            setSuccess(\"User added to whitelist successfully: \".concat(kycUserAddress));\n            setKycUserAddress('');\n        } catch (error) {\n            console.error('Error adding to whitelist:', error);\n            setError(\"Failed to add to whitelist: \".concat(error.message));\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handleIssueClaim = async ()=>{\n        if (!claimUserAddress || !claimTopicId) return;\n        setActionLoading(true);\n        setError(null);\n        setSuccess(null);\n        try {\n            // Call the claims API\n            const response = await fetch('/api/kyc/issue-claim', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    userAddress: claimUserAddress,\n                    topicId: claimTopicId,\n                    data: claimData || ''\n                })\n            });\n            if (!response.ok) {\n                throw new Error('Failed to issue claim');\n            }\n            const result = await response.json();\n            setSuccess(\"Claim issued successfully! Claim ID: \".concat(result.claimId));\n            setClaimUserAddress('');\n            setClaimData('');\n        } catch (error) {\n            console.error('Error issuing claim:', error);\n            setError(\"Failed to issue claim: \".concat(error.message));\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handleCheckStatus = async ()=>{\n        if (!checkUserAddress) return;\n        setActionLoading(true);\n        setError(null);\n        try {\n            // Call the status check API\n            const response = await fetch(\"/api/kyc/status?tokenAddress=\".concat(tokenAddress, \"&userAddress=\").concat(checkUserAddress));\n            if (!response.ok) {\n                throw new Error('Failed to check status');\n            }\n            const status = await response.json();\n            setVerificationStatus(status);\n        } catch (error) {\n            console.error('Error checking status:', error);\n            setError(\"Failed to check status: \".concat(error.message));\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    // Utility functions\n    const formatAddress = (address)=>{\n        return \"\".concat(address.slice(0, 6), \"...\").concat(address.slice(-4));\n    };\n    const formatTimestamp = (timestamp)=>{\n        return new Date(timestamp * 1000).toLocaleString();\n    };\n    if (!provider || !signer) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-6 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold text-yellow-800 mb-4\",\n                        children: \"\\uD83D\\uDD17 Wallet Connection Required\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                        lineNumber: 452,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-yellow-700 mb-4\",\n                        children: \"Please connect your MetaMask wallet to manage this modular token.\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                        lineNumber: 455,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: initializeProvider,\n                        className: \"bg-yellow-600 hover:bg-yellow-700 text-white font-bold py-2 px-4 rounded\",\n                        children: \"Connect Wallet\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                        lineNumber: 458,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                lineNumber: 451,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n            lineNumber: 450,\n            columnNumber: 7\n        }, this);\n    }\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-center items-center h-64\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                    lineNumber: 473,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                lineNumber: 472,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n            lineNumber: 471,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto px-4 py-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-gray-900 mb-2\",\n                                children: \"Modular Token Management\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 485,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: [\n                                    \"Token Address: \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-mono text-sm\",\n                                        children: tokenAddress\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 489,\n                                        columnNumber: 30\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 488,\n                                columnNumber: 13\n                            }, this),\n                            tokenInfo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: [\n                                    tokenInfo.name,\n                                    \" (\",\n                                    tokenInfo.symbol,\n                                    \") - Version \",\n                                    tokenInfo.version\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 492,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                        lineNumber: 484,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                    lineNumber: 483,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                lineNumber: 482,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-50 border border-red-200 rounded-lg p-4 mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-shrink-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"h-5 w-5 text-red-400\",\n                                viewBox: \"0 0 20 20\",\n                                fill: \"currentColor\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    fillRule: \"evenodd\",\n                                    d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\",\n                                    clipRule: \"evenodd\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                    lineNumber: 506,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 505,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                            lineNumber: 504,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"ml-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-sm font-medium text-red-800\",\n                                    children: \"Error\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                    lineNumber: 510,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-red-700 mt-1\",\n                                    children: error\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                    lineNumber: 511,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                            lineNumber: 509,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                    lineNumber: 503,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                lineNumber: 502,\n                columnNumber: 9\n            }, this),\n            success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-green-50 border border-green-200 rounded-lg p-4 mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-shrink-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"h-5 w-5 text-green-400\",\n                                viewBox: \"0 0 20 20\",\n                                fill: \"currentColor\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    fillRule: \"evenodd\",\n                                    d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",\n                                    clipRule: \"evenodd\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                    lineNumber: 522,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 521,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                            lineNumber: 520,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"ml-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-sm font-medium text-green-800\",\n                                    children: \"Success\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                    lineNumber: 526,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-green-700 mt-1\",\n                                    children: success\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                    lineNumber: 527,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                            lineNumber: 525,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                    lineNumber: 519,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                lineNumber: 518,\n                columnNumber: 9\n            }, this),\n            tokenInfo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow p-6 mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold mb-4\",\n                        children: \"Token Overview\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                        lineNumber: 536,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-500\",\n                                        children: \"Total Supply\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 539,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-blue-600\",\n                                        children: tokenInfo.totalSupply\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 540,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 538,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-500\",\n                                        children: \"Max Supply\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 545,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-green-600\",\n                                        children: tokenInfo.maxSupply\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 546,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 544,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-500\",\n                                        children: \"Status\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 551,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold \".concat(tokenInfo.paused ? 'text-red-600' : 'text-green-600'),\n                                        children: tokenInfo.paused ? 'Paused' : 'Active'\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 552,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 550,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                        lineNumber: 537,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-6 grid grid-cols-1 md:grid-cols-2 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-500\",\n                                        children: \"Token Price\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 560,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"font-medium\",\n                                        children: tokenInfo.metadata.tokenPrice\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 561,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 559,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-500\",\n                                        children: \"Bonus Tiers\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 564,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"font-medium\",\n                                        children: tokenInfo.metadata.bonusTiers\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 565,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 563,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                        lineNumber: 558,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                lineNumber: 535,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-600\",\n                    children: \"Full modular token management interface will be implemented here. This includes minting, pausing, admin controls, and upgrade management.\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                    lineNumber: 573,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                lineNumber: 572,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n        lineNumber: 480,\n        columnNumber: 5\n    }, this);\n}\n_s(ModularTokenDetailsPage, \"svge7dOzaj3+vx3iINKQ9qZOzC0=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        _hooks_useModularToken__WEBPACK_IMPORTED_MODULE_3__.useModularToken\n    ];\n});\n_c = ModularTokenDetailsPage;\nvar _c;\n$RefreshReg$(_c, \"ModularTokenDetailsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/modular-tokens/[address]/page.tsx\n"));

/***/ })

});