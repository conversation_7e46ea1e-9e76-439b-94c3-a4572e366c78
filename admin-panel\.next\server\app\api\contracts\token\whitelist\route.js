/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/contracts/token/whitelist/route";
exports.ids = ["app/api/contracts/token/whitelist/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcontracts%2Ftoken%2Fwhitelist%2Froute&page=%2Fapi%2Fcontracts%2Ftoken%2Fwhitelist%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcontracts%2Ftoken%2Fwhitelist%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcontracts%2Ftoken%2Fwhitelist%2Froute&page=%2Fapi%2Fcontracts%2Ftoken%2Fwhitelist%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcontracts%2Ftoken%2Fwhitelist%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_github_tokendev_newroo_admin_panel_src_app_api_contracts_token_whitelist_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/contracts/token/whitelist/route.ts */ \"(rsc)/./src/app/api/contracts/token/whitelist/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/contracts/token/whitelist/route\",\n        pathname: \"/api/contracts/token/whitelist\",\n        filename: \"route\",\n        bundlePath: \"app/api/contracts/token/whitelist/route\"\n    },\n    resolvedPagePath: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\api\\\\contracts\\\\token\\\\whitelist\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_github_tokendev_newroo_admin_panel_src_app_api_contracts_token_whitelist_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcontracts%2Ftoken%2Fwhitelist%2Froute&page=%2Fapi%2Fcontracts%2Ftoken%2Fwhitelist%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcontracts%2Ftoken%2Fwhitelist%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/contracts/token/whitelist/route.ts":
/*!********************************************************!*\
  !*** ./src/app/api/contracts/token/whitelist/route.ts ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/address/checks.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/providers/provider-jsonrpc.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/contract/contract.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/utils/units.js\");\n\n\nconst SECURITY_TOKEN_CORE_ABI = [\n    \"function isWhitelisted(address account) external view returns (bool)\",\n    \"function isVerified(address account) external view returns (bool)\",\n    \"function balanceOf(address account) external view returns (uint256)\",\n    \"function decimals() external view returns (uint8)\",\n    \"event AddressWhitelisted(address indexed account)\",\n    \"event AddressRemovedFromWhitelist(address indexed account)\"\n];\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const tokenAddress = searchParams.get('tokenAddress');\n        if (!tokenAddress) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Missing tokenAddress parameter'\n            }, {\n                status: 400\n            });\n        }\n        // Validate address\n        if (!ethers__WEBPACK_IMPORTED_MODULE_1__.isAddress(tokenAddress)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Invalid token address format'\n            }, {\n                status: 400\n            });\n        }\n        // Get environment variables\n        const rpcUrl = process.env.AMOY_RPC_URL;\n        if (!rpcUrl) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Server configuration error: Missing RPC URL'\n            }, {\n                status: 500\n            });\n        }\n        // Setup provider\n        const provider = new ethers__WEBPACK_IMPORTED_MODULE_2__.JsonRpcProvider(rpcUrl);\n        // Get token contract\n        const tokenContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(tokenAddress, SECURITY_TOKEN_CORE_ABI, provider);\n        console.log('Fetching whitelist for token:', tokenAddress);\n        // Method 1: Get addresses from events\n        let whitelistedAddresses = new Set();\n        try {\n            // Get AddressWhitelisted events\n            const whitelistFilter = tokenContract.filters.AddressWhitelisted();\n            const whitelistEvents = await tokenContract.queryFilter(whitelistFilter, -10000); // Last 10k blocks\n            console.log(`Found ${whitelistEvents.length} AddressWhitelisted events`);\n            for (const event of whitelistEvents){\n                if (event.args && event.args.account) {\n                    whitelistedAddresses.add(event.args.account.toLowerCase());\n                }\n            }\n            // Get AddressRemovedFromWhitelist events and remove those addresses\n            const removeFilter = tokenContract.filters.AddressRemovedFromWhitelist();\n            const removeEvents = await tokenContract.queryFilter(removeFilter, -10000);\n            console.log(`Found ${removeEvents.length} AddressRemovedFromWhitelist events`);\n            for (const event of removeEvents){\n                if (event.args && event.args.account) {\n                    whitelistedAddresses.delete(event.args.account.toLowerCase());\n                }\n            }\n        } catch (eventError) {\n            console.log('Error fetching events, using fallback method:', eventError.message);\n        }\n        // Method 2: Fallback - check known addresses that might be whitelisted\n        const knownAddresses = [\n            '******************************************',\n            '******************************************',\n            '******************************************' // Test address\n        ];\n        // If no events found, check known addresses\n        if (whitelistedAddresses.size === 0) {\n            console.log('No events found, checking known addresses...');\n            for (const address of knownAddresses){\n                try {\n                    const isWhitelisted = await tokenContract.isWhitelisted(address);\n                    if (isWhitelisted) {\n                        whitelistedAddresses.add(address.toLowerCase());\n                        console.log(`Found whitelisted address: ${address}`);\n                    }\n                } catch (error) {\n                    console.log(`Error checking ${address}:`, error.message);\n                }\n            }\n        }\n        // Convert Set to Array and get details for each address\n        const addressList = Array.from(whitelistedAddresses);\n        console.log(`Processing ${addressList.length} whitelisted addresses`);\n        const decimals = await tokenContract.decimals();\n        const addressDetails = await Promise.all(addressList.map(async (address)=>{\n            try {\n                const [isWhitelisted, isVerified, balance] = await Promise.all([\n                    tokenContract.isWhitelisted(address),\n                    tokenContract.isVerified(address),\n                    tokenContract.balanceOf(address)\n                ]);\n                // Double-check that address is still whitelisted\n                if (!isWhitelisted) {\n                    console.log(`Address ${address} no longer whitelisted, skipping`);\n                    return null;\n                }\n                const balanceFormatted = decimals === 0 ? balance.toString() : ethers__WEBPACK_IMPORTED_MODULE_4__.formatUnits(balance, decimals);\n                return {\n                    address: address,\n                    balance: balanceFormatted,\n                    isWhitelisted,\n                    isVerified,\n                    frozenTokens: '0' // Default, can be enhanced later\n                };\n            } catch (error) {\n                console.error(`Error processing address ${address}:`, error.message);\n                return null;\n            }\n        }));\n        // Filter out null results\n        const validAddresses = addressDetails.filter((addr)=>addr !== null);\n        console.log(`Returning ${validAddresses.length} valid whitelisted addresses`);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            tokenAddress,\n            whitelistedAddresses: validAddresses,\n            totalAddresses: validAddresses.length,\n            method: whitelistedAddresses.size > 0 ? 'events' : 'fallback'\n        });\n    } catch (error) {\n        console.error('Whitelist API error:', error);\n        let errorMessage = 'Failed to fetch whitelist';\n        if (error.message.includes('network')) {\n            errorMessage = 'Network connection error';\n        } else if (error.message.includes('contract')) {\n            errorMessage = 'Contract interaction error';\n        } else if (error.message) {\n            errorMessage = error.message;\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: errorMessage,\n            details: error.message\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/contracts/token/whitelist/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/ethers","vendor-chunks/@noble","vendor-chunks/@adraffy"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcontracts%2Ftoken%2Fwhitelist%2Froute&page=%2Fapi%2Fcontracts%2Ftoken%2Fwhitelist%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcontracts%2Ftoken%2Fwhitelist%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();