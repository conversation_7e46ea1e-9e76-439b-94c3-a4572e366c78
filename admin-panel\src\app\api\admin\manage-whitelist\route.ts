import { NextRequest, NextResponse } from 'next/server';
import { ethers } from 'ethers';

const SECURITY_TOKEN_CORE_ABI = [
  "function addToWhitelist(address account) external",
  "function removeFromWhitelist(address account) external",
  "function isWhitelisted(address account) external view returns (bool)"
];

export async function POST(request: NextRequest) {
  try {
    const { tokenAddress, address, action } = await request.json();

    if (!tokenAddress) {
      return NextResponse.json({ error: 'Token address is required' }, { status: 400 });
    }

    if (!address) {
      return NextResponse.json({ error: 'Address is required' }, { status: 400 });
    }

    if (!action || !['add', 'remove'].includes(action)) {
      return NextResponse.json({ error: 'Action must be "add" or "remove"' }, { status: 400 });
    }

    // Validate addresses
    if (!ethers.isAddress(tokenAddress) || !ethers.isAddress(address)) {
      return NextResponse.json({ error: 'Invalid address format' }, { status: 400 });
    }

    // Get environment variables
    const privateKey = process.env.CONTRACT_ADMIN_PRIVATE_KEY;
    const rpcUrl = process.env.AMOY_RPC_URL || process.env.NEXT_PUBLIC_AMOY_RPC_URL;

    if (!privateKey) {
      return NextResponse.json({ error: 'Admin private key not configured' }, { status: 500 });
    }

    if (!rpcUrl) {
      return NextResponse.json({ error: 'RPC URL not configured' }, { status: 500 });
    }

    console.log(`Attempting to ${action} address ${address} ${action === 'add' ? 'to' : 'from'} whitelist on token:`, tokenAddress);

    // Setup provider and signer
    const provider = new ethers.JsonRpcProvider(rpcUrl);
    const signer = new ethers.Wallet(privateKey, provider);

    // Get token contract
    const tokenContract = new ethers.Contract(tokenAddress, SECURITY_TOKEN_CORE_ABI, signer);

    // Check current whitelist status
    const currentlyWhitelisted = await tokenContract.isWhitelisted(address);
    console.log('Current whitelist status:', currentlyWhitelisted);

    // Validate action makes sense
    if (action === 'add' && currentlyWhitelisted) {
      return NextResponse.json({ 
        success: true, 
        message: 'Address is already whitelisted',
        alreadyInDesiredState: true 
      });
    }

    if (action === 'remove' && !currentlyWhitelisted) {
      return NextResponse.json({ 
        success: true, 
        message: 'Address is already not whitelisted',
        alreadyInDesiredState: true 
      });
    }

    // Multiple strategies to handle Amoy testnet issues
    const strategies = [
      // Strategy 1: Standard contract call
      async () => {
        const tx = action === 'add' 
          ? await tokenContract.addToWhitelist(address, { gasLimit: 300000, gasPrice: ethers.parseUnits('30', 'gwei') })
          : await tokenContract.removeFromWhitelist(address, { gasLimit: 300000, gasPrice: ethers.parseUnits('30', 'gwei') });
        return tx.wait();
      },
      // Strategy 2: Raw transaction approach
      async () => {
        const iface = new ethers.Interface(SECURITY_TOKEN_CORE_ABI);
        const functionName = action === 'add' ? 'addToWhitelist' : 'removeFromWhitelist';
        const data = iface.encodeFunctionData(functionName, [address]);
        const nonce = await signer.getNonce();
        
        const tx = await signer.sendTransaction({
          to: tokenAddress,
          data: data,
          gasLimit: 300000,
          gasPrice: ethers.parseUnits('30', 'gwei'),
          nonce: nonce
        });
        return tx.wait();
      }
    ];

    let lastError;
    for (let i = 0; i < strategies.length; i++) {
      try {
        console.log(`Attempting whitelist ${action} strategy ${i + 1}...`);
        const receipt = await strategies[i]();
        console.log(`Whitelist ${action} successful with strategy ${i + 1}. Tx hash:`, receipt.hash);

        // Verify the action worked
        const newWhitelistStatus = await tokenContract.isWhitelisted(address);
        const expectedStatus = action === 'add';
        
        if (newWhitelistStatus === expectedStatus) {
          return NextResponse.json({
            success: true,
            message: `Address ${action === 'add' ? 'added to' : 'removed from'} whitelist successfully`,
            transactionHash: receipt.hash,
            blockNumber: receipt.blockNumber,
            address: address,
            whitelisted: newWhitelistStatus,
            strategy: i + 1
          });
        } else {
          throw new Error(`Transaction succeeded but whitelist status didn't change as expected`);
        }
      } catch (error: any) {
        console.error(`Strategy ${i + 1} failed:`, error);
        lastError = error;
        
        // If this is not the last strategy, wait and try the next one
        if (i < strategies.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 2000));
        }
      }
    }

    // All strategies failed
    console.error('All whitelist strategies failed. Last error:', lastError);
    
    return NextResponse.json({
      error: `Failed to ${action} address ${action === 'add' ? 'to' : 'from'} whitelist after trying multiple methods`,
      details: lastError?.message || 'Unknown error',
      suggestion: 'The Amoy testnet may be experiencing issues. Please try again in a few minutes.'
    }, { status: 500 });

  } catch (error: any) {
    console.error('Manage whitelist API error:', error);
    return NextResponse.json({
      error: 'Internal server error',
      details: error.message
    }, { status: 500 });
  }
}
