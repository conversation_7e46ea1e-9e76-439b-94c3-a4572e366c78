"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/modular-tokens/[address]/page",{

/***/ "(app-pages-browser)/./src/hooks/useModularToken.ts":
/*!**************************************!*\
  !*** ./src/hooks/useModularToken.ts ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useModularToken: () => (/* binding */ useModularToken)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/providers/provider-browser.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/contract/contract.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/utils/units.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/crypto/keccak.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/utils/utf8.js\");\n/* harmony import */ var _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/contracts/SecurityTokenCore.json */ \"(app-pages-browser)/./src/contracts/SecurityTokenCore.json\");\n/* harmony import */ var _contracts_UpgradeManager_json__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contracts/UpgradeManager.json */ \"(app-pages-browser)/./src/contracts/UpgradeManager.json\");\n/* __next_internal_client_entry_do_not_use__ useModularToken auto */ \n\n// Import ABIs - extract the abi property from the JSON artifacts\n\n\n// Extract ABIs from artifacts\nconst SecurityTokenCoreABI = _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_1__.abi;\nconst UpgradeManagerABI = _contracts_UpgradeManager_json__WEBPACK_IMPORTED_MODULE_2__.abi;\n// Contract addresses from environment\nconst SECURITY_TOKEN_CORE_ADDRESS = \"******************************************\";\nconst UPGRADE_MANAGER_ADDRESS = \"0xb7a53dC340C2E5e823002B174629e2a44B8ed815\";\nfunction useModularToken(tokenAddress) {\n    const [provider, setProvider] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [signer, setSigner] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [tokenInfo, setTokenInfo] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [upgradeInfo, setUpgradeInfo] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [pendingUpgrades, setPendingUpgrades] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [upgradeHistory, setUpgradeHistory] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    // Initialize provider and signer\n    const initializeProvider = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[initializeProvider]\": async ()=>{\n            try {\n                if ( true && window.ethereum) {\n                    const provider = new ethers__WEBPACK_IMPORTED_MODULE_3__.BrowserProvider(window.ethereum);\n                    await provider.send('eth_requestAccounts', []);\n                    const signer = await provider.getSigner();\n                    setProvider(provider);\n                    setSigner(signer);\n                    return {\n                        provider,\n                        signer\n                    };\n                } else {\n                    throw new Error('MetaMask not found');\n                }\n            } catch (error) {\n                console.error('Error initializing provider:', error);\n                setError('Failed to connect to wallet');\n                return null;\n            }\n        }\n    }[\"useModularToken.useCallback[initializeProvider]\"], []);\n    // Load token information\n    const loadTokenInfo = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[loadTokenInfo]\": async ()=>{\n            const contractAddress = tokenAddress || SECURITY_TOKEN_CORE_ADDRESS;\n            if (!provider || !contractAddress) return;\n            try {\n                setLoading(true);\n                const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(contractAddress, SecurityTokenCoreABI, provider);\n                const [name, symbol, version, totalSupply, maxSupply, decimals, paused, metadata] = await Promise.all([\n                    contract.name(),\n                    contract.symbol(),\n                    contract.version(),\n                    contract.totalSupply(),\n                    contract.maxSupply(),\n                    contract.decimals(),\n                    contract.paused(),\n                    contract.getTokenMetadata()\n                ]);\n                console.log('Token contract data:');\n                console.log('- Name:', name);\n                console.log('- Symbol:', symbol);\n                console.log('- Decimals (raw):', decimals);\n                console.log('- Decimals (number):', Number(decimals));\n                console.log('- Total Supply (raw):', totalSupply.toString());\n                console.log('- Max Supply (raw):', maxSupply.toString());\n                const decimalsNumber = Number(decimals);\n                // For display purposes, format with proper precision\n                let formattedTotalSupply;\n                let formattedMaxSupply;\n                if (decimalsNumber === 0) {\n                    // For 0 decimals, show as whole numbers\n                    formattedTotalSupply = totalSupply.toString();\n                    formattedMaxSupply = maxSupply.toString();\n                } else {\n                    // For tokens with decimals, format and remove unnecessary trailing zeros\n                    const totalSupplyFormatted = ethers__WEBPACK_IMPORTED_MODULE_5__.formatUnits(totalSupply, decimalsNumber);\n                    const maxSupplyFormatted = ethers__WEBPACK_IMPORTED_MODULE_5__.formatUnits(maxSupply, decimalsNumber);\n                    // Remove trailing zeros after decimal point, but keep at least one decimal place\n                    formattedTotalSupply = parseFloat(totalSupplyFormatted).toLocaleString('en-US', {\n                        minimumFractionDigits: 0,\n                        maximumFractionDigits: 6\n                    });\n                    formattedMaxSupply = parseFloat(maxSupplyFormatted).toLocaleString('en-US', {\n                        minimumFractionDigits: 0,\n                        maximumFractionDigits: 6\n                    });\n                }\n                console.log('- Total Supply (formatted):', formattedTotalSupply);\n                console.log('- Max Supply (formatted):', formattedMaxSupply);\n                setTokenInfo({\n                    name,\n                    symbol,\n                    version,\n                    totalSupply: formattedTotalSupply,\n                    maxSupply: formattedMaxSupply,\n                    decimals: decimalsNumber,\n                    paused,\n                    metadata: {\n                        tokenPrice: metadata[0],\n                        bonusTiers: metadata[1],\n                        tokenDetails: metadata[2],\n                        tokenImageUrl: metadata[3]\n                    }\n                });\n            } catch (error) {\n                console.error('Error loading token info:', error);\n                setError('Failed to load token information');\n            } finally{\n                setLoading(false);\n            }\n        }\n    }[\"useModularToken.useCallback[loadTokenInfo]\"], [\n        provider,\n        tokenAddress\n    ]);\n    // Load upgrade information\n    const loadUpgradeInfo = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[loadUpgradeInfo]\": async ()=>{\n            if (!provider || !UPGRADE_MANAGER_ADDRESS) return;\n            try {\n                setLoading(true);\n                const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(UPGRADE_MANAGER_ADDRESS, UpgradeManagerABI, provider);\n                const [emergencyModeActive, registeredModules, upgradeDelay, emergencyModeDuration, pendingUpgradeIds] = await Promise.all([\n                    contract.isEmergencyModeActive(),\n                    contract.getRegisteredModules(),\n                    contract.UPGRADE_DELAY(),\n                    contract.EMERGENCY_MODE_DURATION(),\n                    contract.getPendingUpgradeIds()\n                ]);\n                setUpgradeInfo({\n                    emergencyModeActive,\n                    registeredModules,\n                    upgradeDelay: Number(upgradeDelay),\n                    emergencyModeDuration: Number(emergencyModeDuration)\n                });\n                // Load pending upgrades\n                const pendingUpgradesData = await Promise.all(pendingUpgradeIds.map({\n                    \"useModularToken.useCallback[loadUpgradeInfo]\": async (id)=>{\n                        const upgrade = await contract.pendingUpgrades(id);\n                        return {\n                            upgradeId: id,\n                            moduleId: upgrade.moduleId,\n                            proxy: upgrade.proxy,\n                            newImplementation: upgrade.newImplementation,\n                            executeTime: Number(upgrade.executeTime),\n                            executed: upgrade.executed,\n                            cancelled: upgrade.cancelled,\n                            description: upgrade.description\n                        };\n                    }\n                }[\"useModularToken.useCallback[loadUpgradeInfo]\"]));\n                setPendingUpgrades(pendingUpgradesData);\n                // Load upgrade history for SecurityTokenCore\n                const SECURITY_TOKEN_CORE_ID = ethers__WEBPACK_IMPORTED_MODULE_6__.keccak256(ethers__WEBPACK_IMPORTED_MODULE_7__.toUtf8Bytes(\"SECURITY_TOKEN_CORE\"));\n                const history = await contract.getUpgradeHistory(SECURITY_TOKEN_CORE_ID);\n                setUpgradeHistory(history.map({\n                    \"useModularToken.useCallback[loadUpgradeInfo]\": (record)=>({\n                            oldImplementation: record.oldImplementation,\n                            newImplementation: record.newImplementation,\n                            timestamp: Number(record.timestamp),\n                            executor: record.executor,\n                            version: record.version,\n                            isEmergency: record.isEmergency,\n                            description: record.description\n                        })\n                }[\"useModularToken.useCallback[loadUpgradeInfo]\"]));\n            } catch (error) {\n                console.error('Error loading upgrade info:', error);\n                setError('Failed to load upgrade information');\n            } finally{\n                setLoading(false);\n            }\n        }\n    }[\"useModularToken.useCallback[loadUpgradeInfo]\"], [\n        provider\n    ]);\n    // Mint tokens\n    const mintTokens = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[mintTokens]\": async (address, amount)=>{\n            const contractAddress = tokenAddress || SECURITY_TOKEN_CORE_ADDRESS;\n            if (!signer || !contractAddress) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(contractAddress, SecurityTokenCoreABI, signer);\n            try {\n                const decimals = (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.decimals) || 0;\n                const amountWei = ethers__WEBPACK_IMPORTED_MODULE_5__.parseUnits(amount, decimals);\n                const tx = await contract.mint(address, amountWei, {\n                    gasLimit: 400000\n                });\n                return tx.wait();\n            } catch (error) {\n                console.error('Mint tokens error:', error);\n                if (error.code === 'ACTION_REJECTED') {\n                    throw new Error('Transaction was rejected by user');\n                } else if (error.reason) {\n                    throw new Error(\"Contract error: \".concat(error.reason));\n                } else {\n                    throw new Error(\"Failed to mint tokens: \".concat(error.message));\n                }\n            }\n        }\n    }[\"useModularToken.useCallback[mintTokens]\"], [\n        signer,\n        tokenInfo,\n        tokenAddress\n    ]);\n    // Toggle pause state with multiple retry strategies\n    const togglePause = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[togglePause]\": async ()=>{\n            const contractAddress = tokenAddress || SECURITY_TOKEN_CORE_ADDRESS;\n            if (!signer || !contractAddress) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(contractAddress, SecurityTokenCoreABI, signer);\n            const isPausing = !(tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.paused);\n            const functionName = isPausing ? 'pause' : 'unpause';\n            // Try multiple strategies to handle Amoy testnet issues\n            const strategies = [\n                {\n                    \"useModularToken.useCallback[togglePause]\": // Strategy 1: Standard call with high gas\n                    async ()=>{\n                        const tx = isPausing ? await contract.pause({\n                            gasLimit: 500000,\n                            gasPrice: ethers__WEBPACK_IMPORTED_MODULE_5__.parseUnits('30', 'gwei')\n                        }) : await contract.unpause({\n                            gasLimit: 500000,\n                            gasPrice: ethers__WEBPACK_IMPORTED_MODULE_5__.parseUnits('30', 'gwei')\n                        });\n                        return tx.wait();\n                    }\n                }[\"useModularToken.useCallback[togglePause]\"],\n                {\n                    \"useModularToken.useCallback[togglePause]\": // Strategy 2: Raw transaction approach (like the working script)\n                    async ()=>{\n                        const functionSelector = isPausing ? '0x8456cb59' : '0x3f4ba83a';\n                        const nonce = await signer.getNonce();\n                        const tx = await signer.sendTransaction({\n                            to: contractAddress,\n                            data: functionSelector,\n                            gasLimit: 500000,\n                            gasPrice: ethers__WEBPACK_IMPORTED_MODULE_5__.parseUnits('30', 'gwei'),\n                            nonce: nonce\n                        });\n                        return tx.wait();\n                    }\n                }[\"useModularToken.useCallback[togglePause]\"]\n            ];\n            for(let i = 0; i < strategies.length; i++){\n                try {\n                    console.log(\"Attempting \".concat(functionName, \" strategy \").concat(i + 1, \"...\"));\n                    const result = await strategies[i]();\n                    console.log(\"\".concat(functionName, \" successful with strategy \").concat(i + 1));\n                    return result;\n                } catch (error) {\n                    console.error(\"Strategy \".concat(i + 1, \" failed:\"), error);\n                    if (error.code === 'ACTION_REJECTED') {\n                        throw new Error('Transaction was rejected by user');\n                    }\n                    // If this is the last strategy, throw a comprehensive error\n                    if (i === strategies.length - 1) {\n                        var _error_error;\n                        if (error.code === 'UNKNOWN_ERROR' && ((_error_error = error.error) === null || _error_error === void 0 ? void 0 : _error_error.code) === -32603) {\n                            throw new Error(\"Amoy testnet RPC error: The network is experiencing issues. Please try again in a few minutes, or use a different RPC endpoint.\");\n                        } else if (error.reason) {\n                            throw new Error(\"Contract error: \".concat(error.reason));\n                        } else {\n                            throw new Error(\"Failed to \".concat(functionName, \" token after trying multiple methods: \").concat(error.message));\n                        }\n                    }\n                    // Wait a bit before trying the next strategy\n                    await new Promise({\n                        \"useModularToken.useCallback[togglePause]\": (resolve)=>setTimeout(resolve, 1000)\n                    }[\"useModularToken.useCallback[togglePause]\"]);\n                }\n            }\n        }\n    }[\"useModularToken.useCallback[togglePause]\"], [\n        signer,\n        tokenInfo,\n        tokenAddress\n    ]);\n    // Schedule upgrade\n    const scheduleUpgrade = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[scheduleUpgrade]\": async (implementationAddress, description)=>{\n            if (!signer || !UPGRADE_MANAGER_ADDRESS) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(UPGRADE_MANAGER_ADDRESS, UpgradeManagerABI, signer);\n            const SECURITY_TOKEN_CORE_ID = ethers__WEBPACK_IMPORTED_MODULE_6__.keccak256(ethers__WEBPACK_IMPORTED_MODULE_7__.toUtf8Bytes(\"SECURITY_TOKEN_CORE\"));\n            const tx = await contract.scheduleUpgrade(SECURITY_TOKEN_CORE_ID, implementationAddress, description);\n            return tx.wait();\n        }\n    }[\"useModularToken.useCallback[scheduleUpgrade]\"], [\n        signer\n    ]);\n    // Execute upgrade\n    const executeUpgrade = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[executeUpgrade]\": async (upgradeId)=>{\n            if (!signer || !UPGRADE_MANAGER_ADDRESS) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(UPGRADE_MANAGER_ADDRESS, UpgradeManagerABI, signer);\n            const tx = await contract.executeUpgrade(upgradeId);\n            return tx.wait();\n        }\n    }[\"useModularToken.useCallback[executeUpgrade]\"], [\n        signer\n    ]);\n    // Emergency upgrade\n    const emergencyUpgrade = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[emergencyUpgrade]\": async (implementationAddress, description)=>{\n            if (!signer || !UPGRADE_MANAGER_ADDRESS) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(UPGRADE_MANAGER_ADDRESS, UpgradeManagerABI, signer);\n            const SECURITY_TOKEN_CORE_ID = ethers__WEBPACK_IMPORTED_MODULE_6__.keccak256(ethers__WEBPACK_IMPORTED_MODULE_7__.toUtf8Bytes(\"SECURITY_TOKEN_CORE\"));\n            const tx = await contract.emergencyUpgrade(SECURITY_TOKEN_CORE_ID, implementationAddress, description);\n            return tx.wait();\n        }\n    }[\"useModularToken.useCallback[emergencyUpgrade]\"], [\n        signer\n    ]);\n    // Toggle emergency mode\n    const toggleEmergencyMode = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[toggleEmergencyMode]\": async ()=>{\n            if (!signer || !UPGRADE_MANAGER_ADDRESS) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(UPGRADE_MANAGER_ADDRESS, UpgradeManagerABI, signer);\n            const tx = (upgradeInfo === null || upgradeInfo === void 0 ? void 0 : upgradeInfo.emergencyModeActive) ? await contract.deactivateEmergencyMode() : await contract.activateEmergencyMode();\n            return tx.wait();\n        }\n    }[\"useModularToken.useCallback[toggleEmergencyMode]\"], [\n        signer,\n        upgradeInfo\n    ]);\n    // Cancel upgrade\n    const cancelUpgrade = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[cancelUpgrade]\": async (upgradeId)=>{\n            if (!signer || !UPGRADE_MANAGER_ADDRESS) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(UPGRADE_MANAGER_ADDRESS, UpgradeManagerABI, signer);\n            const tx = await contract.cancelUpgrade(upgradeId);\n            return tx.wait();\n        }\n    }[\"useModularToken.useCallback[cancelUpgrade]\"], [\n        signer\n    ]);\n    // Update token price\n    const updateTokenPrice = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[updateTokenPrice]\": async (newPrice)=>{\n            const contractAddress = tokenAddress || SECURITY_TOKEN_CORE_ADDRESS;\n            if (!signer || !contractAddress) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(contractAddress, SecurityTokenCoreABI, signer);\n            try {\n                const tx = await contract.updateTokenPrice(newPrice, {\n                    gasLimit: 300000\n                });\n                return tx.wait();\n            } catch (error) {\n                console.error('Update token price error:', error);\n                if (error.code === 'ACTION_REJECTED') {\n                    throw new Error('Transaction was rejected by user');\n                } else if (error.reason) {\n                    throw new Error(\"Contract error: \".concat(error.reason));\n                } else {\n                    throw new Error(\"Failed to update token price: \".concat(error.message));\n                }\n            }\n        }\n    }[\"useModularToken.useCallback[updateTokenPrice]\"], [\n        signer,\n        tokenAddress\n    ]);\n    // Update bonus tiers\n    const updateBonusTiers = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[updateBonusTiers]\": async (newBonusTiers)=>{\n            const contractAddress = tokenAddress || SECURITY_TOKEN_CORE_ADDRESS;\n            if (!signer || !contractAddress) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(contractAddress, SecurityTokenCoreABI, signer);\n            try {\n                const tx = await contract.updateBonusTiers(newBonusTiers, {\n                    gasLimit: 300000\n                });\n                return tx.wait();\n            } catch (error) {\n                console.error('Update bonus tiers error:', error);\n                if (error.code === 'ACTION_REJECTED') {\n                    throw new Error('Transaction was rejected by user');\n                } else if (error.reason) {\n                    throw new Error(\"Contract error: \".concat(error.reason));\n                } else {\n                    throw new Error(\"Failed to update bonus tiers: \".concat(error.message));\n                }\n            }\n        }\n    }[\"useModularToken.useCallback[updateBonusTiers]\"], [\n        signer,\n        tokenAddress\n    ]);\n    // Update max supply\n    const updateMaxSupply = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[updateMaxSupply]\": async (newMaxSupply)=>{\n            const contractAddress = tokenAddress || SECURITY_TOKEN_CORE_ADDRESS;\n            if (!signer || !contractAddress) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(contractAddress, SecurityTokenCoreABI, signer);\n            try {\n                const maxSupplyWei = ethers__WEBPACK_IMPORTED_MODULE_5__.parseUnits(newMaxSupply, (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.decimals) || 0);\n                const tx = await contract.updateMaxSupply(maxSupplyWei, {\n                    gasLimit: 300000\n                });\n                return tx.wait();\n            } catch (error) {\n                console.error('Update max supply error:', error);\n                if (error.code === 'ACTION_REJECTED') {\n                    throw new Error('Transaction was rejected by user');\n                } else if (error.reason) {\n                    throw new Error(\"Contract error: \".concat(error.reason));\n                } else {\n                    throw new Error(\"Failed to update max supply: \".concat(error.message));\n                }\n            }\n        }\n    }[\"useModularToken.useCallback[updateMaxSupply]\"], [\n        signer,\n        tokenInfo,\n        tokenAddress\n    ]);\n    // Add to whitelist with identity registration\n    const addToWhitelist = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[addToWhitelist]\": async (address)=>{\n            const contractAddress = tokenAddress || SECURITY_TOKEN_CORE_ADDRESS;\n            if (!signer || !contractAddress) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(contractAddress, SecurityTokenCoreABI, signer);\n            try {\n                // First check if the address is already verified/registered\n                const isVerified = await contract.isVerified(address);\n                if (!isVerified) {\n                    // Need to register identity first - use the API approach for this\n                    throw new Error('Address must be registered first. Please use the API method or register the identity manually.');\n                }\n                // Now try to add to whitelist\n                const tx = await contract.addToWhitelist(address, {\n                    gasLimit: 400000,\n                    gasPrice: ethers__WEBPACK_IMPORTED_MODULE_5__.parseUnits('30', 'gwei')\n                });\n                return tx.wait();\n            } catch (error) {\n                console.error('Add to whitelist error:', error);\n                if (error.code === 'ACTION_REJECTED') {\n                    throw new Error('Transaction was rejected by user');\n                } else if (error.reason) {\n                    throw new Error(\"Contract error: \".concat(error.reason));\n                } else if (error.message.includes('identity not registered')) {\n                    throw new Error('Address must be registered first. Please use the API method which handles registration automatically.');\n                } else {\n                    throw new Error(\"Failed to add address to whitelist: \".concat(error.message));\n                }\n            }\n        }\n    }[\"useModularToken.useCallback[addToWhitelist]\"], [\n        signer,\n        tokenAddress\n    ]);\n    // Remove from whitelist\n    const removeFromWhitelist = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[removeFromWhitelist]\": async (address)=>{\n            const contractAddress = tokenAddress || SECURITY_TOKEN_CORE_ADDRESS;\n            if (!signer || !contractAddress) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(contractAddress, SecurityTokenCoreABI, signer);\n            try {\n                const tx = await contract.removeFromWhitelist(address, {\n                    gasLimit: 300000\n                });\n                return tx.wait();\n            } catch (error) {\n                console.error('Remove from whitelist error:', error);\n                if (error.code === 'ACTION_REJECTED') {\n                    throw new Error('Transaction was rejected by user');\n                } else if (error.reason) {\n                    throw new Error(\"Contract error: \".concat(error.reason));\n                } else {\n                    throw new Error(\"Failed to remove address from whitelist: \".concat(error.message));\n                }\n            }\n        }\n    }[\"useModularToken.useCallback[removeFromWhitelist]\"], [\n        signer,\n        tokenAddress\n    ]);\n    // Refresh all data\n    const refreshData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[refreshData]\": async ()=>{\n            await Promise.all([\n                loadTokenInfo(),\n                loadUpgradeInfo()\n            ]);\n        }\n    }[\"useModularToken.useCallback[refreshData]\"], [\n        loadTokenInfo,\n        loadUpgradeInfo\n    ]);\n    // Initialize on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useModularToken.useEffect\": ()=>{\n            initializeProvider();\n        }\n    }[\"useModularToken.useEffect\"], [\n        initializeProvider\n    ]);\n    // Load data when provider is ready\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useModularToken.useEffect\": ()=>{\n            if (provider && signer) {\n                refreshData();\n            }\n        }\n    }[\"useModularToken.useEffect\"], [\n        provider,\n        signer,\n        refreshData\n    ]);\n    return {\n        // State\n        provider,\n        signer,\n        tokenInfo,\n        upgradeInfo,\n        pendingUpgrades,\n        upgradeHistory,\n        loading,\n        error,\n        // Actions\n        initializeProvider,\n        loadTokenInfo,\n        loadUpgradeInfo,\n        mintTokens,\n        togglePause,\n        scheduleUpgrade,\n        executeUpgrade,\n        emergencyUpgrade,\n        toggleEmergencyMode,\n        cancelUpgrade,\n        refreshData,\n        // Admin Functions\n        updateTokenPrice,\n        updateBonusTiers,\n        updateMaxSupply,\n        addToWhitelist,\n        removeFromWhitelist,\n        // Utilities\n        setError,\n        setLoading\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useModularToken.ts\n"));

/***/ })

});