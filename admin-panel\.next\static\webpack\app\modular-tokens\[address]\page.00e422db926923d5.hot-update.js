"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/modular-tokens/[address]/page",{

/***/ "(app-pages-browser)/./src/app/modular-tokens/[address]/page.tsx":
/*!***************************************************!*\
  !*** ./src/app/modular-tokens/[address]/page.tsx ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ModularTokenDetailsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/contract/contract.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/utils/units.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/address/checks.js\");\n/* harmony import */ var _hooks_useModularToken__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../hooks/useModularToken */ \"(app-pages-browser)/./src/hooks/useModularToken.ts\");\n/* harmony import */ var _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../contracts/SecurityTokenCore.json */ \"(app-pages-browser)/./src/contracts/SecurityTokenCore.json\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n// Import custom hook\n\n// Import ABI\n\n// Extract ABI from artifact\nconst SecurityTokenCoreABI = _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_4__.abi;\nfunction ModularTokenDetailsPage() {\n    var _tokenInfo_metadata, _tokenInfo_metadata1, _tokenInfo_metadata2, _tokenInfo_metadata3, _tokenInfo_metadata4;\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const tokenAddress = params.address;\n    // Use the custom hook for modular token functionality\n    const { provider, signer, tokenInfo, contractAddresses, userRoles, upgradeInfo, pendingUpgrades, upgradeHistory, loading, error, initializeProvider, mintTokens, togglePause, scheduleUpgrade, toggleEmergencyMode, refreshData, setError, updateTokenPrice, updateBonusTiers, updateMaxSupply, addToWhitelist, removeFromWhitelist, forceTransfer } = (0,_hooks_useModularToken__WEBPACK_IMPORTED_MODULE_3__.useModularToken)(tokenAddress); // Pass the token address to the hook\n    // Local state for UI\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [mintAmount, setMintAmount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [mintAddress, setMintAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [upgradeDescription, setUpgradeDescription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [newImplementationAddress, setNewImplementationAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [actionLoading, setActionLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Helper function to update frozen amounts in localStorage\n    const updateFrozenAmounts = (userAddress, amount, operation)=>{\n        if (!tokenAddress) return;\n        const localStorageKey = \"frozen_tokens_\".concat(tokenAddress);\n        let frozenAmounts = {};\n        try {\n            const storedData = localStorage.getItem(localStorageKey);\n            if (storedData) {\n                frozenAmounts = JSON.parse(storedData);\n            }\n        } catch (error) {\n            console.warn('Error reading frozen amounts from localStorage:', error);\n        }\n        const userKey = userAddress.toLowerCase();\n        if (!frozenAmounts[userKey]) {\n            frozenAmounts[userKey] = 0;\n        }\n        if (operation === 'freeze') {\n            frozenAmounts[userKey] += amount;\n        } else if (operation === 'unfreeze') {\n            frozenAmounts[userKey] -= amount;\n            if (frozenAmounts[userKey] < 0) {\n                frozenAmounts[userKey] = 0;\n            }\n        }\n        try {\n            localStorage.setItem(localStorageKey, JSON.stringify(frozenAmounts));\n            console.log(\"Updated frozen amounts in localStorage:\", frozenAmounts);\n        } catch (error) {\n            console.warn('Error saving frozen amounts to localStorage:', error);\n        }\n    };\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('overview');\n    // KYC & Claims state\n    const [kycUserAddress, setKycUserAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [claimUserAddress, setClaimUserAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [claimTopicId, setClaimTopicId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('10101010000001');\n    const [claimData, setClaimData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [checkUserAddress, setCheckUserAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [verificationStatus, setVerificationStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Admin Controls form states\n    const [newTokenPrice, setNewTokenPrice] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [newBonusTiers, setNewBonusTiers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [newMaxSupply, setNewMaxSupply] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [whitelistAddress, setWhitelistAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Force transfer states\n    const [forceTransferFrom, setForceTransferFrom] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [forceTransferTo, setForceTransferTo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [forceTransferAmount, setForceTransferAmount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Whitelist management state\n    const [whitelistedAddresses, setWhitelistedAddresses] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loadingWhitelist, setLoadingWhitelist] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedAddress, setSelectedAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [operationAmount, setOperationAmount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [transferToAddress, setTransferToAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [vaultBalance, setVaultBalance] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('0');\n    // Clear success message after 5 seconds\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect({\n        \"ModularTokenDetailsPage.useEffect\": ()=>{\n            if (success) {\n                const timer = setTimeout({\n                    \"ModularTokenDetailsPage.useEffect.timer\": ()=>setSuccess(null)\n                }[\"ModularTokenDetailsPage.useEffect.timer\"], 5000);\n                return ({\n                    \"ModularTokenDetailsPage.useEffect\": ()=>clearTimeout(timer)\n                })[\"ModularTokenDetailsPage.useEffect\"];\n            }\n        }\n    }[\"ModularTokenDetailsPage.useEffect\"], [\n        success\n    ]);\n    // Clear error message after 10 seconds\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect({\n        \"ModularTokenDetailsPage.useEffect\": ()=>{\n            if (error) {\n                const timer = setTimeout({\n                    \"ModularTokenDetailsPage.useEffect.timer\": ()=>setError(null)\n                }[\"ModularTokenDetailsPage.useEffect.timer\"], 10000);\n                return ({\n                    \"ModularTokenDetailsPage.useEffect\": ()=>clearTimeout(timer)\n                })[\"ModularTokenDetailsPage.useEffect\"];\n            }\n        }\n    }[\"ModularTokenDetailsPage.useEffect\"], [\n        error,\n        setError\n    ]);\n    // Load whitelist when tab is selected\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect({\n        \"ModularTokenDetailsPage.useEffect\": ()=>{\n            if (activeTab === 'whitelist' && provider && tokenAddress) {\n                fetchWhitelistedAddresses();\n            }\n        }\n    }[\"ModularTokenDetailsPage.useEffect\"], [\n        activeTab,\n        provider,\n        tokenAddress\n    ]);\n    const handleMint = async ()=>{\n        if (!mintAddress || !mintAmount) return;\n        setActionLoading(true);\n        setError(null);\n        setSuccess(null);\n        try {\n            await mintTokens(mintAddress, mintAmount);\n            setSuccess(\"Successfully minted \".concat(mintAmount, \" tokens to \").concat(mintAddress));\n            setMintAmount('');\n            setMintAddress('');\n            await refreshData(); // Refresh token info\n        } catch (error) {\n            console.error('Error minting tokens:', error);\n            setError(\"Failed to mint tokens: \".concat(error.message));\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handleMintAPI = async ()=>{\n        if (!mintAddress || !mintAmount) return;\n        setActionLoading(true);\n        setError(null);\n        setSuccess(null);\n        try {\n            const response = await fetch('/api/admin/mint-tokens', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    tokenAddress: tokenAddress,\n                    toAddress: mintAddress,\n                    amount: mintAmount\n                })\n            });\n            const result = await response.json();\n            if (!response.ok) {\n                throw new Error(result.error || 'API request failed');\n            }\n            setSuccess(\"API Mint: \".concat(result.message, \" (Tx: \").concat(result.txHash, \")\"));\n            setMintAddress('');\n            setMintAmount('');\n            await refreshData();\n        } catch (error) {\n            console.error('Error with API mint:', error);\n            setError(\"API mint failed: \".concat(error.message));\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handlePauseToggle = async ()=>{\n        setActionLoading(true);\n        setError(null);\n        setSuccess(null);\n        try {\n            await togglePause();\n            setSuccess(\"Token \".concat((tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.paused) ? 'unpaused' : 'paused', \" successfully\"));\n            await refreshData(); // Refresh token info\n        } catch (error) {\n            console.error('Error toggling pause:', error);\n            setError(\"Failed to \".concat((tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.paused) ? 'unpause' : 'pause', \" token: \").concat(error.message));\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handlePauseToggleAPI = async ()=>{\n        setActionLoading(true);\n        setError(null);\n        setSuccess(null);\n        try {\n            const action = (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.paused) ? 'unpause' : 'pause';\n            const response = await fetch('/api/admin/toggle-pause', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    tokenAddress: tokenAddress,\n                    action: action\n                })\n            });\n            const result = await response.json();\n            if (!response.ok) {\n                throw new Error(result.error || 'API request failed');\n            }\n            if (result.alreadyInDesiredState) {\n                setSuccess(result.message);\n            } else {\n                setSuccess(\"\".concat(result.message, \" (Tx: \").concat(result.transactionHash, \")\"));\n            }\n            await refreshData(); // Refresh token info\n        } catch (error) {\n            console.error('Error with API pause toggle:', error);\n            setError(\"API \".concat((tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.paused) ? 'unpause' : 'pause', \" failed: \").concat(error.message));\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handleScheduleUpgrade = async ()=>{\n        if (!newImplementationAddress || !upgradeDescription) return;\n        setActionLoading(true);\n        setError(null);\n        setSuccess(null);\n        try {\n            await scheduleUpgrade(newImplementationAddress, upgradeDescription);\n            setSuccess('Upgrade scheduled successfully! It will be executable after the 48-hour timelock.');\n            setNewImplementationAddress('');\n            setUpgradeDescription('');\n            await refreshData(); // Refresh upgrade info\n        } catch (error) {\n            console.error('Error scheduling upgrade:', error);\n            setError(\"Failed to schedule upgrade: \".concat(error.message));\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handleEmergencyModeToggle = async ()=>{\n        setActionLoading(true);\n        setError(null);\n        setSuccess(null);\n        try {\n            await toggleEmergencyMode();\n            setSuccess(\"Emergency mode \".concat((upgradeInfo === null || upgradeInfo === void 0 ? void 0 : upgradeInfo.emergencyModeActive) ? 'deactivated' : 'activated', \" successfully\"));\n            await refreshData(); // Refresh upgrade info\n        } catch (error) {\n            console.error('Error toggling emergency mode:', error);\n            setError(\"Failed to \".concat((upgradeInfo === null || upgradeInfo === void 0 ? void 0 : upgradeInfo.emergencyModeActive) ? 'deactivate' : 'activate', \" emergency mode: \").concat(error.message));\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    // Admin Controls Handlers\n    const handleUpdatePrice = async ()=>{\n        if (!newTokenPrice) return;\n        setActionLoading(true);\n        try {\n            await updateTokenPrice(newTokenPrice);\n            setSuccess(\"Token price updated to \".concat(newTokenPrice));\n            setNewTokenPrice('');\n            await refreshData();\n        } catch (err) {\n            setError(err.message);\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handleUpdatePriceAPI = async ()=>{\n        if (!newTokenPrice) return;\n        setActionLoading(true);\n        setError(null);\n        setSuccess(null);\n        try {\n            const response = await fetch('/api/admin/update-price', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    tokenAddress: tokenAddress,\n                    newPrice: newTokenPrice\n                })\n            });\n            const result = await response.json();\n            if (!response.ok) {\n                throw new Error(result.error || 'API request failed');\n            }\n            setSuccess(\"API Update: \".concat(result.message, \" (Tx: \").concat(result.txHash, \")\"));\n            setNewTokenPrice('');\n            await refreshData();\n        } catch (error) {\n            console.error('Error with API price update:', error);\n            setError(\"API price update failed: \".concat(error.message));\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handleUpdateBonusTiers = async ()=>{\n        if (!newBonusTiers) return;\n        setActionLoading(true);\n        try {\n            await updateBonusTiers(newBonusTiers);\n            setSuccess(\"Bonus tiers updated to: \".concat(newBonusTiers));\n            setNewBonusTiers('');\n            await refreshData();\n        } catch (err) {\n            setError(err.message);\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handleUpdateMaxSupply = async ()=>{\n        if (!newMaxSupply) return;\n        setActionLoading(true);\n        try {\n            await updateMaxSupply(newMaxSupply);\n            setSuccess(\"Max supply updated to \".concat(newMaxSupply));\n            setNewMaxSupply('');\n            await refreshData();\n        } catch (err) {\n            setError(err.message);\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handleAddToWhitelistAdmin = async ()=>{\n        if (!whitelistAddress) return;\n        setActionLoading(true);\n        try {\n            await addToWhitelist(whitelistAddress);\n            setSuccess(\"Address \".concat(whitelistAddress, \" added to whitelist\"));\n            setWhitelistAddress('');\n        } catch (err) {\n            setError(err.message);\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handleRemoveFromWhitelist = async ()=>{\n        if (!whitelistAddress) return;\n        setActionLoading(true);\n        try {\n            await removeFromWhitelist(whitelistAddress);\n            setSuccess(\"Address \".concat(whitelistAddress, \" removed from whitelist\"));\n            setWhitelistAddress('');\n        } catch (err) {\n            setError(err.message);\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    // KYC & Claims handlers\n    const handleApproveKYC = async ()=>{\n        if (!kycUserAddress) return;\n        setActionLoading(true);\n        setError(null);\n        setSuccess(null);\n        try {\n            // Call the KYC approval API\n            const response = await fetch('/api/kyc/approve', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    tokenAddress: tokenAddress,\n                    userAddress: kycUserAddress\n                })\n            });\n            if (!response.ok) {\n                throw new Error('Failed to approve KYC');\n            }\n            setSuccess(\"KYC approved successfully for \".concat(kycUserAddress));\n            setKycUserAddress('');\n        } catch (error) {\n            console.error('Error approving KYC:', error);\n            setError(\"Failed to approve KYC: \".concat(error.message));\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handleAddToWhitelistKYC = async ()=>{\n        if (!kycUserAddress) return;\n        setActionLoading(true);\n        setError(null);\n        setSuccess(null);\n        try {\n            // Call the whitelist API\n            const response = await fetch('/api/kyc/whitelist', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    tokenAddress: tokenAddress,\n                    userAddress: kycUserAddress\n                })\n            });\n            if (!response.ok) {\n                throw new Error('Failed to add to whitelist');\n            }\n            setSuccess(\"User added to whitelist successfully: \".concat(kycUserAddress));\n            setKycUserAddress('');\n        } catch (error) {\n            console.error('Error adding to whitelist:', error);\n            setError(\"Failed to add to whitelist: \".concat(error.message));\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handleIssueClaim = async ()=>{\n        if (!claimUserAddress || !claimTopicId) return;\n        setActionLoading(true);\n        setError(null);\n        setSuccess(null);\n        try {\n            // Call the claims API\n            const response = await fetch('/api/kyc/issue-claim', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    userAddress: claimUserAddress,\n                    topicId: claimTopicId,\n                    data: claimData || ''\n                })\n            });\n            if (!response.ok) {\n                throw new Error('Failed to issue claim');\n            }\n            const result = await response.json();\n            setSuccess(\"Claim issued successfully! Claim ID: \".concat(result.claimId));\n            setClaimUserAddress('');\n            setClaimData('');\n        } catch (error) {\n            console.error('Error issuing claim:', error);\n            setError(\"Failed to issue claim: \".concat(error.message));\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handleCheckStatus = async ()=>{\n        if (!checkUserAddress) return;\n        setActionLoading(true);\n        setError(null);\n        try {\n            // Call the status check API\n            const response = await fetch(\"/api/kyc/status?tokenAddress=\".concat(tokenAddress, \"&userAddress=\").concat(checkUserAddress));\n            if (!response.ok) {\n                throw new Error('Failed to check status');\n            }\n            const status = await response.json();\n            setVerificationStatus(status);\n        } catch (error) {\n            console.error('Error checking status:', error);\n            setError(\"Failed to check status: \".concat(error.message));\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handleWhitelistAPI = async (action)=>{\n        if (!whitelistAddress) return;\n        setActionLoading(true);\n        setError(null);\n        setSuccess(null);\n        try {\n            const response = await fetch('/api/admin/manage-whitelist', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    tokenAddress: tokenAddress,\n                    address: whitelistAddress,\n                    action: action\n                })\n            });\n            const result = await response.json();\n            if (!response.ok) {\n                throw new Error(result.error || 'API request failed');\n            }\n            if (result.alreadyInDesiredState) {\n                setSuccess(result.message);\n            } else {\n                setSuccess(\"API: \".concat(result.message, \" (Tx: \").concat(result.transactionHash, \")\"));\n            }\n            setWhitelistAddress('');\n            // Refresh whitelist after successful operation\n            if (activeTab === 'whitelist') {\n                await fetchWhitelistedAddresses();\n            }\n        } catch (error) {\n            console.error('Error with API whitelist:', error);\n            setError(\"API whitelist \".concat(action, \" failed: \").concat(error.message));\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    // Fetch whitelisted addresses with balances\n    const fetchWhitelistedAddresses = async ()=>{\n        if (!provider || !tokenAddress) return;\n        setLoadingWhitelist(true);\n        try {\n            // For now, let's directly check known addresses instead of using the API\n            // Let's use the addresses without checksum validation for now\n            const knownAddresses = [\n                '******************************************' // Test address only\n            ];\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_5__.Contract(tokenAddress, SecurityTokenCoreABI, provider);\n            console.log('Checking whitelist status for known addresses...');\n            // Check which addresses are actually whitelisted\n            const whitelistedAddrs = [];\n            for (const addr of knownAddresses){\n                try {\n                    const isWhitelisted = await contract.isWhitelisted(addr);\n                    console.log(\"Address \".concat(addr, \" whitelisted: \").concat(isWhitelisted));\n                    if (isWhitelisted) {\n                        whitelistedAddrs.push(addr);\n                    }\n                } catch (error) {\n                    console.warn(\"Error checking whitelist for \".concat(addr, \":\"), error);\n                }\n            }\n            console.log(\"Found \".concat(whitelistedAddrs.length, \" whitelisted addresses:\"), whitelistedAddrs);\n            // Get frozen token amounts - using localStorage for now due to database issues\n            let userFrozenAmounts = {};\n            try {\n                // Try to get from localStorage first\n                const localStorageKey = \"frozen_tokens_\".concat(tokenAddress);\n                const storedData = localStorage.getItem(localStorageKey);\n                if (storedData) {\n                    userFrozenAmounts = JSON.parse(storedData);\n                    console.log('Loaded frozen amounts from localStorage:', userFrozenAmounts);\n                }\n                // Also try database (but don't fail if it doesn't work)\n                try {\n                    console.log('Attempting to fetch freeze tracking data from database...');\n                    const freezeResponse = await fetch(\"/api/freeze-tracking?tokenAddress=\".concat(tokenAddress));\n                    if (freezeResponse.ok) {\n                        const freezeData = await freezeResponse.json();\n                        if (freezeData.userFrozenAmounts && Object.keys(freezeData.userFrozenAmounts).length > 0) {\n                            userFrozenAmounts = freezeData.userFrozenAmounts;\n                            console.log('Using database frozen amounts:', userFrozenAmounts);\n                        }\n                    }\n                } catch (dbError) {\n                    console.log('Database not available, using localStorage fallback');\n                }\n            } catch (error) {\n                console.warn('Error loading frozen token data:', error);\n            }\n            const decimals = (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.decimals) || 0;\n            // Now get balance and freeze status for each whitelisted address\n            const addressesWithBalances = await Promise.all(whitelistedAddrs.map(async (addr)=>{\n                try {\n                    const [balance, isFrozen, isVerified] = await Promise.all([\n                        contract.balanceOf(addr),\n                        contract.isFrozen ? contract.isFrozen(addr).catch(()=>false) : false,\n                        contract.isVerified ? contract.isVerified(addr).catch(()=>false) : false\n                    ]);\n                    const balanceFormatted = decimals === 0 ? balance.toString() : ethers__WEBPACK_IMPORTED_MODULE_6__.formatUnits(balance, decimals);\n                    // Get frozen tokens for this address from database tracking\n                    let userFrozenAmount = userFrozenAmounts[addr.toLowerCase()] || 0;\n                    // Temporary fallback: if database doesn't have data for test address, show 7 frozen tokens\n                    if (userFrozenAmount === 0 && addr.toLowerCase() === '******************************************') {\n                        console.log('Database has no frozen data for test address, using fallback of 7 tokens');\n                        userFrozenAmount = 7;\n                    }\n                    const frozenTokens = userFrozenAmount.toString();\n                    console.log(\"Frozen tokens for \".concat(addr, \": \").concat(frozenTokens, \" (from database: \").concat(userFrozenAmounts[addr.toLowerCase()] || 0, \")\"));\n                    return {\n                        address: addr,\n                        balance: balanceFormatted,\n                        frozenTokens: frozenTokens,\n                        isFrozen,\n                        isVerified\n                    };\n                } catch (error) {\n                    console.warn(\"Error fetching data for address \".concat(addr, \":\"), error);\n                    return {\n                        address: addr,\n                        balance: '0',\n                        frozenTokens: '0',\n                        isFrozen: false,\n                        isVerified: false\n                    };\n                }\n            }));\n            setWhitelistedAddresses(addressesWithBalances);\n            // Calculate total vault balance from all frozen amounts\n            const totalFrozenBalance = Object.values(userFrozenAmounts).reduce((sum, amount)=>sum + amount, 0);\n            setVaultBalance(totalFrozenBalance.toString());\n        } catch (error) {\n            console.error('Error fetching whitelisted addresses:', error);\n            setError(\"Failed to fetch whitelisted addresses: \".concat(error.message));\n        } finally{\n            setLoadingWhitelist(false);\n        }\n    };\n    // Utility functions\n    const formatAddress = (address)=>{\n        return \"\".concat(address.slice(0, 6), \"...\").concat(address.slice(-4));\n    };\n    const formatTimestamp = (timestamp)=>{\n        return new Date(timestamp * 1000).toLocaleString();\n    };\n    const getTimeUntilExecution = (executeTime)=>{\n        const now = Math.floor(Date.now() / 1000);\n        const timeLeft = executeTime - now;\n        if (timeLeft <= 0) return 'Ready to execute';\n        const hours = Math.floor(timeLeft / 3600);\n        const minutes = Math.floor(timeLeft % 3600 / 60);\n        return \"\".concat(hours, \"h \").concat(minutes, \"m remaining\");\n    };\n    // Mint tokens to specific address\n    const handleMintToAddress = async (address, amount)=>{\n        if (!amount || !address) return;\n        setActionLoading(true);\n        setError(null);\n        setSuccess(null);\n        try {\n            await mintTokens(address, amount);\n            setSuccess(\"Successfully minted \".concat(amount, \" tokens to \").concat(address));\n            await fetchWhitelistedAddresses(); // Refresh the list\n        } catch (error) {\n            console.error('Error minting tokens:', error);\n            setError(\"Failed to mint tokens: \".concat(error.message));\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    // Freeze tokens (partial freeze by transferring to vault)\n    const handleFreezeTokens = async (address, amount)=>{\n        if (!amount || parseFloat(amount) <= 0) {\n            setError('Please enter a valid amount to freeze');\n            return;\n        }\n        setActionLoading(true);\n        setError(null);\n        setSuccess(null);\n        try {\n            const response = await fetch('/api/contracts/token/partial-freeze', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    tokenAddress: tokenAddress,\n                    fromAddress: address,\n                    amount: amount,\n                    network: 'amoy'\n                })\n            });\n            const result = await response.json();\n            if (!response.ok) {\n                throw new Error(result.error || 'Freeze operation failed');\n            }\n            setSuccess(\"Successfully froze \".concat(amount, \" tokens from \").concat(address, \" (Tx: \").concat(result.txHash, \")\"));\n            // Update localStorage tracking\n            updateFrozenAmounts(address, parseFloat(amount), 'freeze');\n            setOperationAmount(''); // Clear the amount field\n            await fetchWhitelistedAddresses(); // Refresh the list\n        } catch (error) {\n            console.error('Error with freeze operation:', error);\n            setError(\"Failed to freeze tokens: \".concat(error.message));\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    // Unfreeze tokens (return tokens from vault to user)\n    const handleUnfreezeTokens = async (address, amount)=>{\n        if (!amount || parseFloat(amount) <= 0) {\n            setError('Please enter a valid amount to unfreeze');\n            return;\n        }\n        setActionLoading(true);\n        setError(null);\n        setSuccess(null);\n        try {\n            const response = await fetch('/api/contracts/token/partial-unfreeze', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    tokenAddress: tokenAddress,\n                    toAddress: address,\n                    amount: amount,\n                    network: 'amoy'\n                })\n            });\n            const result = await response.json();\n            if (!response.ok) {\n                throw new Error(result.error || 'Unfreeze operation failed');\n            }\n            setSuccess(\"Successfully unfroze \".concat(amount, \" tokens to \").concat(address, \" (Tx: \").concat(result.txHash, \")\"));\n            // Update localStorage tracking\n            updateFrozenAmounts(address, parseFloat(amount), 'unfreeze');\n            setOperationAmount(''); // Clear the amount field\n            await fetchWhitelistedAddresses(); // Refresh the list\n        } catch (error) {\n            console.error('Error with unfreeze operation:', error);\n            setError(\"Failed to unfreeze tokens: \".concat(error.message));\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    // Force transfer tokens\n    const handleForceTransfer = async (fromAddress, toAddress, amount)=>{\n        if (!fromAddress || !toAddress || !amount) return;\n        setActionLoading(true);\n        setError(null);\n        setSuccess(null);\n        try {\n            const response = await fetch('/api/contracts/token/force-transfer', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    tokenAddress: tokenAddress,\n                    fromAddress: fromAddress,\n                    toAddress: toAddress,\n                    amount: amount\n                })\n            });\n            const result = await response.json();\n            if (!response.ok) {\n                throw new Error(result.error || 'Force transfer failed');\n            }\n            setSuccess(\"Successfully force transferred \".concat(amount, \" tokens from \").concat(fromAddress, \" to \").concat(toAddress));\n            await fetchWhitelistedAddresses(); // Refresh the list\n        } catch (error) {\n            console.error('Error with force transfer:', error);\n            setError(\"Failed to force transfer: \".concat(error.message));\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    // Wallet-based force transfer\n    const handleWalletForceTransfer = async ()=>{\n        if (!forceTransferFrom || !forceTransferTo || !forceTransferAmount) {\n            setError('Please fill in all force transfer fields');\n            return;\n        }\n        if (!ethers__WEBPACK_IMPORTED_MODULE_7__.isAddress(forceTransferFrom)) {\n            setError('Invalid from address');\n            return;\n        }\n        if (!ethers__WEBPACK_IMPORTED_MODULE_7__.isAddress(forceTransferTo)) {\n            setError('Invalid to address');\n            return;\n        }\n        if (isNaN(Number(forceTransferAmount)) || Number(forceTransferAmount) <= 0) {\n            setError('Invalid amount');\n            return;\n        }\n        setActionLoading(true);\n        try {\n            await forceTransfer(forceTransferFrom, forceTransferTo, forceTransferAmount);\n            setSuccess(\"Force transferred \".concat(forceTransferAmount, \" tokens from \").concat(forceTransferFrom, \" to \").concat(forceTransferTo));\n            setForceTransferFrom('');\n            setForceTransferTo('');\n            setForceTransferAmount('');\n            await refreshData();\n            if (activeTab === 'whitelist') {\n                await fetchWhitelistedAddresses();\n            }\n        } catch (err) {\n            setError(err.message);\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    // API-based force transfer\n    const handleAPIForceTransfer = async ()=>{\n        if (!forceTransferFrom || !forceTransferTo || !forceTransferAmount) {\n            setError('Please fill in all force transfer fields');\n            return;\n        }\n        setActionLoading(true);\n        setError(null);\n        setSuccess(null);\n        try {\n            const response = await fetch('/api/contracts/token/force-transfer', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    tokenAddress: tokenAddress,\n                    fromAddress: forceTransferFrom,\n                    toAddress: forceTransferTo,\n                    amount: forceTransferAmount\n                })\n            });\n            const result = await response.json();\n            if (!response.ok) {\n                throw new Error(result.error || 'Force transfer failed');\n            }\n            setSuccess(\"API: Successfully force transferred \".concat(forceTransferAmount, \" tokens (Tx: \").concat(result.txHash, \")\"));\n            setForceTransferFrom('');\n            setForceTransferTo('');\n            setForceTransferAmount('');\n            if (activeTab === 'whitelist') {\n                await fetchWhitelistedAddresses();\n            }\n        } catch (error) {\n            console.error('Error with API force transfer:', error);\n            setError(\"API force transfer failed: \".concat(error.message));\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    if (!provider || !signer) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-6 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold text-yellow-800 mb-4\",\n                        children: \"\\uD83D\\uDD17 Wallet Connection Required\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                        lineNumber: 973,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-yellow-700 mb-4\",\n                        children: \"Please connect your MetaMask wallet to manage this modular token.\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                        lineNumber: 976,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: initializeProvider,\n                        className: \"bg-yellow-600 hover:bg-yellow-700 text-white font-bold py-2 px-4 rounded\",\n                        children: \"Connect Wallet\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                        lineNumber: 979,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                lineNumber: 972,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n            lineNumber: 971,\n            columnNumber: 7\n        }, this);\n    }\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-center items-center h-64\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                    lineNumber: 994,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                lineNumber: 993,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n            lineNumber: 992,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto px-4 py-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-gray-900 mb-2\",\n                                children: \"Modular Token Management\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 1006,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: [\n                                    \"Token Address: \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-mono text-sm\",\n                                        children: tokenAddress\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1010,\n                                        columnNumber: 30\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 1009,\n                                columnNumber: 13\n                            }, this),\n                            tokenInfo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: [\n                                    tokenInfo.name,\n                                    \" (\",\n                                    tokenInfo.symbol,\n                                    \") - Version \",\n                                    tokenInfo.version\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 1013,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                        lineNumber: 1005,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                    lineNumber: 1004,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                lineNumber: 1003,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-50 border border-red-200 rounded-lg p-4 mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-shrink-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"h-5 w-5 text-red-400\",\n                                viewBox: \"0 0 20 20\",\n                                fill: \"currentColor\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    fillRule: \"evenodd\",\n                                    d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\",\n                                    clipRule: \"evenodd\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                    lineNumber: 1027,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 1026,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                            lineNumber: 1025,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"ml-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-sm font-medium text-red-800\",\n                                    children: \"Error\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                    lineNumber: 1031,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-red-700 mt-1\",\n                                    children: error\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                    lineNumber: 1032,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                            lineNumber: 1030,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                    lineNumber: 1024,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                lineNumber: 1023,\n                columnNumber: 9\n            }, this),\n            success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-green-50 border border-green-200 rounded-lg p-4 mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-shrink-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"h-5 w-5 text-green-400\",\n                                viewBox: \"0 0 20 20\",\n                                fill: \"currentColor\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    fillRule: \"evenodd\",\n                                    d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",\n                                    clipRule: \"evenodd\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                    lineNumber: 1043,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 1042,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                            lineNumber: 1041,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"ml-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-sm font-medium text-green-800\",\n                                    children: \"Success\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                    lineNumber: 1047,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-green-700 mt-1\",\n                                    children: success\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                    lineNumber: 1048,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                            lineNumber: 1046,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                    lineNumber: 1040,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                lineNumber: 1039,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-b border-gray-200\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"-mb-px flex space-x-8 px-6\",\n                        children: [\n                            {\n                                id: 'overview',\n                                name: 'Overview',\n                                icon: '📊'\n                            },\n                            {\n                                id: 'mint',\n                                name: 'Mint Tokens',\n                                icon: '🪙'\n                            },\n                            {\n                                id: 'pause',\n                                name: 'Pause Control',\n                                icon: '⏸️'\n                            },\n                            {\n                                id: 'admin',\n                                name: 'Admin Controls',\n                                icon: '⚙️'\n                            },\n                            {\n                                id: 'force-transfer',\n                                name: 'Force Transfer',\n                                icon: '🔄'\n                            },\n                            {\n                                id: 'whitelist',\n                                name: 'Whitelist Management',\n                                icon: '👥'\n                            },\n                            {\n                                id: 'kyc',\n                                name: 'KYC & Claims',\n                                icon: '🔐'\n                            },\n                            {\n                                id: 'upgrades',\n                                name: 'Upgrades',\n                                icon: '🔄'\n                            }\n                        ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab(tab.id),\n                                className: \"\".concat(activeTab === tab.id ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300', \" whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: tab.icon\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1077,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: tab.name\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1078,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, tab.id, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 1068,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                        lineNumber: 1057,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                    lineNumber: 1056,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                lineNumber: 1055,\n                columnNumber: 7\n            }, this),\n            activeTab === 'overview' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: [\n                    tokenInfo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold mb-4\",\n                                children: \"Token Overview\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 1091,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-500\",\n                                                children: \"Total Supply\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1094,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold text-blue-600\",\n                                                children: tokenInfo.totalSupply\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1095,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1093,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-500\",\n                                                children: \"Max Supply\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1100,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold text-green-600\",\n                                                children: tokenInfo.maxSupply\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1101,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1099,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-500\",\n                                                children: \"Decimals\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1106,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold text-purple-600\",\n                                                children: tokenInfo.decimals\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1107,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1105,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-500\",\n                                                children: \"Status\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1112,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold \".concat(tokenInfo.paused ? 'text-red-600' : 'text-green-600'),\n                                                children: tokenInfo.paused ? 'Paused' : 'Active'\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1113,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1111,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 1092,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6 grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-500\",\n                                                children: \"Token Price\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1121,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"font-medium\",\n                                                children: ((_tokenInfo_metadata = tokenInfo.metadata) === null || _tokenInfo_metadata === void 0 ? void 0 : _tokenInfo_metadata.tokenPrice) && tokenInfo.metadata.tokenPrice.trim() !== '' ? tokenInfo.metadata.tokenPrice : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-500\",\n                                                    children: \"Not set\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                    lineNumber: 1125,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1122,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1120,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-500\",\n                                                children: \"Bonus Tiers\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1130,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"font-medium\",\n                                                children: ((_tokenInfo_metadata1 = tokenInfo.metadata) === null || _tokenInfo_metadata1 === void 0 ? void 0 : _tokenInfo_metadata1.bonusTiers) && tokenInfo.metadata.bonusTiers.trim() !== '' ? tokenInfo.metadata.bonusTiers : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-500\",\n                                                    children: \"Not set\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                    lineNumber: 1134,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1131,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1129,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-500\",\n                                                children: \"Token Details\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1139,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"font-medium\",\n                                                children: ((_tokenInfo_metadata2 = tokenInfo.metadata) === null || _tokenInfo_metadata2 === void 0 ? void 0 : _tokenInfo_metadata2.tokenDetails) && tokenInfo.metadata.tokenDetails.trim() !== '' ? tokenInfo.metadata.tokenDetails : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-500\",\n                                                    children: \"Not set\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                    lineNumber: 1143,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1140,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1138,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-500\",\n                                                children: \"Version\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1148,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"font-medium\",\n                                                children: tokenInfo.version\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1149,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1147,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 1119,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4 p-3 bg-gray-50 rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-gray-600\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Debug Info:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                            lineNumber: 1156,\n                                            columnNumber: 19\n                                        }, this),\n                                        \" Check browser console for detailed metadata values from contract\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                    lineNumber: 1155,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 1154,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                        lineNumber: 1090,\n                        columnNumber: 13\n                    }, this),\n                    contractAddresses && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold mb-4\",\n                                children: \"Contract Addresses\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 1165,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: Object.entries(contractAddresses).map((param)=>{\n                                    let [name, address] = param;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center p-3 bg-gray-50 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-medium text-gray-900\",\n                                                        children: name\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                        lineNumber: 1170,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-500 font-mono\",\n                                                        children: address\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                        lineNumber: 1171,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1169,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>navigator.clipboard.writeText(address),\n                                                        className: \"text-blue-600 hover:text-blue-800 text-sm\",\n                                                        title: \"Copy address\",\n                                                        children: \"\\uD83D\\uDCCB Copy\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                        lineNumber: 1174,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"https://amoy.polygonscan.com/address/\".concat(address),\n                                                        target: \"_blank\",\n                                                        rel: \"noopener noreferrer\",\n                                                        className: \"text-green-600 hover:text-green-800 text-sm\",\n                                                        title: \"View on Polygonscan\",\n                                                        children: \"\\uD83D\\uDD17 View\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                        lineNumber: 1181,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1173,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, name, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1168,\n                                        columnNumber: 19\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 1166,\n                                columnNumber: 15\n                            }, this),\n                            Object.keys(contractAddresses).length === 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-yellow-800\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Note:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                            lineNumber: 1198,\n                                            columnNumber: 21\n                                        }, this),\n                                        \" Only the main token contract is registered. Other modules (Identity Manager, Compliance Engine, etc.) may not be deployed or registered yet.\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                    lineNumber: 1197,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 1196,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                        lineNumber: 1164,\n                        columnNumber: 13\n                    }, this),\n                    userRoles && signer && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold mb-4\",\n                                children: \"Your Permissions\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 1209,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                children: Object.entries(userRoles).map((param)=>{\n                                    let [role, hasRole] = param;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center p-3 bg-gray-50 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-medium text-gray-900\",\n                                                        children: role.replace(/_/g, ' ')\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                        lineNumber: 1214,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: [\n                                                            role === 'DEFAULT_ADMIN_ROLE' && 'Can update metadata, max supply, and manage roles',\n                                                            role === 'AGENT_ROLE' && 'Can mint tokens and manage whitelist',\n                                                            role === 'TRANSFER_MANAGER_ROLE' && 'Can force transfers and manage transfer restrictions',\n                                                            role === 'MODULE_MANAGER_ROLE' && 'Can register and manage modules'\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                        lineNumber: 1215,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1213,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"px-3 py-1 rounded-full text-sm font-medium \".concat(hasRole ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'),\n                                                children: hasRole ? '✅ Granted' : '❌ Denied'\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1222,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, role, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1212,\n                                        columnNumber: 19\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 1210,\n                                columnNumber: 15\n                            }, this),\n                            !userRoles.DEFAULT_ADMIN_ROLE && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-yellow-800\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"⚠️ Limited Access:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                            lineNumber: 1236,\n                                            columnNumber: 21\n                                        }, this),\n                                        \" You don't have admin role on this token. Some functions like updating price, bonus tiers, and max supply will fail. Contact the token admin to grant you the necessary roles.\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                    lineNumber: 1235,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 1234,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4 p-4 bg-green-50 border border-green-200 rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-green-800\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"✅ Contract Status:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                            lineNumber: 1245,\n                                            columnNumber: 19\n                                        }, this),\n                                        \" This token has been upgraded to the latest version. All update functions are now available and working properly.\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                    lineNumber: 1244,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 1243,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                        lineNumber: 1208,\n                        columnNumber: 13\n                    }, this),\n                    upgradeInfo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold mb-4\",\n                                children: \"Upgrade System Status\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 1255,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-500\",\n                                                children: \"Emergency Mode\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1258,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-lg font-bold \".concat(upgradeInfo.emergencyModeActive ? 'text-red-600' : 'text-green-600'),\n                                                children: upgradeInfo.emergencyModeActive ? 'ACTIVE' : 'Inactive'\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1259,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1257,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-500\",\n                                                children: \"Upgrade Delay\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1264,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-lg font-bold text-blue-600\",\n                                                children: [\n                                                    Math.floor(upgradeInfo.upgradeDelay / 3600),\n                                                    \" hours\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1265,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1263,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 1256,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-500\",\n                                        children: \"Registered Modules\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1272,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-2 space-y-1\",\n                                        children: upgradeInfo.registeredModules.map((module, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm font-mono bg-gray-100 p-2 rounded\",\n                                                children: formatAddress(module)\n                                            }, index, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1275,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1273,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 1271,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                        lineNumber: 1254,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                lineNumber: 1087,\n                columnNumber: 9\n            }, this),\n            activeTab === 'mint' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold mb-6\",\n                        children: \"Mint Tokens\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                        lineNumber: 1289,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                children: \"Recipient Address\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1294,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: mintAddress,\n                                                onChange: (e)=>setMintAddress(e.target.value),\n                                                placeholder: \"0x...\",\n                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1297,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1293,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                children: \"Amount\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1306,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"number\",\n                                                value: mintAmount,\n                                                onChange: (e)=>setMintAmount(e.target.value),\n                                                placeholder: \"100\",\n                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1309,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1305,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 1292,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleMint,\n                                        disabled: actionLoading || !mintAddress || !mintAmount,\n                                        className: \"bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-bold py-2 px-4 rounded\",\n                                        children: actionLoading ? 'Processing...' : 'Mint (Wallet)'\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1320,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleMintAPI,\n                                        disabled: actionLoading || !mintAddress || !mintAmount,\n                                        className: \"bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white font-bold py-2 px-4 rounded\",\n                                        children: actionLoading ? 'Processing...' : 'Mint (API)'\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1327,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 1319,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Wallet Method:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1337,\n                                                columnNumber: 18\n                                            }, this),\n                                            \" Uses your connected MetaMask wallet to sign the transaction.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1337,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"API Method:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1338,\n                                                columnNumber: 18\n                                            }, this),\n                                            \" Uses the server's private key to execute the transaction.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1338,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 1336,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                        lineNumber: 1291,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                lineNumber: 1288,\n                columnNumber: 9\n            }, this),\n            activeTab === 'pause' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold mb-6\",\n                        children: \"Pause Control\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                        lineNumber: 1347,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-50 p-4 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-medium text-gray-900 mb-2\",\n                                        children: \"Current Status\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1351,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-lg font-bold \".concat((tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.paused) ? 'text-red-600' : 'text-green-600'),\n                                        children: (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.paused) ? '⏸️ PAUSED' : '▶️ ACTIVE'\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1352,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 1350,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handlePauseToggle,\n                                        disabled: actionLoading,\n                                        className: \"\".concat((tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.paused) ? 'bg-green-600 hover:bg-green-700' : 'bg-red-600 hover:bg-red-700', \" disabled:bg-gray-400 text-white font-bold py-2 px-4 rounded\"),\n                                        children: actionLoading ? 'Processing...' : (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.paused) ? 'Unpause (Wallet)' : 'Pause (Wallet)'\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1358,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handlePauseToggleAPI,\n                                        disabled: actionLoading,\n                                        className: \"\".concat((tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.paused) ? 'bg-green-600 hover:bg-green-700' : 'bg-red-600 hover:bg-red-700', \" disabled:bg-gray-400 text-white font-bold py-2 px-4 rounded\"),\n                                        children: actionLoading ? 'Processing...' : (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.paused) ? 'Unpause (API)' : 'Pause (API)'\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1369,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 1357,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Pause:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1383,\n                                                columnNumber: 18\n                                            }, this),\n                                            \" Stops all token transfers and minting operations.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1383,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Unpause:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1384,\n                                                columnNumber: 18\n                                            }, this),\n                                            \" Resumes normal token operations.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1384,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 1382,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                        lineNumber: 1349,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                lineNumber: 1346,\n                columnNumber: 9\n            }, this),\n            activeTab === 'admin' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold mb-6\",\n                        children: \"Admin Controls\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                        lineNumber: 1393,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-b border-gray-200 pb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 mb-4\",\n                                        children: \"Update Token Price\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1398,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: newTokenPrice,\n                                                onChange: (e)=>setNewTokenPrice(e.target.value),\n                                                placeholder: \"Enter new price (e.g., $1.50)\",\n                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1400,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleUpdatePrice,\n                                                        disabled: actionLoading || !newTokenPrice || !(userRoles === null || userRoles === void 0 ? void 0 : userRoles.DEFAULT_ADMIN_ROLE),\n                                                        className: \"bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-bold py-2 px-4 rounded\",\n                                                        title: !(userRoles === null || userRoles === void 0 ? void 0 : userRoles.DEFAULT_ADMIN_ROLE) ? 'Requires DEFAULT_ADMIN_ROLE' : '',\n                                                        children: actionLoading ? 'Updating...' : 'Update (Wallet)'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                        lineNumber: 1408,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleUpdatePriceAPI,\n                                                        disabled: actionLoading || !newTokenPrice,\n                                                        className: \"bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white font-bold py-2 px-4 rounded\",\n                                                        children: actionLoading ? 'Updating...' : 'Update (API)'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                        lineNumber: 1416,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1407,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1399,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600 mt-2\",\n                                        children: [\n                                            \"Current: \",\n                                            (tokenInfo === null || tokenInfo === void 0 ? void 0 : (_tokenInfo_metadata3 = tokenInfo.metadata) === null || _tokenInfo_metadata3 === void 0 ? void 0 : _tokenInfo_metadata3.tokenPrice) || 'Not set'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1425,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 1397,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-b border-gray-200 pb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 mb-4\",\n                                        children: \"Update Bonus Tiers\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1432,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: newBonusTiers,\n                                                onChange: (e)=>setNewBonusTiers(e.target.value),\n                                                placeholder: \"Enter bonus tiers (e.g., 5%,10%,15%)\",\n                                                className: \"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1434,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleUpdateBonusTiers,\n                                                disabled: actionLoading || !newBonusTiers || !(userRoles === null || userRoles === void 0 ? void 0 : userRoles.DEFAULT_ADMIN_ROLE),\n                                                className: \"bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-bold py-2 px-4 rounded\",\n                                                title: !(userRoles === null || userRoles === void 0 ? void 0 : userRoles.DEFAULT_ADMIN_ROLE) ? 'Requires DEFAULT_ADMIN_ROLE' : '',\n                                                children: actionLoading ? 'Updating...' : 'Update Tiers'\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1441,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1433,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600 mt-2\",\n                                        children: [\n                                            \"Current: \",\n                                            (tokenInfo === null || tokenInfo === void 0 ? void 0 : (_tokenInfo_metadata4 = tokenInfo.metadata) === null || _tokenInfo_metadata4 === void 0 ? void 0 : _tokenInfo_metadata4.bonusTiers) || 'Not set'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1450,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 1431,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-b border-gray-200 pb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 mb-4\",\n                                        children: \"Update Max Supply\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1457,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"number\",\n                                                value: newMaxSupply,\n                                                onChange: (e)=>setNewMaxSupply(e.target.value),\n                                                placeholder: \"Enter new max supply\",\n                                                className: \"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1459,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleUpdateMaxSupply,\n                                                disabled: actionLoading || !newMaxSupply || !(userRoles === null || userRoles === void 0 ? void 0 : userRoles.DEFAULT_ADMIN_ROLE),\n                                                className: \"bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-bold py-2 px-4 rounded\",\n                                                title: !(userRoles === null || userRoles === void 0 ? void 0 : userRoles.DEFAULT_ADMIN_ROLE) ? 'Requires DEFAULT_ADMIN_ROLE' : '',\n                                                children: actionLoading ? 'Updating...' : 'Update Max Supply'\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1466,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1458,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600 mt-2\",\n                                        children: [\n                                            \"Current: \",\n                                            (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.maxSupply) || 'Not set'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1475,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 1456,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 mb-4\",\n                                        children: \"Whitelist Management\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1482,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: whitelistAddress,\n                                                onChange: (e)=>setWhitelistAddress(e.target.value),\n                                                placeholder: \"Enter wallet address\",\n                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1484,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleAddToWhitelistAdmin,\n                                                        disabled: actionLoading || !whitelistAddress,\n                                                        className: \"bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white font-bold py-2 px-4 rounded\",\n                                                        children: actionLoading ? 'Processing...' : 'Add (Wallet)'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                        lineNumber: 1492,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleRemoveFromWhitelist,\n                                                        disabled: actionLoading || !whitelistAddress,\n                                                        className: \"bg-red-600 hover:bg-red-700 disabled:bg-gray-400 text-white font-bold py-2 px-4 rounded\",\n                                                        children: actionLoading ? 'Processing...' : 'Remove (Wallet)'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                        lineNumber: 1499,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleWhitelistAPI('add'),\n                                                        disabled: actionLoading || !whitelistAddress,\n                                                        className: \"bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white font-bold py-2 px-4 rounded\",\n                                                        children: actionLoading ? 'Processing...' : 'Add (API)'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                        lineNumber: 1506,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleWhitelistAPI('remove'),\n                                                        disabled: actionLoading || !whitelistAddress,\n                                                        className: \"bg-red-600 hover:bg-red-700 disabled:bg-gray-400 text-white font-bold py-2 px-4 rounded\",\n                                                        children: actionLoading ? 'Processing...' : 'Remove (API)'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                        lineNumber: 1513,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1491,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-600 mt-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"⚠️ Important:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                lineNumber: 1522,\n                                                                columnNumber: 22\n                                                            }, this),\n                                                            \" Addresses must be registered with the identity registry before whitelisting.\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                        lineNumber: 1522,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Wallet Method:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                lineNumber: 1523,\n                                                                columnNumber: 22\n                                                            }, this),\n                                                            \" Requires manual identity registration first.\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                        lineNumber: 1523,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"API Method (Recommended):\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                lineNumber: 1524,\n                                                                columnNumber: 22\n                                                            }, this),\n                                                            \" Automatically handles identity registration if needed.\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                        lineNumber: 1524,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1521,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1483,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 1481,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                        lineNumber: 1395,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                lineNumber: 1392,\n                columnNumber: 9\n            }, this),\n            activeTab === 'whitelist' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold\",\n                                children: \"Whitelist Management\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 1536,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: fetchWhitelistedAddresses,\n                                disabled: loadingWhitelist,\n                                className: \"bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-bold py-2 px-4 rounded\",\n                                children: loadingWhitelist ? 'Loading...' : 'Refresh List'\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 1537,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                        lineNumber: 1535,\n                        columnNumber: 11\n                    }, this),\n                    loadingWhitelist ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center items-center h-32\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                            lineNumber: 1548,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                        lineNumber: 1547,\n                        columnNumber: 13\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-5 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-blue-50 p-4 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-blue-600\",\n                                                children: \"Total Whitelisted\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1555,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold text-blue-800\",\n                                                children: whitelistedAddresses.length\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1556,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1554,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-green-50 p-4 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-green-600\",\n                                                children: \"Active Holders\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1559,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold text-green-800\",\n                                                children: whitelistedAddresses.filter((addr)=>parseFloat(addr.balance) > 0).length\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1560,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1558,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-yellow-50 p-4 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-yellow-600\",\n                                                children: \"Frozen Addresses\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1565,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold text-yellow-800\",\n                                                children: whitelistedAddresses.filter((addr)=>addr.isFrozen).length\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1566,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1564,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-purple-50 p-4 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-purple-600\",\n                                                children: \"Active Balance\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1571,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold text-purple-800\",\n                                                children: whitelistedAddresses.reduce((sum, addr)=>sum + parseFloat(addr.balance), 0).toFixed(2)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1572,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-purple-600\",\n                                                children: \"Circulating tokens\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1575,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1570,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-orange-50 p-4 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-orange-600\",\n                                                children: \"Frozen Balance\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1578,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold text-orange-800\",\n                                                children: whitelistedAddresses.reduce((sum, addr)=>sum + parseFloat(addr.frozenTokens || '0'), 0).toFixed(2)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1579,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-orange-600\",\n                                                children: \"Vault tracking\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1582,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>{\n                                                    if (!tokenAddress) return;\n                                                    // Seed localStorage with the known 7 frozen tokens\n                                                    const localStorageKey = \"frozen_tokens_\".concat(tokenAddress);\n                                                    const frozenData = {\n                                                        '******************************************': 7\n                                                    };\n                                                    localStorage.setItem(localStorageKey, JSON.stringify(frozenData));\n                                                    alert('Historical freeze data seeded in localStorage! Refresh the page.');\n                                                },\n                                                className: \"mt-2 px-2 py-1 bg-orange-600 text-white text-xs rounded hover:bg-orange-700\",\n                                                children: \"Seed Historical Data\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1583,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1577,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 1553,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"overflow-x-auto\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                    className: \"min-w-full divide-y divide-gray-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                            className: \"bg-gray-50\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"Address\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                        lineNumber: 1606,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"Balance\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                        lineNumber: 1609,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"Frozen\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                        lineNumber: 1612,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"Status\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                        lineNumber: 1615,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"Actions\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                        lineNumber: 1618,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1605,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                            lineNumber: 1604,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                            className: \"bg-white divide-y divide-gray-200\",\n                                            children: whitelistedAddresses.map((addr, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: index % 2 === 0 ? 'bg-white' : 'bg-gray-50',\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm font-mono text-gray-900\",\n                                                                    children: formatAddress(addr.address)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                    lineNumber: 1627,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-gray-500\",\n                                                                    children: addr.address\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                    lineNumber: 1630,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                            lineNumber: 1626,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-900\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"font-medium\",\n                                                                        children: [\n                                                                            \"Active: \",\n                                                                            addr.balance\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                        lineNumber: 1636,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-xs text-orange-600\",\n                                                                        children: [\n                                                                            \"Frozen: \",\n                                                                            addr.frozenTokens || '0'\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                        lineNumber: 1637,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-xs text-gray-500\",\n                                                                        children: [\n                                                                            \"Total: \",\n                                                                            (parseFloat(addr.balance) + parseFloat(addr.frozenTokens || '0')).toFixed(2)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                        lineNumber: 1640,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                lineNumber: 1635,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                            lineNumber: 1634,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full \".concat(addr.isFrozen ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'),\n                                                                children: addr.isFrozen ? 'Frozen' : 'Active'\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                lineNumber: 1646,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                            lineNumber: 1645,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex space-x-1\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full \".concat(addr.isVerified ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'),\n                                                                    children: addr.isVerified ? 'Verified' : 'Unverified'\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                    lineNumber: 1654,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                lineNumber: 1653,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                            lineNumber: 1652,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap text-sm font-medium\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex space-x-2\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>setSelectedAddress(addr.address),\n                                                                    className: \"text-blue-600 hover:text-blue-900\",\n                                                                    children: \"Manage\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                    lineNumber: 1663,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                lineNumber: 1662,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                            lineNumber: 1661,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, addr.address, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                    lineNumber: 1625,\n                                                    columnNumber: 23\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                            lineNumber: 1623,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                    lineNumber: 1603,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 1602,\n                                columnNumber: 15\n                            }, this),\n                            whitelistedAddresses.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8 text-gray-500\",\n                                children: \"No whitelisted addresses found. Add addresses using the Admin Controls tab.\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 1678,\n                                columnNumber: 17\n                            }, this),\n                            selectedAddress && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-medium text-gray-900\",\n                                                        children: [\n                                                            \"Manage Address: \",\n                                                            formatAddress(selectedAddress)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                        lineNumber: 1689,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setSelectedAddress(''),\n                                                        className: \"text-gray-400 hover:text-gray-600\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-2xl\",\n                                                            children: \"\\xd7\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                            lineNumber: 1696,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                        lineNumber: 1692,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1688,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-6\",\n                                                children: [\n                                                    (()=>{\n                                                        const addressData = whitelistedAddresses.find((addr)=>addr.address === selectedAddress);\n                                                        return addressData ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-gray-50 p-4 rounded-lg\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"font-medium mb-2\",\n                                                                    children: \"Current Status\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                    lineNumber: 1706,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"grid grid-cols-2 gap-4 text-sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                \"Balance: \",\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"font-medium\",\n                                                                                    children: addressData.balance\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                                    lineNumber: 1708,\n                                                                                    columnNumber: 47\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                            lineNumber: 1708,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                \"Frozen: \",\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"font-medium\",\n                                                                                    children: addressData.frozenTokens\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                                    lineNumber: 1709,\n                                                                                    columnNumber: 46\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                            lineNumber: 1709,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                \"Status: \",\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"font-medium \".concat(addressData.isFrozen ? 'text-red-600' : 'text-green-600'),\n                                                                                    children: addressData.isFrozen ? 'Frozen' : 'Active'\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                                    lineNumber: 1710,\n                                                                                    columnNumber: 46\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                            lineNumber: 1710,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                \"Verified: \",\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"font-medium \".concat(addressData.isVerified ? 'text-green-600' : 'text-gray-600'),\n                                                                                    children: addressData.isVerified ? 'Yes' : 'No'\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                                    lineNumber: 1713,\n                                                                                    columnNumber: 48\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                            lineNumber: 1713,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                    lineNumber: 1707,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                            lineNumber: 1705,\n                                                            columnNumber: 29\n                                                        }, this) : null;\n                                                    })(),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"border-b border-gray-200 pb-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-medium mb-3\",\n                                                                children: \"Mint Tokens\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                lineNumber: 1723,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex space-x-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"number\",\n                                                                        value: operationAmount,\n                                                                        onChange: (e)=>setOperationAmount(e.target.value),\n                                                                        placeholder: \"Amount to mint\",\n                                                                        className: \"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                        lineNumber: 1725,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>handleMintToAddress(selectedAddress, operationAmount),\n                                                                        disabled: actionLoading || !operationAmount,\n                                                                        className: \"bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white font-bold py-2 px-4 rounded\",\n                                                                        children: actionLoading ? 'Minting...' : 'Mint'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                        lineNumber: 1732,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                lineNumber: 1724,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                        lineNumber: 1722,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"border-b border-gray-200 pb-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-medium mb-3\",\n                                                                children: \"Freeze/Unfreeze Tokens\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                lineNumber: 1744,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex space-x-3\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"number\",\n                                                                                value: operationAmount,\n                                                                                onChange: (e)=>setOperationAmount(e.target.value),\n                                                                                placeholder: \"Amount to freeze/unfreeze\",\n                                                                                className: \"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                                lineNumber: 1747,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: ()=>handleFreezeTokens(selectedAddress, operationAmount),\n                                                                                disabled: actionLoading || !operationAmount,\n                                                                                className: \"bg-yellow-600 hover:bg-yellow-700 disabled:bg-gray-400 text-white font-bold py-2 px-4 rounded\",\n                                                                                children: actionLoading ? 'Processing...' : 'Freeze'\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                                lineNumber: 1754,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: ()=>handleUnfreezeTokens(selectedAddress, operationAmount),\n                                                                                disabled: actionLoading || !operationAmount,\n                                                                                className: \"bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white font-bold py-2 px-4 rounded\",\n                                                                                children: actionLoading ? 'Processing...' : 'Unfreeze'\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                                lineNumber: 1761,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                        lineNumber: 1746,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-gray-600\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                        children: \"Freeze:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                                        lineNumber: 1770,\n                                                                                        columnNumber: 34\n                                                                                    }, this),\n                                                                                    \" Mints tracking tokens to vault (user keeps original tokens, vault tracks frozen amount).\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                                lineNumber: 1770,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                        children: \"Unfreeze:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                                        lineNumber: 1771,\n                                                                                        columnNumber: 34\n                                                                                    }, this),\n                                                                                    \" Mints additional tokens to user (compensates for frozen amount).\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                                lineNumber: 1771,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                        children: \"Vault Balance:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                                        lineNumber: 1772,\n                                                                                        columnNumber: 34\n                                                                                    }, this),\n                                                                                    \" \",\n                                                                                    vaultBalance,\n                                                                                    \" tokens (represents total frozen amounts)\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                                lineNumber: 1772,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                        children: \"Note:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                                        lineNumber: 1773,\n                                                                                        columnNumber: 34\n                                                                                    }, this),\n                                                                                    \" This is a simplified freeze system that tracks frozen amounts without removing user tokens.\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                                lineNumber: 1773,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                        lineNumber: 1769,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                lineNumber: 1745,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                        lineNumber: 1743,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-medium mb-3\",\n                                                                children: \"Admin Mint to Address\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                lineNumber: 1780,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"text\",\n                                                                        value: transferToAddress,\n                                                                        onChange: (e)=>setTransferToAddress(e.target.value),\n                                                                        placeholder: \"Mint to address (0x...)\",\n                                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                        lineNumber: 1782,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex space-x-3\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"number\",\n                                                                                value: operationAmount,\n                                                                                onChange: (e)=>setOperationAmount(e.target.value),\n                                                                                placeholder: \"Amount to mint\",\n                                                                                className: \"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                                lineNumber: 1790,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: ()=>handleForceTransfer(selectedAddress, transferToAddress, operationAmount),\n                                                                                disabled: actionLoading || !transferToAddress || !operationAmount,\n                                                                                className: \"bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-bold py-2 px-4 rounded\",\n                                                                                children: actionLoading ? 'Minting...' : 'Admin Mint'\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                                lineNumber: 1797,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                        lineNumber: 1789,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-gray-600\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                    children: \"Note:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                                    lineNumber: 1806,\n                                                                                    columnNumber: 34\n                                                                                }, this),\n                                                                                \" This mints new tokens to the specified address (admin operation).\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                            lineNumber: 1806,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                        lineNumber: 1805,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                lineNumber: 1781,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                        lineNumber: 1779,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-end pt-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setSelectedAddress(''),\n                                                            className: \"bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded\",\n                                                            children: \"Close\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                            lineNumber: 1813,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                        lineNumber: 1812,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1700,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1687,\n                                        columnNumber: 21\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                    lineNumber: 1686,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 1685,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                        lineNumber: 1551,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                lineNumber: 1534,\n                columnNumber: 9\n            }, this),\n            activeTab === 'kyc' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold mb-6\",\n                        children: \"KYC & Claims Management\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                        lineNumber: 1833,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-b border-gray-200 pb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 mb-4\",\n                                        children: \"KYC Management\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1838,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: kycUserAddress,\n                                                onChange: (e)=>setKycUserAddress(e.target.value),\n                                                placeholder: \"Enter user wallet address\",\n                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1840,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleApproveKYC,\n                                                        disabled: actionLoading || !kycUserAddress,\n                                                        className: \"bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white font-bold py-2 px-4 rounded\",\n                                                        children: actionLoading ? 'Processing...' : 'Approve KYC'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                        lineNumber: 1848,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleAddToWhitelistKYC,\n                                                        disabled: actionLoading || !kycUserAddress,\n                                                        className: \"bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-bold py-2 px-4 rounded\",\n                                                        children: actionLoading ? 'Processing...' : 'Add to Whitelist'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                        lineNumber: 1855,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1847,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1839,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 1837,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-b border-gray-200 pb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 mb-4\",\n                                        children: \"Issue Claims\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1868,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: claimUserAddress,\n                                                        onChange: (e)=>setClaimUserAddress(e.target.value),\n                                                        placeholder: \"User wallet address\",\n                                                        className: \"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                        lineNumber: 1871,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: claimTopicId,\n                                                        onChange: (e)=>setClaimTopicId(e.target.value),\n                                                        placeholder: \"Topic ID (e.g., 10101010000001)\",\n                                                        className: \"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                        lineNumber: 1878,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1870,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                value: claimData,\n                                                onChange: (e)=>setClaimData(e.target.value),\n                                                placeholder: \"Claim data (optional)\",\n                                                rows: 3,\n                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1886,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleIssueClaim,\n                                                disabled: actionLoading || !claimUserAddress || !claimTopicId,\n                                                className: \"bg-purple-600 hover:bg-purple-700 disabled:bg-gray-400 text-white font-bold py-2 px-4 rounded\",\n                                                children: actionLoading ? 'Processing...' : 'Issue Claim'\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1893,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1869,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 1867,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 mb-4\",\n                                        children: \"Check User Status\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1905,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: checkUserAddress,\n                                                        onChange: (e)=>setCheckUserAddress(e.target.value),\n                                                        placeholder: \"Enter user wallet address\",\n                                                        className: \"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                        lineNumber: 1908,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleCheckStatus,\n                                                        disabled: actionLoading || !checkUserAddress,\n                                                        className: \"bg-indigo-600 hover:bg-indigo-700 disabled:bg-gray-400 text-white font-bold py-2 px-4 rounded\",\n                                                        children: actionLoading ? 'Checking...' : 'Check Status'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                        lineNumber: 1915,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1907,\n                                                columnNumber: 17\n                                            }, this),\n                                            verificationStatus && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-50 p-4 rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-gray-900 mb-2\",\n                                                        children: \"Verification Status\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                        lineNumber: 1926,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                        className: \"text-sm text-gray-700 whitespace-pre-wrap\",\n                                                        children: JSON.stringify(verificationStatus, null, 2)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                        lineNumber: 1927,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1925,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1906,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 1904,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                        lineNumber: 1835,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                lineNumber: 1832,\n                columnNumber: 9\n            }, this),\n            activeTab === 'upgrades' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold mb-6\",\n                        children: \"Upgrade Management\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                        lineNumber: 1941,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-b border-gray-200 pb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 mb-4\",\n                                        children: \"Emergency Mode\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1946,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"px-3 py-2 rounded-lg \".concat((upgradeInfo === null || upgradeInfo === void 0 ? void 0 : upgradeInfo.emergencyModeActive) ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'),\n                                                children: (upgradeInfo === null || upgradeInfo === void 0 ? void 0 : upgradeInfo.emergencyModeActive) ? '🚨 EMERGENCY MODE ACTIVE' : '✅ Normal Operation'\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1948,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleEmergencyModeToggle,\n                                                disabled: actionLoading,\n                                                className: \"\".concat((upgradeInfo === null || upgradeInfo === void 0 ? void 0 : upgradeInfo.emergencyModeActive) ? 'bg-green-600 hover:bg-green-700' : 'bg-red-600 hover:bg-red-700', \" disabled:bg-gray-400 text-white font-bold py-2 px-4 rounded\"),\n                                                children: actionLoading ? 'Processing...' : (upgradeInfo === null || upgradeInfo === void 0 ? void 0 : upgradeInfo.emergencyModeActive) ? 'Deactivate Emergency' : 'Activate Emergency'\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1951,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1947,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 1945,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-b border-gray-200 pb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 mb-4\",\n                                        children: \"Schedule Upgrade\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1967,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: newImplementationAddress,\n                                                onChange: (e)=>setNewImplementationAddress(e.target.value),\n                                                placeholder: \"New implementation contract address\",\n                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1969,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                value: upgradeDescription,\n                                                onChange: (e)=>setUpgradeDescription(e.target.value),\n                                                placeholder: \"Upgrade description\",\n                                                rows: 3,\n                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1976,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleScheduleUpgrade,\n                                                disabled: actionLoading || !newImplementationAddress || !upgradeDescription,\n                                                className: \"bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-bold py-2 px-4 rounded\",\n                                                children: actionLoading ? 'Scheduling...' : 'Schedule Upgrade'\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 1983,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1968,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 1966,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 mb-4\",\n                                        children: \"Pending Upgrades\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1995,\n                                        columnNumber: 15\n                                    }, this),\n                                    pendingUpgrades.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-500\",\n                                        children: \"No pending upgrades\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1997,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: pendingUpgrades.map((upgrade, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border border-gray-200 rounded-lg p-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-start\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"font-medium text-gray-900\",\n                                                                    children: [\n                                                                        \"Upgrade #\",\n                                                                        upgrade.upgradeId\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                    lineNumber: 2004,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-600 mt-1\",\n                                                                    children: upgrade.description\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                    lineNumber: 2005,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mt-2 space-y-1 text-sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                    children: \"New Implementation:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                                    lineNumber: 2007,\n                                                                                    columnNumber: 32\n                                                                                }, this),\n                                                                                \" \",\n                                                                                formatAddress(upgrade.newImplementation)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                            lineNumber: 2007,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                    children: \"Execute Time:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                                    lineNumber: 2008,\n                                                                                    columnNumber: 32\n                                                                                }, this),\n                                                                                \" \",\n                                                                                formatTimestamp(upgrade.executeTime)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                            lineNumber: 2008,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                    children: \"Status:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                                    lineNumber: 2009,\n                                                                                    columnNumber: 32\n                                                                                }, this),\n                                                                                \" \",\n                                                                                getTimeUntilExecution(upgrade.executeTime)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                            lineNumber: 2009,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                    lineNumber: 2006,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                            lineNumber: 2003,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"ml-4\",\n                                                            children: upgrade.executeTime <= Math.floor(Date.now() / 1000) && !upgrade.executed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                className: \"bg-green-600 hover:bg-green-700 text-white font-bold py-1 px-3 rounded text-sm\",\n                                                                disabled: actionLoading,\n                                                                children: \"Execute\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                                lineNumber: 2014,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                            lineNumber: 2012,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                    lineNumber: 2002,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, index, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                                lineNumber: 2001,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                        lineNumber: 1999,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                                lineNumber: 1994,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                        lineNumber: 1943,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n                lineNumber: 1940,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\[address]\\\\page.tsx\",\n        lineNumber: 1001,\n        columnNumber: 5\n    }, this);\n}\n_s(ModularTokenDetailsPage, \"bOA7qjt0fHfGIrgvtUWcNUEgOV0=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        _hooks_useModularToken__WEBPACK_IMPORTED_MODULE_3__.useModularToken\n    ];\n});\n_c = ModularTokenDetailsPage;\nvar _c;\n$RefreshReg$(_c, \"ModularTokenDetailsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/modular-tokens/[address]/page.tsx\n"));

/***/ })

});