"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/modular-tokens/[address]/page",{

/***/ "(app-pages-browser)/./src/hooks/useModularToken.ts":
/*!**************************************!*\
  !*** ./src/hooks/useModularToken.ts ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useModularToken: () => (/* binding */ useModularToken)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/providers/provider-browser.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/contract/contract.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/utils/units.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/crypto/keccak.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/utils/utf8.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/constants/addresses.js\");\n/* harmony import */ var _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/contracts/SecurityTokenCore.json */ \"(app-pages-browser)/./src/contracts/SecurityTokenCore.json\");\n/* harmony import */ var _contracts_UpgradeManager_json__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contracts/UpgradeManager.json */ \"(app-pages-browser)/./src/contracts/UpgradeManager.json\");\n/* __next_internal_client_entry_do_not_use__ useModularToken auto */ \n\n// Import ABIs - extract the abi property from the JSON artifacts\n\n\n// Extract ABIs from artifacts\nconst SecurityTokenCoreABI = _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_1__.abi;\nconst UpgradeManagerABI = _contracts_UpgradeManager_json__WEBPACK_IMPORTED_MODULE_2__.abi;\n// Contract addresses from environment\nconst SECURITY_TOKEN_CORE_ADDRESS = \"******************************************\";\nconst UPGRADE_MANAGER_ADDRESS = \"0xb7a53dC340C2E5e823002B174629e2a44B8ed815\";\nfunction useModularToken(tokenAddress) {\n    const [provider, setProvider] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [signer, setSigner] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [tokenInfo, setTokenInfo] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [contractAddresses, setContractAddresses] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [userRoles, setUserRoles] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [upgradeInfo, setUpgradeInfo] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [pendingUpgrades, setPendingUpgrades] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [upgradeHistory, setUpgradeHistory] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    // Initialize provider and signer\n    const initializeProvider = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[initializeProvider]\": async ()=>{\n            try {\n                if ( true && window.ethereum) {\n                    const provider = new ethers__WEBPACK_IMPORTED_MODULE_3__.BrowserProvider(window.ethereum);\n                    await provider.send('eth_requestAccounts', []);\n                    const signer = await provider.getSigner();\n                    setProvider(provider);\n                    setSigner(signer);\n                    return {\n                        provider,\n                        signer\n                    };\n                } else {\n                    throw new Error('MetaMask not found');\n                }\n            } catch (error) {\n                console.error('Error initializing provider:', error);\n                setError('Failed to connect to wallet');\n                return null;\n            }\n        }\n    }[\"useModularToken.useCallback[initializeProvider]\"], []);\n    // Load token information\n    const loadTokenInfo = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[loadTokenInfo]\": async ()=>{\n            const contractAddress = tokenAddress || SECURITY_TOKEN_CORE_ADDRESS;\n            if (!provider || !contractAddress) return;\n            try {\n                setLoading(true);\n                const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(contractAddress, SecurityTokenCoreABI, provider);\n                const [name, symbol, version, totalSupply, maxSupply, decimals, paused, metadata] = await Promise.all([\n                    contract.name(),\n                    contract.symbol(),\n                    contract.version(),\n                    contract.totalSupply(),\n                    contract.maxSupply(),\n                    contract.decimals(),\n                    contract.paused(),\n                    contract.getTokenMetadata()\n                ]);\n                console.log('Token contract data:');\n                console.log('- Name:', name);\n                console.log('- Symbol:', symbol);\n                console.log('- Decimals (raw):', decimals);\n                console.log('- Decimals (number):', Number(decimals));\n                console.log('- Total Supply (raw):', totalSupply.toString());\n                console.log('- Max Supply (raw):', maxSupply.toString());\n                console.log('- Metadata (raw):', metadata);\n                console.log('- Token Price:', metadata[0]);\n                console.log('- Bonus Tiers:', metadata[1]);\n                console.log('- Token Details:', metadata[2]);\n                console.log('- Token Image URL:', metadata[3]);\n                const decimalsNumber = Number(decimals);\n                // Format with proper blockchain decimal precision\n                let formattedTotalSupply;\n                let formattedMaxSupply;\n                if (decimalsNumber === 0) {\n                    // For 0 decimals, show as whole numbers\n                    formattedTotalSupply = totalSupply.toString();\n                    formattedMaxSupply = maxSupply.toString();\n                } else {\n                    // For tokens with decimals, use ethers.formatUnits and preserve decimal precision\n                    const totalSupplyFormatted = ethers__WEBPACK_IMPORTED_MODULE_5__.formatUnits(totalSupply, decimalsNumber);\n                    const maxSupplyFormatted = ethers__WEBPACK_IMPORTED_MODULE_5__.formatUnits(maxSupply, decimalsNumber);\n                    // For blockchain tokens, show with appropriate decimal places\n                    // Remove excessive trailing zeros but keep meaningful precision\n                    formattedTotalSupply = parseFloat(totalSupplyFormatted).toFixed(Math.min(decimalsNumber, 6));\n                    formattedMaxSupply = parseFloat(maxSupplyFormatted).toFixed(Math.min(decimalsNumber, 6));\n                    // Remove trailing zeros after decimal point\n                    formattedTotalSupply = formattedTotalSupply.replace(/\\.?0+$/, '');\n                    formattedMaxSupply = formattedMaxSupply.replace(/\\.?0+$/, '');\n                    // Ensure at least .0 for tokens with decimals\n                    if (!formattedTotalSupply.includes('.')) formattedTotalSupply += '.0';\n                    if (!formattedMaxSupply.includes('.')) formattedMaxSupply += '.0';\n                }\n                console.log('- Total Supply (formatted):', formattedTotalSupply);\n                console.log('- Max Supply (formatted):', formattedMaxSupply);\n                setTokenInfo({\n                    name,\n                    symbol,\n                    version,\n                    totalSupply: formattedTotalSupply,\n                    maxSupply: formattedMaxSupply,\n                    decimals: decimalsNumber,\n                    paused,\n                    metadata: {\n                        tokenPrice: metadata[0],\n                        bonusTiers: metadata[1],\n                        tokenDetails: metadata[2],\n                        tokenImageUrl: metadata[3]\n                    }\n                });\n            } catch (error) {\n                console.error('Error loading token info:', error);\n                setError('Failed to load token information');\n            } finally{\n                setLoading(false);\n            }\n        }\n    }[\"useModularToken.useCallback[loadTokenInfo]\"], [\n        provider,\n        tokenAddress\n    ]);\n    // Load contract addresses\n    const loadContractAddresses = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[loadContractAddresses]\": async ()=>{\n            const contractAddress = tokenAddress || SECURITY_TOKEN_CORE_ADDRESS;\n            if (!provider || !contractAddress) return;\n            try {\n                const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(contractAddress, SecurityTokenCoreABI, provider);\n                // Define module IDs (these are keccak256 hashes of the module names)\n                const moduleIds = {\n                    'Identity Manager': ethers__WEBPACK_IMPORTED_MODULE_6__.keccak256(ethers__WEBPACK_IMPORTED_MODULE_7__.toUtf8Bytes(\"IDENTITY_MANAGER\")),\n                    'Compliance Engine': ethers__WEBPACK_IMPORTED_MODULE_6__.keccak256(ethers__WEBPACK_IMPORTED_MODULE_7__.toUtf8Bytes(\"COMPLIANCE_ENGINE\")),\n                    'Transfer Controller': ethers__WEBPACK_IMPORTED_MODULE_6__.keccak256(ethers__WEBPACK_IMPORTED_MODULE_7__.toUtf8Bytes(\"TRANSFER_CONTROLLER\")),\n                    'Agent Manager': ethers__WEBPACK_IMPORTED_MODULE_6__.keccak256(ethers__WEBPACK_IMPORTED_MODULE_7__.toUtf8Bytes(\"AGENT_MANAGER\")),\n                    'Emergency Manager': ethers__WEBPACK_IMPORTED_MODULE_6__.keccak256(ethers__WEBPACK_IMPORTED_MODULE_7__.toUtf8Bytes(\"EMERGENCY_MANAGER\")),\n                    'KYC Claims Module': ethers__WEBPACK_IMPORTED_MODULE_6__.keccak256(ethers__WEBPACK_IMPORTED_MODULE_7__.toUtf8Bytes(\"KYC_CLAIMS_MODULE\"))\n                };\n                console.log('Fetching module addresses...');\n                // Fetch all module addresses\n                const addresses = {\n                    'Token Contract': contractAddress\n                };\n                for (const [moduleName, moduleId] of Object.entries(moduleIds)){\n                    try {\n                        const moduleAddress = await contract.getModule(moduleId);\n                        if (moduleAddress && moduleAddress !== ethers__WEBPACK_IMPORTED_MODULE_8__.ZeroAddress) {\n                            addresses[moduleName] = moduleAddress;\n                            console.log(\"\".concat(moduleName, \": \").concat(moduleAddress));\n                        } else {\n                            console.log(\"\".concat(moduleName, \": Not registered\"));\n                        }\n                    } catch (error) {\n                        console.warn(\"Error fetching \".concat(moduleName, \" address:\"), error);\n                    }\n                }\n                setContractAddresses(addresses);\n            } catch (error) {\n                console.error('Error loading contract addresses:', error);\n            }\n        }\n    }[\"useModularToken.useCallback[loadContractAddresses]\"], [\n        provider,\n        tokenAddress\n    ]);\n    // Load upgrade information\n    const loadUpgradeInfo = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[loadUpgradeInfo]\": async ()=>{\n            if (!provider || !UPGRADE_MANAGER_ADDRESS) return;\n            try {\n                setLoading(true);\n                const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(UPGRADE_MANAGER_ADDRESS, UpgradeManagerABI, provider);\n                const [emergencyModeActive, registeredModules, upgradeDelay, emergencyModeDuration, pendingUpgradeIds] = await Promise.all([\n                    contract.isEmergencyModeActive(),\n                    contract.getRegisteredModules(),\n                    contract.UPGRADE_DELAY(),\n                    contract.EMERGENCY_MODE_DURATION(),\n                    contract.getPendingUpgradeIds()\n                ]);\n                setUpgradeInfo({\n                    emergencyModeActive,\n                    registeredModules,\n                    upgradeDelay: Number(upgradeDelay),\n                    emergencyModeDuration: Number(emergencyModeDuration)\n                });\n                // Load pending upgrades\n                const pendingUpgradesData = await Promise.all(pendingUpgradeIds.map({\n                    \"useModularToken.useCallback[loadUpgradeInfo]\": async (id)=>{\n                        const upgrade = await contract.pendingUpgrades(id);\n                        return {\n                            upgradeId: id,\n                            moduleId: upgrade.moduleId,\n                            proxy: upgrade.proxy,\n                            newImplementation: upgrade.newImplementation,\n                            executeTime: Number(upgrade.executeTime),\n                            executed: upgrade.executed,\n                            cancelled: upgrade.cancelled,\n                            description: upgrade.description\n                        };\n                    }\n                }[\"useModularToken.useCallback[loadUpgradeInfo]\"]));\n                setPendingUpgrades(pendingUpgradesData);\n                // Load upgrade history for SecurityTokenCore\n                const SECURITY_TOKEN_CORE_ID = ethers__WEBPACK_IMPORTED_MODULE_6__.keccak256(ethers__WEBPACK_IMPORTED_MODULE_7__.toUtf8Bytes(\"SECURITY_TOKEN_CORE\"));\n                const history = await contract.getUpgradeHistory(SECURITY_TOKEN_CORE_ID);\n                setUpgradeHistory(history.map({\n                    \"useModularToken.useCallback[loadUpgradeInfo]\": (record)=>({\n                            oldImplementation: record.oldImplementation,\n                            newImplementation: record.newImplementation,\n                            timestamp: Number(record.timestamp),\n                            executor: record.executor,\n                            version: record.version,\n                            isEmergency: record.isEmergency,\n                            description: record.description\n                        })\n                }[\"useModularToken.useCallback[loadUpgradeInfo]\"]));\n            } catch (error) {\n                console.error('Error loading upgrade info:', error);\n                setError('Failed to load upgrade information');\n            } finally{\n                setLoading(false);\n            }\n        }\n    }[\"useModularToken.useCallback[loadUpgradeInfo]\"], [\n        provider\n    ]);\n    // Mint tokens\n    const mintTokens = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[mintTokens]\": async (address, amount)=>{\n            const contractAddress = tokenAddress || SECURITY_TOKEN_CORE_ADDRESS;\n            if (!signer || !contractAddress) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(contractAddress, SecurityTokenCoreABI, signer);\n            try {\n                const decimals = (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.decimals) || 0;\n                const amountWei = ethers__WEBPACK_IMPORTED_MODULE_5__.parseUnits(amount, decimals);\n                const tx = await contract.mint(address, amountWei, {\n                    gasLimit: 400000\n                });\n                return tx.wait();\n            } catch (error) {\n                console.error('Mint tokens error:', error);\n                if (error.code === 'ACTION_REJECTED') {\n                    throw new Error('Transaction was rejected by user');\n                } else if (error.reason) {\n                    throw new Error(\"Contract error: \".concat(error.reason));\n                } else {\n                    throw new Error(\"Failed to mint tokens: \".concat(error.message));\n                }\n            }\n        }\n    }[\"useModularToken.useCallback[mintTokens]\"], [\n        signer,\n        tokenInfo,\n        tokenAddress\n    ]);\n    // Toggle pause state with multiple retry strategies\n    const togglePause = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[togglePause]\": async ()=>{\n            const contractAddress = tokenAddress || SECURITY_TOKEN_CORE_ADDRESS;\n            if (!signer || !contractAddress) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(contractAddress, SecurityTokenCoreABI, signer);\n            const isPausing = !(tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.paused);\n            const functionName = isPausing ? 'pause' : 'unpause';\n            // Try multiple strategies to handle Amoy testnet issues\n            const strategies = [\n                {\n                    \"useModularToken.useCallback[togglePause]\": // Strategy 1: Standard call with high gas\n                    async ()=>{\n                        const tx = isPausing ? await contract.pause({\n                            gasLimit: 500000,\n                            gasPrice: ethers__WEBPACK_IMPORTED_MODULE_5__.parseUnits('30', 'gwei')\n                        }) : await contract.unpause({\n                            gasLimit: 500000,\n                            gasPrice: ethers__WEBPACK_IMPORTED_MODULE_5__.parseUnits('30', 'gwei')\n                        });\n                        return tx.wait();\n                    }\n                }[\"useModularToken.useCallback[togglePause]\"],\n                {\n                    \"useModularToken.useCallback[togglePause]\": // Strategy 2: Raw transaction approach (like the working script)\n                    async ()=>{\n                        const functionSelector = isPausing ? '0x8456cb59' : '0x3f4ba83a';\n                        const nonce = await signer.getNonce();\n                        const tx = await signer.sendTransaction({\n                            to: contractAddress,\n                            data: functionSelector,\n                            gasLimit: 500000,\n                            gasPrice: ethers__WEBPACK_IMPORTED_MODULE_5__.parseUnits('30', 'gwei'),\n                            nonce: nonce\n                        });\n                        return tx.wait();\n                    }\n                }[\"useModularToken.useCallback[togglePause]\"]\n            ];\n            for(let i = 0; i < strategies.length; i++){\n                try {\n                    console.log(\"Attempting \".concat(functionName, \" strategy \").concat(i + 1, \"...\"));\n                    const result = await strategies[i]();\n                    console.log(\"\".concat(functionName, \" successful with strategy \").concat(i + 1));\n                    return result;\n                } catch (error) {\n                    console.error(\"Strategy \".concat(i + 1, \" failed:\"), error);\n                    if (error.code === 'ACTION_REJECTED') {\n                        throw new Error('Transaction was rejected by user');\n                    }\n                    // If this is the last strategy, throw a comprehensive error\n                    if (i === strategies.length - 1) {\n                        var _error_error;\n                        if (error.code === 'UNKNOWN_ERROR' && ((_error_error = error.error) === null || _error_error === void 0 ? void 0 : _error_error.code) === -32603) {\n                            throw new Error(\"Amoy testnet RPC error: The network is experiencing issues. Please try again in a few minutes, or use a different RPC endpoint.\");\n                        } else if (error.reason) {\n                            throw new Error(\"Contract error: \".concat(error.reason));\n                        } else {\n                            throw new Error(\"Failed to \".concat(functionName, \" token after trying multiple methods: \").concat(error.message));\n                        }\n                    }\n                    // Wait a bit before trying the next strategy\n                    await new Promise({\n                        \"useModularToken.useCallback[togglePause]\": (resolve)=>setTimeout(resolve, 1000)\n                    }[\"useModularToken.useCallback[togglePause]\"]);\n                }\n            }\n        }\n    }[\"useModularToken.useCallback[togglePause]\"], [\n        signer,\n        tokenInfo,\n        tokenAddress\n    ]);\n    // Schedule upgrade\n    const scheduleUpgrade = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[scheduleUpgrade]\": async (implementationAddress, description)=>{\n            if (!signer || !UPGRADE_MANAGER_ADDRESS) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(UPGRADE_MANAGER_ADDRESS, UpgradeManagerABI, signer);\n            const SECURITY_TOKEN_CORE_ID = ethers__WEBPACK_IMPORTED_MODULE_6__.keccak256(ethers__WEBPACK_IMPORTED_MODULE_7__.toUtf8Bytes(\"SECURITY_TOKEN_CORE\"));\n            const tx = await contract.scheduleUpgrade(SECURITY_TOKEN_CORE_ID, implementationAddress, description);\n            return tx.wait();\n        }\n    }[\"useModularToken.useCallback[scheduleUpgrade]\"], [\n        signer\n    ]);\n    // Execute upgrade\n    const executeUpgrade = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[executeUpgrade]\": async (upgradeId)=>{\n            if (!signer || !UPGRADE_MANAGER_ADDRESS) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(UPGRADE_MANAGER_ADDRESS, UpgradeManagerABI, signer);\n            const tx = await contract.executeUpgrade(upgradeId);\n            return tx.wait();\n        }\n    }[\"useModularToken.useCallback[executeUpgrade]\"], [\n        signer\n    ]);\n    // Emergency upgrade\n    const emergencyUpgrade = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[emergencyUpgrade]\": async (implementationAddress, description)=>{\n            if (!signer || !UPGRADE_MANAGER_ADDRESS) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(UPGRADE_MANAGER_ADDRESS, UpgradeManagerABI, signer);\n            const SECURITY_TOKEN_CORE_ID = ethers__WEBPACK_IMPORTED_MODULE_6__.keccak256(ethers__WEBPACK_IMPORTED_MODULE_7__.toUtf8Bytes(\"SECURITY_TOKEN_CORE\"));\n            const tx = await contract.emergencyUpgrade(SECURITY_TOKEN_CORE_ID, implementationAddress, description);\n            return tx.wait();\n        }\n    }[\"useModularToken.useCallback[emergencyUpgrade]\"], [\n        signer\n    ]);\n    // Toggle emergency mode\n    const toggleEmergencyMode = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[toggleEmergencyMode]\": async ()=>{\n            if (!signer || !UPGRADE_MANAGER_ADDRESS) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(UPGRADE_MANAGER_ADDRESS, UpgradeManagerABI, signer);\n            const tx = (upgradeInfo === null || upgradeInfo === void 0 ? void 0 : upgradeInfo.emergencyModeActive) ? await contract.deactivateEmergencyMode() : await contract.activateEmergencyMode();\n            return tx.wait();\n        }\n    }[\"useModularToken.useCallback[toggleEmergencyMode]\"], [\n        signer,\n        upgradeInfo\n    ]);\n    // Cancel upgrade\n    const cancelUpgrade = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[cancelUpgrade]\": async (upgradeId)=>{\n            if (!signer || !UPGRADE_MANAGER_ADDRESS) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(UPGRADE_MANAGER_ADDRESS, UpgradeManagerABI, signer);\n            const tx = await contract.cancelUpgrade(upgradeId);\n            return tx.wait();\n        }\n    }[\"useModularToken.useCallback[cancelUpgrade]\"], [\n        signer\n    ]);\n    // Update token price\n    const updateTokenPrice = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[updateTokenPrice]\": async (newPrice)=>{\n            const contractAddress = tokenAddress || SECURITY_TOKEN_CORE_ADDRESS;\n            if (!signer || !contractAddress) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(contractAddress, SecurityTokenCoreABI, signer);\n            try {\n                const tx = await contract.updateTokenPrice(newPrice, {\n                    gasLimit: 300000\n                });\n                return tx.wait();\n            } catch (error) {\n                console.error('Update token price error:', error);\n                if (error.code === 'ACTION_REJECTED') {\n                    throw new Error('Transaction was rejected by user');\n                } else if (error.reason) {\n                    throw new Error(\"Contract error: \".concat(error.reason));\n                } else {\n                    throw new Error(\"Failed to update token price: \".concat(error.message));\n                }\n            }\n        }\n    }[\"useModularToken.useCallback[updateTokenPrice]\"], [\n        signer,\n        tokenAddress\n    ]);\n    // Update bonus tiers\n    const updateBonusTiers = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[updateBonusTiers]\": async (newBonusTiers)=>{\n            const contractAddress = tokenAddress || SECURITY_TOKEN_CORE_ADDRESS;\n            if (!signer || !contractAddress) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(contractAddress, SecurityTokenCoreABI, signer);\n            try {\n                const tx = await contract.updateBonusTiers(newBonusTiers, {\n                    gasLimit: 300000\n                });\n                return tx.wait();\n            } catch (error) {\n                console.error('Update bonus tiers error:', error);\n                if (error.code === 'ACTION_REJECTED') {\n                    throw new Error('Transaction was rejected by user');\n                } else if (error.reason) {\n                    throw new Error(\"Contract error: \".concat(error.reason));\n                } else {\n                    throw new Error(\"Failed to update bonus tiers: \".concat(error.message));\n                }\n            }\n        }\n    }[\"useModularToken.useCallback[updateBonusTiers]\"], [\n        signer,\n        tokenAddress\n    ]);\n    // Update max supply\n    const updateMaxSupply = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[updateMaxSupply]\": async (newMaxSupply)=>{\n            const contractAddress = tokenAddress || SECURITY_TOKEN_CORE_ADDRESS;\n            if (!signer || !contractAddress) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(contractAddress, SecurityTokenCoreABI, signer);\n            try {\n                const maxSupplyWei = ethers__WEBPACK_IMPORTED_MODULE_5__.parseUnits(newMaxSupply, (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.decimals) || 0);\n                const tx = await contract.updateMaxSupply(maxSupplyWei, {\n                    gasLimit: 300000\n                });\n                return tx.wait();\n            } catch (error) {\n                console.error('Update max supply error:', error);\n                if (error.code === 'ACTION_REJECTED') {\n                    throw new Error('Transaction was rejected by user');\n                } else if (error.reason) {\n                    throw new Error(\"Contract error: \".concat(error.reason));\n                } else {\n                    throw new Error(\"Failed to update max supply: \".concat(error.message));\n                }\n            }\n        }\n    }[\"useModularToken.useCallback[updateMaxSupply]\"], [\n        signer,\n        tokenInfo,\n        tokenAddress\n    ]);\n    // Add to whitelist with identity registration\n    const addToWhitelist = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[addToWhitelist]\": async (address)=>{\n            const contractAddress = tokenAddress || SECURITY_TOKEN_CORE_ADDRESS;\n            if (!signer || !contractAddress) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(contractAddress, SecurityTokenCoreABI, signer);\n            try {\n                // First check if the address is already verified/registered\n                const isVerified = await contract.isVerified(address);\n                if (!isVerified) {\n                    // Need to register identity first - use the API approach for this\n                    throw new Error('Address must be registered first. Please use the API method or register the identity manually.');\n                }\n                // Now try to add to whitelist\n                const tx = await contract.addToWhitelist(address, {\n                    gasLimit: 400000,\n                    gasPrice: ethers__WEBPACK_IMPORTED_MODULE_5__.parseUnits('30', 'gwei')\n                });\n                return tx.wait();\n            } catch (error) {\n                console.error('Add to whitelist error:', error);\n                if (error.code === 'ACTION_REJECTED') {\n                    throw new Error('Transaction was rejected by user');\n                } else if (error.reason) {\n                    throw new Error(\"Contract error: \".concat(error.reason));\n                } else if (error.message.includes('identity not registered')) {\n                    throw new Error('Address must be registered first. Please use the API method which handles registration automatically.');\n                } else {\n                    throw new Error(\"Failed to add address to whitelist: \".concat(error.message));\n                }\n            }\n        }\n    }[\"useModularToken.useCallback[addToWhitelist]\"], [\n        signer,\n        tokenAddress\n    ]);\n    // Remove from whitelist\n    const removeFromWhitelist = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[removeFromWhitelist]\": async (address)=>{\n            const contractAddress = tokenAddress || SECURITY_TOKEN_CORE_ADDRESS;\n            if (!signer || !contractAddress) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(contractAddress, SecurityTokenCoreABI, signer);\n            try {\n                const tx = await contract.removeFromWhitelist(address, {\n                    gasLimit: 300000\n                });\n                return tx.wait();\n            } catch (error) {\n                console.error('Remove from whitelist error:', error);\n                if (error.code === 'ACTION_REJECTED') {\n                    throw new Error('Transaction was rejected by user');\n                } else if (error.reason) {\n                    throw new Error(\"Contract error: \".concat(error.reason));\n                } else {\n                    throw new Error(\"Failed to remove address from whitelist: \".concat(error.message));\n                }\n            }\n        }\n    }[\"useModularToken.useCallback[removeFromWhitelist]\"], [\n        signer,\n        tokenAddress\n    ]);\n    // Refresh all data\n    const refreshData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[refreshData]\": async ()=>{\n            await Promise.all([\n                loadTokenInfo(),\n                loadContractAddresses(),\n                loadUpgradeInfo()\n            ]);\n        }\n    }[\"useModularToken.useCallback[refreshData]\"], [\n        loadTokenInfo,\n        loadContractAddresses,\n        loadUpgradeInfo\n    ]);\n    // Initialize on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useModularToken.useEffect\": ()=>{\n            initializeProvider();\n        }\n    }[\"useModularToken.useEffect\"], [\n        initializeProvider\n    ]);\n    // Load data when provider is ready\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useModularToken.useEffect\": ()=>{\n            if (provider && signer) {\n                refreshData();\n            }\n        }\n    }[\"useModularToken.useEffect\"], [\n        provider,\n        signer,\n        refreshData\n    ]);\n    return {\n        // State\n        provider,\n        signer,\n        tokenInfo,\n        contractAddresses,\n        upgradeInfo,\n        pendingUpgrades,\n        upgradeHistory,\n        loading,\n        error,\n        // Actions\n        initializeProvider,\n        loadTokenInfo,\n        loadContractAddresses,\n        loadUpgradeInfo,\n        mintTokens,\n        togglePause,\n        scheduleUpgrade,\n        executeUpgrade,\n        emergencyUpgrade,\n        toggleEmergencyMode,\n        cancelUpgrade,\n        refreshData,\n        // Admin Functions\n        updateTokenPrice,\n        updateBonusTiers,\n        updateMaxSupply,\n        addToWhitelist,\n        removeFromWhitelist,\n        // Utilities\n        setError,\n        setLoading\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useModularToken.ts\n"));

/***/ })

});