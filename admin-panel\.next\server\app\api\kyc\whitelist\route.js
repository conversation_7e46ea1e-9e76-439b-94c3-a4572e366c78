/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/kyc/whitelist/route";
exports.ids = ["app/api/kyc/whitelist/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fkyc%2Fwhitelist%2Froute&page=%2Fapi%2Fkyc%2Fwhitelist%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fkyc%2Fwhitelist%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fkyc%2Fwhitelist%2Froute&page=%2Fapi%2Fkyc%2Fwhitelist%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fkyc%2Fwhitelist%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_github_tokendev_newroo_admin_panel_src_app_api_kyc_whitelist_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/kyc/whitelist/route.ts */ \"(rsc)/./src/app/api/kyc/whitelist/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/kyc/whitelist/route\",\n        pathname: \"/api/kyc/whitelist\",\n        filename: \"route\",\n        bundlePath: \"app/api/kyc/whitelist/route\"\n    },\n    resolvedPagePath: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\api\\\\kyc\\\\whitelist\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_github_tokendev_newroo_admin_panel_src_app_api_kyc_whitelist_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fkyc%2Fwhitelist%2Froute&page=%2Fapi%2Fkyc%2Fwhitelist%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fkyc%2Fwhitelist%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/kyc/whitelist/route.ts":
/*!********************************************!*\
  !*** ./src/app/api/kyc/whitelist/route.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/address/checks.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/providers/provider-jsonrpc.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/wallet/wallet.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/contract/contract.js\");\n\n\nconst SECURITY_TOKEN_CORE_ABI = [\n    \"function addToWhitelist(address account) external\",\n    \"function isWhitelisted(address account) external view returns (bool)\"\n];\nconst KYC_CLAIMS_MODULE_ABI = [\n    \"function addToWhitelist(address token, address user) external\"\n];\nasync function POST(request) {\n    try {\n        const { tokenAddress, userAddress } = await request.json();\n        if (!tokenAddress || !userAddress) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Token address and user address are required'\n            }, {\n                status: 400\n            });\n        }\n        // Validate addresses\n        if (!ethers__WEBPACK_IMPORTED_MODULE_1__.isAddress(tokenAddress) || !ethers__WEBPACK_IMPORTED_MODULE_1__.isAddress(userAddress)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Invalid address format'\n            }, {\n                status: 400\n            });\n        }\n        // Get environment variables\n        const rpcUrl = process.env.AMOY_RPC_URL;\n        const privateKey = process.env.CONTRACT_ADMIN_PRIVATE_KEY;\n        const kycModuleAddress = process.env.AMOY_KYC_CLAIMS_MODULE_ADDRESS;\n        if (!rpcUrl || !privateKey) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Server configuration error'\n            }, {\n                status: 500\n            });\n        }\n        // Setup provider and signer\n        const provider = new ethers__WEBPACK_IMPORTED_MODULE_2__.JsonRpcProvider(rpcUrl);\n        const signer = new ethers__WEBPACK_IMPORTED_MODULE_3__.Wallet(privateKey, provider);\n        let txHash;\n        if (kycModuleAddress && ethers__WEBPACK_IMPORTED_MODULE_1__.isAddress(kycModuleAddress)) {\n            // Use KYC Claims Module if available\n            console.log('Using KYC Claims Module for whitelist');\n            const kycModule = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(kycModuleAddress, KYC_CLAIMS_MODULE_ABI, signer);\n            try {\n                const tx = await kycModule.addToWhitelist(tokenAddress, userAddress);\n                await tx.wait();\n                txHash = tx.hash;\n                console.log('User added to whitelist via KYC Claims Module:', txHash);\n            } catch (moduleError) {\n                console.log('KYC Claims Module failed, falling back to direct token call:', moduleError);\n                // Fallback to direct token call\n                const tokenContract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(tokenAddress, SECURITY_TOKEN_CORE_ABI, signer);\n                const tx = await tokenContract.addToWhitelist(userAddress);\n                await tx.wait();\n                txHash = tx.hash;\n                console.log('User added to whitelist via direct token call:', txHash);\n            }\n        } else {\n            // Direct token call\n            console.log('Using direct token call for whitelist');\n            const tokenContract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(tokenAddress, SECURITY_TOKEN_CORE_ABI, signer);\n            const tx = await tokenContract.addToWhitelist(userAddress);\n            await tx.wait();\n            txHash = tx.hash;\n            console.log('User added to whitelist via direct token call:', txHash);\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'User added to whitelist successfully',\n            txHash,\n            userAddress,\n            tokenAddress\n        });\n    } catch (error) {\n        console.error('Error adding to whitelist:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: `Failed to add to whitelist: ${error.message}`\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/kyc/whitelist/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/ethers","vendor-chunks/@noble","vendor-chunks/@adraffy","vendor-chunks/aes-js"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fkyc%2Fwhitelist%2Froute&page=%2Fapi%2Fkyc%2Fwhitelist%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fkyc%2Fwhitelist%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();