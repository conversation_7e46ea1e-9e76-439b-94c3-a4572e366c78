"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/modular-tokens/[address]/page",{

/***/ "(app-pages-browser)/./src/hooks/useModularToken.ts":
/*!**************************************!*\
  !*** ./src/hooks/useModularToken.ts ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useModularToken: () => (/* binding */ useModularToken)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/providers/provider-browser.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/contract/contract.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/utils/units.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/crypto/keccak.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/utils/utf8.js\");\n/* harmony import */ var _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/contracts/SecurityTokenCore.json */ \"(app-pages-browser)/./src/contracts/SecurityTokenCore.json\");\n/* harmony import */ var _contracts_UpgradeManager_json__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contracts/UpgradeManager.json */ \"(app-pages-browser)/./src/contracts/UpgradeManager.json\");\n/* __next_internal_client_entry_do_not_use__ useModularToken auto */ \n\n// Import ABIs - extract the abi property from the JSON artifacts\n\n\n// Extract ABIs from artifacts\nconst SecurityTokenCoreABI = _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_1__.abi;\nconst UpgradeManagerABI = _contracts_UpgradeManager_json__WEBPACK_IMPORTED_MODULE_2__.abi;\n// Contract addresses from environment\nconst SECURITY_TOKEN_CORE_ADDRESS = \"******************************************\";\nconst UPGRADE_MANAGER_ADDRESS = \"0xb7a53dC340C2E5e823002B174629e2a44B8ed815\";\nfunction useModularToken(tokenAddress) {\n    const [provider, setProvider] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [signer, setSigner] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [tokenInfo, setTokenInfo] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [upgradeInfo, setUpgradeInfo] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [pendingUpgrades, setPendingUpgrades] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [upgradeHistory, setUpgradeHistory] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    // Initialize provider and signer\n    const initializeProvider = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[initializeProvider]\": async ()=>{\n            try {\n                if ( true && window.ethereum) {\n                    const provider = new ethers__WEBPACK_IMPORTED_MODULE_3__.BrowserProvider(window.ethereum);\n                    await provider.send('eth_requestAccounts', []);\n                    const signer = await provider.getSigner();\n                    setProvider(provider);\n                    setSigner(signer);\n                    return {\n                        provider,\n                        signer\n                    };\n                } else {\n                    throw new Error('MetaMask not found');\n                }\n            } catch (error) {\n                console.error('Error initializing provider:', error);\n                setError('Failed to connect to wallet');\n                return null;\n            }\n        }\n    }[\"useModularToken.useCallback[initializeProvider]\"], []);\n    // Load token information\n    const loadTokenInfo = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[loadTokenInfo]\": async ()=>{\n            const contractAddress = tokenAddress || SECURITY_TOKEN_CORE_ADDRESS;\n            if (!provider || !contractAddress) return;\n            try {\n                setLoading(true);\n                const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(contractAddress, SecurityTokenCoreABI, provider);\n                const [name, symbol, version, totalSupply, maxSupply, decimals, paused, metadata] = await Promise.all([\n                    contract.name(),\n                    contract.symbol(),\n                    contract.version(),\n                    contract.totalSupply(),\n                    contract.maxSupply(),\n                    contract.decimals(),\n                    contract.paused(),\n                    contract.getTokenMetadata()\n                ]);\n                setTokenInfo({\n                    name,\n                    symbol,\n                    version,\n                    totalSupply: ethers__WEBPACK_IMPORTED_MODULE_5__.formatUnits(totalSupply, decimals),\n                    maxSupply: ethers__WEBPACK_IMPORTED_MODULE_5__.formatUnits(maxSupply, decimals),\n                    decimals,\n                    paused,\n                    metadata: {\n                        tokenPrice: metadata[0],\n                        bonusTiers: metadata[1],\n                        tokenDetails: metadata[2],\n                        tokenImageUrl: metadata[3]\n                    }\n                });\n            } catch (error) {\n                console.error('Error loading token info:', error);\n                setError('Failed to load token information');\n            } finally{\n                setLoading(false);\n            }\n        }\n    }[\"useModularToken.useCallback[loadTokenInfo]\"], [\n        provider,\n        tokenAddress\n    ]);\n    // Load upgrade information\n    const loadUpgradeInfo = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[loadUpgradeInfo]\": async ()=>{\n            if (!provider || !UPGRADE_MANAGER_ADDRESS) return;\n            try {\n                setLoading(true);\n                const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(UPGRADE_MANAGER_ADDRESS, UpgradeManagerABI, provider);\n                const [emergencyModeActive, registeredModules, upgradeDelay, emergencyModeDuration, pendingUpgradeIds] = await Promise.all([\n                    contract.isEmergencyModeActive(),\n                    contract.getRegisteredModules(),\n                    contract.UPGRADE_DELAY(),\n                    contract.EMERGENCY_MODE_DURATION(),\n                    contract.getPendingUpgradeIds()\n                ]);\n                setUpgradeInfo({\n                    emergencyModeActive,\n                    registeredModules,\n                    upgradeDelay: Number(upgradeDelay),\n                    emergencyModeDuration: Number(emergencyModeDuration)\n                });\n                // Load pending upgrades\n                const pendingUpgradesData = await Promise.all(pendingUpgradeIds.map({\n                    \"useModularToken.useCallback[loadUpgradeInfo]\": async (id)=>{\n                        const upgrade = await contract.pendingUpgrades(id);\n                        return {\n                            upgradeId: id,\n                            moduleId: upgrade.moduleId,\n                            proxy: upgrade.proxy,\n                            newImplementation: upgrade.newImplementation,\n                            executeTime: Number(upgrade.executeTime),\n                            executed: upgrade.executed,\n                            cancelled: upgrade.cancelled,\n                            description: upgrade.description\n                        };\n                    }\n                }[\"useModularToken.useCallback[loadUpgradeInfo]\"]));\n                setPendingUpgrades(pendingUpgradesData);\n                // Load upgrade history for SecurityTokenCore\n                const SECURITY_TOKEN_CORE_ID = ethers__WEBPACK_IMPORTED_MODULE_6__.keccak256(ethers__WEBPACK_IMPORTED_MODULE_7__.toUtf8Bytes(\"SECURITY_TOKEN_CORE\"));\n                const history = await contract.getUpgradeHistory(SECURITY_TOKEN_CORE_ID);\n                setUpgradeHistory(history.map({\n                    \"useModularToken.useCallback[loadUpgradeInfo]\": (record)=>({\n                            oldImplementation: record.oldImplementation,\n                            newImplementation: record.newImplementation,\n                            timestamp: Number(record.timestamp),\n                            executor: record.executor,\n                            version: record.version,\n                            isEmergency: record.isEmergency,\n                            description: record.description\n                        })\n                }[\"useModularToken.useCallback[loadUpgradeInfo]\"]));\n            } catch (error) {\n                console.error('Error loading upgrade info:', error);\n                setError('Failed to load upgrade information');\n            } finally{\n                setLoading(false);\n            }\n        }\n    }[\"useModularToken.useCallback[loadUpgradeInfo]\"], [\n        provider\n    ]);\n    // Mint tokens\n    const mintTokens = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[mintTokens]\": async (address, amount)=>{\n            const contractAddress = tokenAddress || SECURITY_TOKEN_CORE_ADDRESS;\n            if (!signer || !contractAddress) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(contractAddress, SecurityTokenCoreABI, signer);\n            try {\n                const decimals = (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.decimals) || 0;\n                const amountWei = ethers__WEBPACK_IMPORTED_MODULE_5__.parseUnits(amount, decimals);\n                const tx = await contract.mint(address, amountWei, {\n                    gasLimit: 400000\n                });\n                return tx.wait();\n            } catch (error) {\n                console.error('Mint tokens error:', error);\n                if (error.code === 'ACTION_REJECTED') {\n                    throw new Error('Transaction was rejected by user');\n                } else if (error.reason) {\n                    throw new Error(\"Contract error: \".concat(error.reason));\n                } else {\n                    throw new Error(\"Failed to mint tokens: \".concat(error.message));\n                }\n            }\n        }\n    }[\"useModularToken.useCallback[mintTokens]\"], [\n        signer,\n        tokenInfo,\n        tokenAddress\n    ]);\n    // Toggle pause state with multiple retry strategies\n    const togglePause = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[togglePause]\": async ()=>{\n            const contractAddress = tokenAddress || SECURITY_TOKEN_CORE_ADDRESS;\n            if (!signer || !contractAddress) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(contractAddress, SecurityTokenCoreABI, signer);\n            const isPausing = !(tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.paused);\n            const functionName = isPausing ? 'pause' : 'unpause';\n            // Try multiple strategies to handle Amoy testnet issues\n            const strategies = [\n                {\n                    \"useModularToken.useCallback[togglePause]\": // Strategy 1: Standard call with high gas\n                    async ()=>{\n                        const tx = isPausing ? await contract.pause({\n                            gasLimit: 500000,\n                            gasPrice: ethers__WEBPACK_IMPORTED_MODULE_5__.parseUnits('30', 'gwei')\n                        }) : await contract.unpause({\n                            gasLimit: 500000,\n                            gasPrice: ethers__WEBPACK_IMPORTED_MODULE_5__.parseUnits('30', 'gwei')\n                        });\n                        return tx.wait();\n                    }\n                }[\"useModularToken.useCallback[togglePause]\"],\n                {\n                    \"useModularToken.useCallback[togglePause]\": // Strategy 2: Raw transaction approach (like the working script)\n                    async ()=>{\n                        const functionSelector = isPausing ? '0x8456cb59' : '0x3f4ba83a';\n                        const nonce = await signer.getNonce();\n                        const tx = await signer.sendTransaction({\n                            to: contractAddress,\n                            data: functionSelector,\n                            gasLimit: 500000,\n                            gasPrice: ethers__WEBPACK_IMPORTED_MODULE_5__.parseUnits('30', 'gwei'),\n                            nonce: nonce\n                        });\n                        return tx.wait();\n                    }\n                }[\"useModularToken.useCallback[togglePause]\"]\n            ];\n            for(let i = 0; i < strategies.length; i++){\n                try {\n                    console.log(\"Attempting \".concat(functionName, \" strategy \").concat(i + 1, \"...\"));\n                    const result = await strategies[i]();\n                    console.log(\"\".concat(functionName, \" successful with strategy \").concat(i + 1));\n                    return result;\n                } catch (error) {\n                    console.error(\"Strategy \".concat(i + 1, \" failed:\"), error);\n                    if (error.code === 'ACTION_REJECTED') {\n                        throw new Error('Transaction was rejected by user');\n                    }\n                    // If this is the last strategy, throw a comprehensive error\n                    if (i === strategies.length - 1) {\n                        var _error_error;\n                        if (error.code === 'UNKNOWN_ERROR' && ((_error_error = error.error) === null || _error_error === void 0 ? void 0 : _error_error.code) === -32603) {\n                            throw new Error(\"Amoy testnet RPC error: The network is experiencing issues. Please try again in a few minutes, or use a different RPC endpoint.\");\n                        } else if (error.reason) {\n                            throw new Error(\"Contract error: \".concat(error.reason));\n                        } else {\n                            throw new Error(\"Failed to \".concat(functionName, \" token after trying multiple methods: \").concat(error.message));\n                        }\n                    }\n                    // Wait a bit before trying the next strategy\n                    await new Promise({\n                        \"useModularToken.useCallback[togglePause]\": (resolve)=>setTimeout(resolve, 1000)\n                    }[\"useModularToken.useCallback[togglePause]\"]);\n                }\n            }\n        }\n    }[\"useModularToken.useCallback[togglePause]\"], [\n        signer,\n        tokenInfo,\n        tokenAddress\n    ]);\n    // Schedule upgrade\n    const scheduleUpgrade = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[scheduleUpgrade]\": async (implementationAddress, description)=>{\n            if (!signer || !UPGRADE_MANAGER_ADDRESS) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(UPGRADE_MANAGER_ADDRESS, UpgradeManagerABI, signer);\n            const SECURITY_TOKEN_CORE_ID = ethers__WEBPACK_IMPORTED_MODULE_6__.keccak256(ethers__WEBPACK_IMPORTED_MODULE_7__.toUtf8Bytes(\"SECURITY_TOKEN_CORE\"));\n            const tx = await contract.scheduleUpgrade(SECURITY_TOKEN_CORE_ID, implementationAddress, description);\n            return tx.wait();\n        }\n    }[\"useModularToken.useCallback[scheduleUpgrade]\"], [\n        signer\n    ]);\n    // Execute upgrade\n    const executeUpgrade = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[executeUpgrade]\": async (upgradeId)=>{\n            if (!signer || !UPGRADE_MANAGER_ADDRESS) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(UPGRADE_MANAGER_ADDRESS, UpgradeManagerABI, signer);\n            const tx = await contract.executeUpgrade(upgradeId);\n            return tx.wait();\n        }\n    }[\"useModularToken.useCallback[executeUpgrade]\"], [\n        signer\n    ]);\n    // Emergency upgrade\n    const emergencyUpgrade = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[emergencyUpgrade]\": async (implementationAddress, description)=>{\n            if (!signer || !UPGRADE_MANAGER_ADDRESS) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(UPGRADE_MANAGER_ADDRESS, UpgradeManagerABI, signer);\n            const SECURITY_TOKEN_CORE_ID = ethers__WEBPACK_IMPORTED_MODULE_6__.keccak256(ethers__WEBPACK_IMPORTED_MODULE_7__.toUtf8Bytes(\"SECURITY_TOKEN_CORE\"));\n            const tx = await contract.emergencyUpgrade(SECURITY_TOKEN_CORE_ID, implementationAddress, description);\n            return tx.wait();\n        }\n    }[\"useModularToken.useCallback[emergencyUpgrade]\"], [\n        signer\n    ]);\n    // Toggle emergency mode\n    const toggleEmergencyMode = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[toggleEmergencyMode]\": async ()=>{\n            if (!signer || !UPGRADE_MANAGER_ADDRESS) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(UPGRADE_MANAGER_ADDRESS, UpgradeManagerABI, signer);\n            const tx = (upgradeInfo === null || upgradeInfo === void 0 ? void 0 : upgradeInfo.emergencyModeActive) ? await contract.deactivateEmergencyMode() : await contract.activateEmergencyMode();\n            return tx.wait();\n        }\n    }[\"useModularToken.useCallback[toggleEmergencyMode]\"], [\n        signer,\n        upgradeInfo\n    ]);\n    // Cancel upgrade\n    const cancelUpgrade = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[cancelUpgrade]\": async (upgradeId)=>{\n            if (!signer || !UPGRADE_MANAGER_ADDRESS) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(UPGRADE_MANAGER_ADDRESS, UpgradeManagerABI, signer);\n            const tx = await contract.cancelUpgrade(upgradeId);\n            return tx.wait();\n        }\n    }[\"useModularToken.useCallback[cancelUpgrade]\"], [\n        signer\n    ]);\n    // Update token price\n    const updateTokenPrice = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[updateTokenPrice]\": async (newPrice)=>{\n            const contractAddress = tokenAddress || SECURITY_TOKEN_CORE_ADDRESS;\n            if (!signer || !contractAddress) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(contractAddress, SecurityTokenCoreABI, signer);\n            try {\n                const tx = await contract.updateTokenPrice(newPrice, {\n                    gasLimit: 300000\n                });\n                return tx.wait();\n            } catch (error) {\n                console.error('Update token price error:', error);\n                if (error.code === 'ACTION_REJECTED') {\n                    throw new Error('Transaction was rejected by user');\n                } else if (error.reason) {\n                    throw new Error(\"Contract error: \".concat(error.reason));\n                } else {\n                    throw new Error(\"Failed to update token price: \".concat(error.message));\n                }\n            }\n        }\n    }[\"useModularToken.useCallback[updateTokenPrice]\"], [\n        signer\n    ]);\n    // Update bonus tiers\n    const updateBonusTiers = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[updateBonusTiers]\": async (newBonusTiers)=>{\n            if (!signer || !SECURITY_TOKEN_CORE_ADDRESS) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(SECURITY_TOKEN_CORE_ADDRESS, SecurityTokenCoreABI, signer);\n            try {\n                const tx = await contract.updateBonusTiers(newBonusTiers, {\n                    gasLimit: 300000\n                });\n                return tx.wait();\n            } catch (error) {\n                console.error('Update bonus tiers error:', error);\n                if (error.code === 'ACTION_REJECTED') {\n                    throw new Error('Transaction was rejected by user');\n                } else if (error.reason) {\n                    throw new Error(\"Contract error: \".concat(error.reason));\n                } else {\n                    throw new Error(\"Failed to update bonus tiers: \".concat(error.message));\n                }\n            }\n        }\n    }[\"useModularToken.useCallback[updateBonusTiers]\"], [\n        signer\n    ]);\n    // Update max supply\n    const updateMaxSupply = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[updateMaxSupply]\": async (newMaxSupply)=>{\n            if (!signer || !SECURITY_TOKEN_CORE_ADDRESS) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(SECURITY_TOKEN_CORE_ADDRESS, SecurityTokenCoreABI, signer);\n            try {\n                const maxSupplyWei = ethers__WEBPACK_IMPORTED_MODULE_5__.parseUnits(newMaxSupply, (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.decimals) || 0);\n                const tx = await contract.updateMaxSupply(maxSupplyWei, {\n                    gasLimit: 300000\n                });\n                return tx.wait();\n            } catch (error) {\n                console.error('Update max supply error:', error);\n                if (error.code === 'ACTION_REJECTED') {\n                    throw new Error('Transaction was rejected by user');\n                } else if (error.reason) {\n                    throw new Error(\"Contract error: \".concat(error.reason));\n                } else {\n                    throw new Error(\"Failed to update max supply: \".concat(error.message));\n                }\n            }\n        }\n    }[\"useModularToken.useCallback[updateMaxSupply]\"], [\n        signer,\n        tokenInfo\n    ]);\n    // Add to whitelist\n    const addToWhitelist = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[addToWhitelist]\": async (address)=>{\n            if (!signer || !SECURITY_TOKEN_CORE_ADDRESS) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(SECURITY_TOKEN_CORE_ADDRESS, SecurityTokenCoreABI, signer);\n            try {\n                const tx = await contract.addToWhitelist(address, {\n                    gasLimit: 300000\n                });\n                return tx.wait();\n            } catch (error) {\n                console.error('Add to whitelist error:', error);\n                if (error.code === 'ACTION_REJECTED') {\n                    throw new Error('Transaction was rejected by user');\n                } else if (error.reason) {\n                    throw new Error(\"Contract error: \".concat(error.reason));\n                } else {\n                    throw new Error(\"Failed to add address to whitelist: \".concat(error.message));\n                }\n            }\n        }\n    }[\"useModularToken.useCallback[addToWhitelist]\"], [\n        signer\n    ]);\n    // Remove from whitelist\n    const removeFromWhitelist = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[removeFromWhitelist]\": async (address)=>{\n            if (!signer || !SECURITY_TOKEN_CORE_ADDRESS) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(SECURITY_TOKEN_CORE_ADDRESS, SecurityTokenCoreABI, signer);\n            try {\n                const tx = await contract.removeFromWhitelist(address, {\n                    gasLimit: 300000\n                });\n                return tx.wait();\n            } catch (error) {\n                console.error('Remove from whitelist error:', error);\n                if (error.code === 'ACTION_REJECTED') {\n                    throw new Error('Transaction was rejected by user');\n                } else if (error.reason) {\n                    throw new Error(\"Contract error: \".concat(error.reason));\n                } else {\n                    throw new Error(\"Failed to remove address from whitelist: \".concat(error.message));\n                }\n            }\n        }\n    }[\"useModularToken.useCallback[removeFromWhitelist]\"], [\n        signer\n    ]);\n    // Refresh all data\n    const refreshData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[refreshData]\": async ()=>{\n            await Promise.all([\n                loadTokenInfo(),\n                loadUpgradeInfo()\n            ]);\n        }\n    }[\"useModularToken.useCallback[refreshData]\"], [\n        loadTokenInfo,\n        loadUpgradeInfo\n    ]);\n    // Initialize on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useModularToken.useEffect\": ()=>{\n            initializeProvider();\n        }\n    }[\"useModularToken.useEffect\"], [\n        initializeProvider\n    ]);\n    // Load data when provider is ready\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useModularToken.useEffect\": ()=>{\n            if (provider && signer) {\n                refreshData();\n            }\n        }\n    }[\"useModularToken.useEffect\"], [\n        provider,\n        signer,\n        refreshData\n    ]);\n    return {\n        // State\n        provider,\n        signer,\n        tokenInfo,\n        upgradeInfo,\n        pendingUpgrades,\n        upgradeHistory,\n        loading,\n        error,\n        // Actions\n        initializeProvider,\n        loadTokenInfo,\n        loadUpgradeInfo,\n        mintTokens,\n        togglePause,\n        scheduleUpgrade,\n        executeUpgrade,\n        emergencyUpgrade,\n        toggleEmergencyMode,\n        cancelUpgrade,\n        refreshData,\n        // Admin Functions\n        updateTokenPrice,\n        updateBonusTiers,\n        updateMaxSupply,\n        addToWhitelist,\n        removeFromWhitelist,\n        // Utilities\n        setError,\n        setLoading\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useModularToken.ts\n"));

/***/ })

});